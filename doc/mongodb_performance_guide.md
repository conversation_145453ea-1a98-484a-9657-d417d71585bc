# MongoDB 性能监控与优化指南

## 慢查询监控配置

### 设置慢查询日志

```javascript
// 只记录查询操作且执行时间超过2000毫秒的操作
db.setProfilingLevel(1, { 
  filter: {
    $and: [
      { op: { $eq: "query" } },
      { millis: { $gte: 2000 } }
    ]
  }
})
```

### 查看当前配置

```javascript
// 查看当前的 profiling 设置
db.getProfilingStatus()
```

输出示例:
```json
{
  "was": 1,
  "slowms": 3000,
  "sampleRate": 0.5,
  "filter": { "$and": [ [Object], [Object] ] },
  "note": "When a filter expression is set, slowms and sampleRate are not used for profiling and slow-query log lines.",
  "ok": 1
}
```

**注意**: 当设置了过滤器时，`slowms` 和 `sampleRate` 参数将被忽略。

### 查询慢查询日志

```javascript
// 查看最近的慢查询
db.system.profile.find().sort({ ts: -1 }).limit(10).pretty()

// 按执行时间降序排列
db.system.profile.find().sort({ millis: -1 }).limit(5)

// 查看特定集合的慢查询
db.system.profile.find({ ns: "your_database.your_collection" }).sort({ ts: -1 })
```

### 清空慢查询日志

```javascript
// 临时禁用 profiling
db.setProfilingLevel(0)

// 删除现有的 profile 集合
db.system.profile.drop()

// 重新创建 profile 集合
db.createCollection("system.profile", { capped: true, size: 1024 * 1024 })

// 重新启用 profiling
db.setProfilingLevel(1, { 
  filter: {
    $and: [
      { op: { $eq: "query" } },
      { millis: { $gte: 2000 } }
    ]
  }
})
```

## 性能问题分析与优化

### 慢查询案例分析

以下是一个典型的慢查询示例:

```json
{
  "op": "query",
  "ns": "piclumen-community.likes",
  "command": {
    "find": "likes",
    "filter": {
      "targetAcc.userId": 1882519584331333600
    },
    "sort": { "_id": -1 },
    "limit": 30
  },
  "keysExamined": 1995588,
  "docsExamined": 1995588,
  "nreturned": 0,
  "millis": 2447,
  "planSummary": "IXSCAN { _id: 1 }"
}
```

**问题**: 查询使用了 `_id` 索引而非 `targetAcc.userId` 字段的索引，导致扫描了近200万条文档。

### 索引优化方案

```javascript
// 为 targetAcc.userId 创建索引
db.likes.createIndex({ "targetAcc.userId": 1 }, { name: "targetAcc_userId_index" })

// 为常用的排序和过滤组合创建复合索引
db.likes.createIndex({ "targetAcc.userId": 1, "_id": -1 }, { name: "targetAcc_userId_id_desc_index" })
```

### 验证索引效果

```javascript
// 使用 explain 分析查询计划
db.likes.find({ "targetAcc.userId": 1882519584331333600 })
  .sort({ _id: -1 })
  .limit(30)
  .explain("executionStats")
```

### 监控索引使用情况

```javascript
// 查看集合的索引使用情况
db.likes.aggregate([
  { $indexStats: {} }
])
```

## WiredTiger 缓存配置

### 查看当前缓存配置

```javascript
// 查看当前的 WiredTiger 缓存配置
db.serverStatus().wiredTiger.cache

// 查看 MongoDB 实例的内存限制
db.hostInfo().system.memLimitMB
```

### 默认配置规则

- 默认值: `max(256MB, (RAM - 1GB) * 0.5)`
- 例如: 在 4GB RAM 的系统上，缓存大小为 `(4GB - 1GB) * 0.5 = 1.5GB`

### 手动设置缓存大小

在 MongoDB 配置文件中添加:

```yaml
storage:
  wiredTiger:
    engineConfig:
      cacheSizeGB: 30  # 根据系统内存调整
```

## 最佳实践建议

1. **定期分析慢查询**: 使用 `db.system.profile` 集合监控性能问题
2. **创建适当的索引**: 为常用查询模式创建索引，避免全表扫描
3. **避免过度索引**: 每个索引都会增加写入开销和存储空间
4. **定期清理旧数据**: 考虑实施数据归档策略，保持集合大小可控
5. **调整 WiredTiger 缓存**: 根据系统内存和工作负载调整缓存大小
6. **监控系统资源**: 关注 CPU、内存、磁盘 I/O 等系统资源使用情况

通过以上配置和优化措施，可以显著提高 MongoDB 的查询性能和系统稳定性。