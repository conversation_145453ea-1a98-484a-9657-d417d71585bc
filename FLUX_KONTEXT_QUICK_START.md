# Flux Kontext Pro API 快速开始指南

## 1. 配置步骤

### 1.1 更新配置文件

在 `src/main/resources/application.properties` 中更新以下配置：

```properties
# 替换为您的实际API密钥
flux.kontext.api.api-key=YOUR_ACTUAL_BFL_API_KEY

# 替换为您的实际回调URL（如果需要回调）
flux.kontext.api.callback-url=http://your-domain.com/api/flux-kontext/callback/webhook
```

### 1.2 启动项目

```bash
mvn spring-boot:run
```

## 2. 快速测试

### 2.1 访问Swagger文档

打开浏览器访问：
```
http://localhost:48080/swagger-ui/index.html
```

在Swagger中找到 "Flux Kontext Pro API" 分组。

### 2.2 获取认证Token

首先需要登录获取认证token，在请求头中添加：
```
Authorization: Bearer YOUR_TOKEN
```

### 2.3 测试文本生图

**接口**: `POST /api/flux-kontext/text-to-image`

**请求体**:
```json
{
  "prompt": "a beautiful sunset over the ocean",
  "aspectRatio": "16:9",
  "enablePromptCheck": true
}
```

### 2.4 测试图像编辑

**接口**: `POST /api/flux-kontext/image-edit`

**请求体**:
```json
{
  "prompt": "change the car color to red",
  "inputImage": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...",
  "aspectRatio": "1:1",
  "enablePromptCheck": true
}
```

### 2.5 查看任务状态

**接口**: `GET /api/flux-kontext/status/{requestId}`

使用上一步返回的requestId查询任务状态。

## 3. 使用cURL测试

### 3.1 文本生图

```bash
curl -X POST "http://localhost:48080/api/flux-kontext/text-to-image" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "a majestic mountain landscape at sunrise",
    "aspectRatio": "16:9"
  }'
```

### 3.2 图像编辑

```bash
curl -X POST "http://localhost:48080/api/flux-kontext/image-edit" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "make the sky more dramatic with storm clouds",
    "inputImage": "https://example.com/your-image.jpg",
    "aspectRatio": "1:1"
  }'
```

### 3.3 兼容接口测试

```bash
curl -X POST "http://localhost:48080/api/flux-kontext/create" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "a futuristic city skyline",
    "resolution": {
      "width": 1024,
      "height": 1024,
      "batch_size": 1
    },
    "model_id": "flux_kontext"
  }'
```

## 4. 响应示例

### 4.1 成功响应

```json
{
  "status": 2000,
  "message": "success",
  "data": {
    "id": "flux_12345678-1234-1234-1234-123456789abc",
    "pollingUrl": "https://api.bfl.ai/v1/get_result?id=flux_12345678-1234-1234-1234-123456789abc"
  }
}
```

### 4.2 任务状态响应

```json
{
  "status": 2000,
  "message": "success",
  "data": {
    "id": "flux_12345678-1234-1234-1234-123456789abc",
    "status": "Ready",
    "result": {
      "sample": "https://signed-url-to-generated-image.jpg",
      "width": 1024,
      "height": 1024,
      "seed": 42,
      "processingTime": 15.5
    }
  }
}
```

### 4.3 错误响应

```json
{
  "status": 4001,
  "message": "Insufficient permissions",
  "data": null
}
```

## 5. 常见问题

### 5.1 API密钥问题

**问题**: 收到401 Unauthorized错误
**解决**: 检查配置文件中的API密钥是否正确设置

### 5.2 图像格式问题

**问题**: 图像编辑接口返回400错误
**解决**: 确保inputImage是有效的Base64格式或可访问的URL

### 5.3 任务超时

**问题**: 任务长时间处于Processing状态
**解决**: 检查网络连接和API服务状态

### 5.4 并发限制

**问题**: 收到"超出并发任务限制"错误
**解决**: 等待现有任务完成或增加并发限制配置

## 6. 高级功能

### 6.1 自定义宽高比

支持的宽高比范围：3:7 到 7:3

```json
{
  "prompt": "your prompt here",
  "aspectRatio": "21:9"  // 超宽屏
}
```

### 6.2 种子值控制

使用种子值获得可重现的结果：

```json
{
  "prompt": "your prompt here",
  "seed": 12345
}
```

### 6.3 安全容忍度

控制内容审核的严格程度（0-6）：

```json
{
  "prompt": "your prompt here",
  "safetyTolerance": 2
}
```

## 7. 监控和日志

### 7.1 查看日志

```bash
# 查看应用日志
tail -f logs/application.log | grep FluxKontext

# 查看API调用日志
tail -f logs/application.log | grep "Flux Kontext"
```

### 7.2 监控指标

- API调用成功率
- 平均响应时间
- 任务完成率
- 错误类型分布

## 8. 下一步

1. **集成前端**: 将API集成到前端应用中
2. **批量处理**: 实现批量图像处理功能
3. **缓存优化**: 添加结果缓存机制
4. **监控告警**: 设置API调用监控和告警
5. **性能调优**: 根据使用情况调整配置参数

## 9. 技术支持

如果遇到问题，请：
1. 查看详细的错误日志
2. 检查API密钥和网络连接
3. 参考完整的集成文档
4. 联系技术支持团队
