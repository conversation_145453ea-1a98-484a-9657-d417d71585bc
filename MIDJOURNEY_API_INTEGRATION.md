# Midjourney API 集成文档

本文档描述了如何在PicLumen项目中集成TT API的Midjourney API。

## 概述

TT API的Midjourney API提供了完整的Midjourney功能，包括：
- 图像生成（/imagine）
- 图像变化（U1-U4, V1-V4等操作）
- 图像合成（/blend）
- 图像描述（/describe）
- 任务状态查询（/fetch）
- 获取种子值（/seed）
- 区域重绘（/inpaint）
- Prompt效验（/promptCheck）

## 配置

### 1. 配置文件设置

在 `application.properties` 中添加以下配置：

```properties
# Midjourney API配置
midjourney.api.base-url=https://api.ttapi.io
midjourney.api.api-key=YOUR_TT_API_KEY_HERE
midjourney.api.callback-url=http://your-domain.com/api/midjourney/callback/webhook
midjourney.api.default-timeout=300
midjourney.api.max-retries=3
midjourney.api.translation-enabled=true
midjourney.api.default-mode=fast
```

### 2. 获取API密钥

1. 访问 [TT API官网](https://ttapi.io)
2. 注册账户并获取API密钥
3. 将API密钥配置到 `midjourney.api.api-key`

## API接口

### 0. 兼容接口（推荐用于迁移）

**接口**: `POST /api/midjourney/create`

**说明**: 兼容原GenController接口格式，自动转换参数并调用Midjourney API

**请求参数**:
```json
{
  "prompt": "a beautiful landscape",
  "negative_prompt": "blurry, low quality",
  "resolution": {
    "width": 1024,
    "height": 1024,
    "batch_size": 1
  },
  "seed": 12345,
  "cfg": 7.5,
  "gen_mode": "fast",
  "continueCreate": false
}
```

**响应**:
```json
{
  "status": 0,
  "message": "success",
  "data": {
    "promptId": "afa774a3-1aee-5aba-4510-14818d6875e4",
    "taskId": "afa774a3-1aee-5aba-4510-14818d6875e4",
    "markId": "afa774a3-1aee-5aba-4510-14818d6875e4",
    "message": "Task submitted successfully via Midjourney API",
    "provider": "midjourney"
  }
}
```

详细使用说明请参考：[兼容接口使用指南](./MIDJOURNEY_COMPATIBILITY_GUIDE.md)

### 1. 生成图像

**接口**: `POST /api/midjourney/imagine`

**请求参数**:
```json
{
  "prompt": "a cute cat",
  "mode": "fast",
  "enablePromptCheck": true
}
```

**参数说明**:
- `prompt`: 提示词（必填）
- `mode`: 生成模式，可选值：fast/relax/turbo（可选，默认fast）
- `enablePromptCheck`: 是否启用Prompt检查（可选，默认true）

**响应**:
```json
{
  "status": "SUCCESS",
  "message": "",
  "data": {
    "jobId": "afa774a3-1aee-5aba-4510-14818d6875e4"
  }
}
```

### 2. 执行操作

**接口**: `POST /api/midjourney/action`

**请求参数**:
```json
{
  "jobId": "afa774a3-1aee-5aba-4510-14818d6875e4",
  "action": "upsample1"
}
```

支持的操作类型：
- `upsample1`, `upsample2`, `upsample3`, `upsample4` (U1-U4按钮)
- `variation1`, `variation2`, `variation3`, `variation4` (V1-V4按钮)
- `high_variation`, `low_variation` (Vary Strong/Subtle)
- `zoom_out_2`, `zoom_out_1_5` (Zoom Out)
- `reroll` (重新生成)

### 3. 图像合成

**接口**: `POST /api/midjourney/blend`

**请求参数**:
```json
{
  "imageBase64List": [
    "data:image/png;base64,xxx1",
    "data:image/png;base64,xxx2"
  ],
  "dimensions": "SQUARE",
  "mode": "fast"
}
```

**文件上传方式**: `POST /api/midjourney/blend-files`
- 支持直接上传2-5张图片文件

### 4. 图像描述

**接口**: `POST /api/midjourney/describe`

**请求参数**:
```json
{
  "imageBase64": "data:image/png;base64,xxx",
  "mode": "fast"
}
```

**文件上传方式**: `POST /api/midjourney/describe-file`
- 支持直接上传图片文件

### 5. 查询任务状态

**接口**: `GET /api/midjourney/status/{jobId}`

**响应**:
```json
{
  "status": "SUCCESS",
  "jobId": "afa774a3-1aee-5aba-4510-14818d6875e4",
  "message": "success",
  "data": {
    "actions": "imagine",
    "jobId": "afa774a3-1aee-5aba-4510-14818d6875e4",
    "progress": "100",
    "prompt": "a cute cat",
    "discordImage": "https://cdn.discordapp.com/...",
    "cdnImage": "https://cdnb.ttapi.io/...",
    "width": 1024,
    "height": 1024,
    "components": ["upsample1", "upsample2", "upsample3", "upsample4"],
    "images": ["https://cdnb.ttapi.io/..."],
    "quota": 1
  }
}
```

### 6. Prompt效验

**接口**: `POST /api/midjourney/prompt-check`

**请求参数**:
```json
{
  "prompt": "a beautiful landscape"
}
```

**响应（成功）**:
```json
{
  "status": "SUCCESS",
  "message": "操作成功",
  "data": {
    "status": "SUCCESS",
    "message": "success",
    "data": "Prompt verification successful."
  }
}
```

**响应（失败）**:
```json
{
  "status": "SUCCESS",
  "message": "操作成功",
  "data": {
    "status": "FAILED",
    "message": "banned prompt words：sexy",
    "data": null
  }
}
```

## 任务状态

- `PENDING_QUEUE`: 排队中
- `ON_QUEUE`: 执行中
- `SUCCESS`: 成功
- `FAILED`: 失败

## 回调处理

系统支持异步回调处理，当任务完成时会自动：
1. 更新数据库中的任务记录
2. 保存生成的图片信息
3. 通过WebSocket通知前端
4. 更新用户统计信息

## 速度模式

- `fast`: 快速模式，响应时间约60秒内
- `relax`: 闲时模式，响应时间约120秒内
- `turbo`: 极速模式，响应时间约30秒内

## 使用示例

### 1. 基本图像生成

```bash
curl -X POST "http://localhost:48080/api/midjourney/imagine" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "a beautiful landscape with mountains and lake",
    "mode": "fast"
  }'
```

### 2. 图像放大

```bash
curl -X POST "http://localhost:48080/api/midjourney/action" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "jobId": "your-job-id",
    "action": "upsample1"
  }'
```

### 3. 文件上传合成

```bash
curl -X POST "http://localhost:48080/api/midjourney/blend-files" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "files=@image1.jpg" \
  -F "files=@image2.jpg" \
  -F "dimensions=SQUARE" \
  -F "mode=fast"
```

## 注意事项

1. **API密钥安全**: 请妥善保管您的TT API密钥，不要在客户端代码中暴露
2. **回调URL**: 确保回调URL可以被TT API服务器访问
3. **图片大小**: 上传的图片大小不能超过5MB
4. **并发限制**: 根据您的TT API套餐限制并发请求数量
5. **错误处理**: 请妥善处理API调用失败的情况

## 故障排除

### 1. API调用失败
- 检查API密钥是否正确
- 检查网络连接
- 查看日志中的详细错误信息

### 2. 回调未收到
- 检查回调URL是否可访问
- 检查防火墙设置
- 查看TT API的回调日志

### 3. 图片上传失败
- 检查图片格式是否支持（支持JPG、PNG、WebP）
- 检查图片大小是否超过限制
- 检查Base64编码是否正确

## 更多信息

- [TT API官方文档](https://docs-zh.mjapiapp.com/api/midjourney-api)
- [Midjourney官方文档](https://docs.midjourney.com/)
