# PicLumen，后端项目

## 环境

* CentOS7
* JDK8
* Python3.7

## 依赖

### ComfyUI

使用ComfyUI作为生图中间层，调用SD生成图片。ComfyUI端口设置为7860，可在`application.properties`中配置`comfyui.host`字段修改。

### NudeNet

本服务依赖NudeNet做NSFW检测，开源项目地址：[https://github.com/notAI-tech/NudeNet](https://github.com/notAI-tech/NudeNet)

``` shell
pip install --upgrade nudenet
pip install flask
```

本项目将其封装为了一个微服务，放在`tools/lightweight_nudity_detect`目录下。需要在服务器上一直开启此服务，端口号`5000`

```shell
python micro_service.py
```

### ImageMagick

本服务依赖ImageMagick做图像二次处理，需要安装服务，并且安装支持webp的扩展包。

```shell
yum install imagemagick
yum install libwebp libwebp-tools libwebp-devel
```

本项目通过本地shell的方式对其进行调用。

## API文档

项目启动后，`http://localhost:48080/swagger-ui/index.html?configUrl=/v3/api-docs/swagger-config#/`

## 图像生成过程

1. `POST /api/gen/prompt`

开始第一步图像生成，这一步会异步启动基本生图任务，同时产生promptId和图像生成记录后立即返回。

2. `GET /api/gen/history/{promptId}`

基本生图完成后，使用步骤1结果中的promptId，得到任务完成后的基本图filename列表。如果基本生图未完成就调用此接口，会拿不到结果。

3. `POST /api/gen/forge`

使用步骤2结果data中的数组传给forge接口，开始最终图片处理，这一步会拿到基本生图中完成的图像做图片检测，并将最终的图片结果保存到服务器，然后返回结果，更新图像生成记录。

(图像生成结束)

## 图像查看

使用 `GET /api/img/gen-history/list/{pageNum}/{pageSize}` 来查询当前用户的生图记录，得到结果解析后展示
