#!/bin/bash

cd /workspace/build/piclumen/core
git pull origin dev

CUR_TIME=$(date +%Y%m%d%H%M)
sed -i "s/_Build/_Build$CUR_TIME/g" core/src/main/resources/application.properties

/workspace/xiang/spring-boot.sh status piclumen-0.0.1-SNAPSHOT.jar
/workspace/xiang/spring-boot.sh stop piclumen-0.0.1-SNAPSHOT.jar

mvn clean package -Dmaven.test.skip
rm -f /workspace/xiang/piclumen-0.0.1-SNAPSHOT.jar
cp ./target/piclumen-0.0.1-SNAPSHOT.jar /workspace/xiang
nohup java -jar /workspace/xiang/piclumen-0.0.1-SNAPSHOT.jar --spring.profiles.active=test >> /workspace/xiang/nohup-piclumen.out 2>&1 &

git checkout -- src/main/resources/application.properties
