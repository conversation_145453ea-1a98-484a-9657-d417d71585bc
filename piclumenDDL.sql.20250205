#2024-6-13新增DDL
alter table gpt_prompt_file
	add width int null comment '图片结果宽度' after sensitive_message;

alter table gpt_prompt_file
	add height int null comment '图片结果高度' after width;

alter table gpt_prompt_record
	add origin_create varchar(20) null comment 'create : 原始生图   hiresFix : 高清修复
removeBackground : 去背景' after negative_prompt;

=====================================================2024-6-14 同步生产环境=====================================================================

#2024-6-17新增索引
create index create_timestamp_uindex
	on gpt_prompt_file (create_timestamp);

	create unique index login_name_uindex
	on gpt_user (login_name);

create unique index prompt_id_uindex
	on gpt_prompt_record (prompt_id);


=====================================================2024-6-17 同步生产环境=====================================================================

#2024-6-18新增索引
create index create_timestamp_index
	on gpt_prompt_record (create_timestamp);

	alter table gpt_prompt_record modify gen_info JSON null;

	alter table gpt_image_gen_record modify gen_info JSON null;

	alter table gpt_user modify email varchar(100) null;


=====================================================2024-6-20 同步生产环境=====================================================================


alter table gpt_prompt_record
	add mark_id varchar(64) null comment '生成任务的id(java端生成)' after gen_info;

=====================================================2024-7-4 同步生产环境=====================================================================

#2024-7-4新增字段索引

alter table gpt_prompt_file
	add like_nums int default 0 null comment '图片点赞数' after height;

create index like_nums_index
	on gpt_prompt_file (like_nums);

/*更新图片对应的点赞数*/
update  gpt_prompt_file f set like_nums = (select count(id) from gpt_user_like l where l.prompt_id = f.prompt_id and l.file_name = f.file_name)
where id is not null;

=====================================================2024-7-5 同步生产环境=====================================================================

#2024-7-8新增字段索引
alter table gpt_prompt_record
	add send_ws_failure boolean default 0 null comment '0 : 发送成功  1 ： 发送失败' after origin_create;


=====================================================2024-7-10 同步生产环境=====================================================================

#2024-7-10 新增联系我们表
CREATE TABLE `gpt_contacts`
(
    `id`          bigint NOT NULL AUTO_INCREMENT,
    `login_name`  varchar(89) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  DEFAULT NULL COMMENT '用户账号',
    `full_name`   varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '联系名称',
    `reason`      tinyint                                                       DEFAULT NULL COMMENT '原因',
    `message`     text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '内容',
    `email`       varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '联系邮箱',
    `create_by`   varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  DEFAULT '' COMMENT '创建者',
    `create_time` datetime                                                      DEFAULT NULL COMMENT '创建时间',
    `update_by`   varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  DEFAULT '' COMMENT '更新者',
    `update_time` datetime                                                      DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1774452687732445187 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='联系我们';


=====================================================2024-7-12 同步生产环境=====================================================================

====[1:新建表]===================================================================

create table gpt_user_collect
(
    id          bigint NOT NULL AUTO_INCREMENT,
    login_name       varchar(80)                           null,
    prompt_id        varchar(64)                           null,
	  file_name         varchar(255)                         null comment '文件名称',
    thumbnail_name    varchar(255)                         null comment '缩略图名称',
    file_url          varchar(2000)                        null comment '图片路径',
    thumbnail_url     varchar(2500)                        null comment '缩略图路径',
    prompt            varchar(3000)                          null comment '正向提示词',
    negative_prompt   varchar(3000)                          null comment '反向提示词',
	  origin_file_delete      bit      default b'0'          null comment '0:原始图片未被删除 1：原始图片已经被删除',
    create_timestamp datetime default current_timestamp()  null comment '创建日期',
    update_timestamp datetime default current_timestamp()  null comment '更新日期',
    PRIMARY KEY (id) USING BTREE
)  ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户收藏记录表';


	CREATE TABLE `gpt_user_album` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
  `login_name` varchar(80) DEFAULT NULL COMMENT '用户账号',
  `img_name` varchar(255) DEFAULT NULL COMMENT '图片名称',
  `thumb_img_name` varchar(255) DEFAULT NULL COMMENT '缩略图名称',
  `img_url` varchar(2000) DEFAULT NULL COMMENT '图片路径',
  `thumb_img_url` varchar(2000) DEFAULT NULL COMMENT '缩略图路径',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `login_name_index` (`login_name`) USING BTREE
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='用户相册';

CREATE TABLE `kpi_mix` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `record_date` date DEFAULT NULL COMMENT '日期',
  `avg_chart_time` double(15,4) DEFAULT NULL COMMENT '平均任务生图时间',
  `avg_wait` double(15,4) DEFAULT NULL COMMENT '平均等待时长',
  `avg_chart_one_time` double(15,4) DEFAULT NULL COMMENT '1图平均生图时间',
  `avg_chart_two_time` double(15,4) DEFAULT NULL COMMENT '2图平均生图时间',
  `avg_chart_three_time` double(15,4) DEFAULT NULL COMMENT '3图平均生图时间',
  `avg_chart_four_time` double(15,4) DEFAULT NULL COMMENT '4图平均生图时间',
  `avg_per_chart_time` double(15,4) DEFAULT NULL COMMENT '平均单图生成时间',
  `chart_count` bigint(20) DEFAULT NULL COMMENT '生图数量',
  `dau` bigint(20) DEFAULT NULL COMMENT '日活',
  `wau` bigint(20) DEFAULT NULL COMMENT '周活',
  `mau` bigint(20) DEFAULT NULL COMMENT '月活',
  `new_registers` bigint(20) DEFAULT NULL COMMENT '新注册用户量',
  `total_registers` bigint(20) DEFAULT NULL COMMENT '总用户量',
  `avg_charts_per_user` bigint(20) DEFAULT NULL COMMENT '单用户平均生图数量',
  `max_concurrent_chart_tasks` bigint(20) DEFAULT NULL COMMENT '当日最大并发生图任务数',
  `max_charts_per_user` bigint(20) DEFAULT NULL COMMENT '单用户最大生图数量',
  `chart_count_one` int(11) DEFAULT NULL COMMENT '生成单图任务数',
  `chart_count_two` int(11) DEFAULT NULL COMMENT '生成2图任务数',
  `chart_count_three` int(11) DEFAULT NULL COMMENT '生成3图任务数',
  `chart_count_four` int(11) DEFAULT NULL COMMENT '生成4图任务数',
  `text2pic_tasks` int(11) DEFAULT NULL COMMENT '文生图任务数',
  `hiresfix_tasks` int(11) DEFAULT NULL COMMENT '高清修复任务数',
  `removebg_tasks` int(11) DEFAULT NULL COMMENT '去背景任务数',
  `pic2pic_tasks` int(11) DEFAULT NULL COMMENT '图生图任务数',
  `fast_tasks` int(11) DEFAULT NULL COMMENT '快速生图任务数',
  `remix_per_user` int(11) DEFAULT NULL COMMENT '单用户remix点击次数',
  `chart_task_count` bigint(20) DEFAULT NULL COMMENT '生图任务数量',
  `pic2pic_character_ref` bigint(20) DEFAULT NULL COMMENT '图生图character_ref次数',
  `pic2pic_content_ref` bigint(20) DEFAULT NULL COMMENT '图生图content_ref次数',
  `pic2pic_style_ref` bigint(20) DEFAULT NULL COMMENT '图生图style_ref次数',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT current_timestamp() COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `record_date` (`record_date`)
) ENGINE=InnoDB AUTO_INCREMENT=1818154464247537667 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Kpi汇总表';


CREATE TABLE `gpt_explore_file` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `login_name` varchar(80) DEFAULT NULL COMMENT '用户账号',
  `prompt_id` varchar(64) DEFAULT NULL,
  `file_name` varchar(255) DEFAULT NULL COMMENT '文件名称',
  `file_message` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '文件相关信息' CHECK (json_valid(`file_message`)),
  `thumbnail_name` varchar(255) DEFAULT NULL COMMENT '缩略图名称',
  `file_url` varchar(2000) DEFAULT NULL COMMENT '图片路径',
  `thumbnail_url` varchar(2500) DEFAULT NULL COMMENT '缩略图路径',
  `sensitive_message` varchar(20) DEFAULT NULL COMMENT '涉黄：NSFW',
  `width` int(11) DEFAULT NULL COMMENT '图片结果宽度',
  `height` int(11) DEFAULT NULL COMMENT '图片结果高度',
  `like_nums` int(11) DEFAULT 0 COMMENT '图片点赞数',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `create_by` varchar(80) DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(80) DEFAULT NULL COMMENT '修改者',
  `del` bit(1) DEFAULT b'0' COMMENT '0:未删除 1：已删除',
  PRIMARY KEY (`id`),
  KEY `create_timestamp_uindex` (`create_time`),
  KEY `like_nums_index` (`like_nums`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='explore页随机展示表';


====[2:统一标准时间]===================================================================

ALTER TABLE gpt_contacts
MODIFY create_time DATETIME DEFAULT CURRENT_TIMESTAMP;

alter table gpt_action_log modify create_time datetime null comment '创建时间';

alter table gpt_action_log modify update_time datetime null comment '修改时间';

alter table gpt_action_log
	add create_by varchar(80) null comment '创建者';

alter table gpt_action_log
	add update_by varchar(80) null comment '修改者';


alter table gpt_divide_commission_record change create_datetime create_time datetime null comment '创建时间';

alter table gpt_divide_commission_record
	add update_time datetime null comment '修改时间' after create_time;

alter table gpt_divide_commission_record
	add create_by varchar(80) null comment '创建者' after update_time;

alter table gpt_divide_commission_record
	add update_by varchar(80) null comment '修改者' after create_by;


	alter table gpt_event_log
	add create_by varchar(80) null comment '创建者';

alter table gpt_event_log
	add update_by varchar(80) null comment '修改者';

	alter table gpt_prompt_file change create_timestamp create_time datetime null comment '创建时间';

alter table gpt_prompt_file change update_timestamp update_time datetime null comment '修改时间';

alter table gpt_prompt_file
	add create_by varchar(80) null comment '创建者' after update_time;

alter table gpt_prompt_file
	add update_by varchar(80) null comment '修改者' after create_by;

	alter table gpt_prompt_record change create_timestamp create_time datetime null comment '创建时间';

alter table gpt_prompt_record change update_timestamp update_time datetime null comment '修改时间';

alter table gpt_prompt_record
	add create_by varchar(80) null comment '创建者' after update_time;

alter table gpt_prompt_record
	add update_by varchar(80) null comment '修改者' after create_by;

alter table gpt_user modify create_by varchar(80) collate utf8mb4_bin default '' null comment '创建者';

alter table gpt_user modify update_by varchar(80) collate utf8mb4_bin default '' null comment '更新者';

alter table gpt_user_collect change create_timestamp create_time datetime null comment '创建时间';

alter table gpt_user_collect change update_timestamp update_time datetime null comment '修改时间';

alter table gpt_user_collect
	add create_by varchar(80) null comment '创建者';

alter table gpt_user_collect
	add update_by varchar(80) null comment '修改者';

alter table gpt_user_like change create_timestamp create_time datetime null comment '创建时间';

alter table gpt_user_like change update_timestamp update_time datetime null comment '修改时间';

alter table gpt_user_like
	add create_by varchar(80) null comment '创建者';

alter table gpt_user_like
	add update_by varchar(80) null comment '修改者';


====[3:更新任务表新增字段]===================================================================

alter table gpt_user
	add album_img_num bigint default 0 null comment '用户相册已经上传了图片数量';

ALTER TABLE `piclumen`.`gpt_prompt_record`
ADD COLUMN `gen_mode` varchar(20) NULL COMMENT '生图模式' AFTER `gen_info`;

ALTER TABLE `piclumen`.`gpt_prompt_record`
ADD COLUMN `gen_start_time` datetime NULL COMMENT '生图开始时间' AFTER `send_ws_failure`,
ADD COLUMN `gen_end_time` datetime NULL COMMENT '生图结束时间' AFTER `gen_start_time`,
ADD COLUMN `batch_size` int NULL COMMENT '生图数量' AFTER `gen_end_time`,
ADD COLUMN `model_id` varchar(64) NULL COMMENT '模型id' AFTER `batch_size`;

ALTER TABLE `piclumen`.`gpt_prompt_record`
ADD COLUMN `aspect_ratio` varchar(100) NULL COMMENT '宽高比' AFTER `batch_size`;

====[4:更新任务表新增字段中的数据]===================================================================

UPDATE gpt_prompt_record
SET batch_size = 1
WHERE origin_create not in  ('create','picCreate');

UPDATE gpt_prompt_record
SET batch_size = JSON_EXTRACT(gen_info, '$.resolution.batch_size')
WHERE origin_create = 'create';

UPDATE gpt_prompt_record
SET model_id = JSON_EXTRACT(gen_info, '$.model_id') where id is not null;

UPDATE gpt_prompt_record
SET aspect_ratio = CONCAT(JSON_EXTRACT(gen_info, '$.resolution.width'), ' * ',JSON_EXTRACT(gen_info, '$.resolution.height'))
 where id is not null

UPDATE gpt_prompt_record r
INNER JOIN (
  SELECT prompt_id, MAX(create_time) AS max_create_time
  FROM gpt_prompt_file
  GROUP BY prompt_id
) f ON r.prompt_id = f.prompt_id
SET r.gen_end_time = f.max_create_time
WHERE r.id IS NOT NULL;


====[5:将原始表中运维的图片迁移到 图片展示表]===================================================================

INSERT INTO gpt_explore_file (
    login_name,
    prompt_id,
    file_name,
    file_message,
    thumbnail_name,
    file_url,
    thumbnail_url,
    sensitive_message,
    width,
    height,
    like_nums,
    create_time,
    update_time,
    create_by,
    update_by,
    del
)
SELECT
    login_name,
    prompt_id,
    file_name,
    file_message,
    thumbnail_name,
    file_url,
    thumbnail_url,
    sensitive_message,
    width,
    height,
    like_nums,
    create_time,
    update_time,
    create_by,
    update_by,
    del
FROM gpt_prompt_file
WHERE del = false and login_name IN (
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>'
);


====[6:新增表中的索引字段]===================================================================

create index login_name_index
	on gpt_user_like (login_name);

create index login_name_index
	on gpt_prompt_file (login_name);

create index prompt_id_index
	on gpt_prompt_file (prompt_id);

create index prompt_id_index
	on gpt_explore_file (prompt_id);




=====================================================2024-7-30 同步生产环境=====================================================================


ALTER TABLE `piclumen`.`kpi_mix`
ADD COLUMN `favorite_aspect_ratio` varchar(100) NULL COMMENT '最喜爱生图比例' AFTER `pic2pic_style_ref`,
ADD COLUMN `realistic_count` bigint NULL COMMENT 'realistic生图数量' AFTER `favorite_aspect_ratio`,
ADD COLUMN `anime_count` bigint NULL COMMENT 'anime生图数量' AFTER `realistic_count`,
ADD COLUMN `lineart_count` bigint NULL COMMENT 'lineart生图数量' AFTER `anime_count`;

create index login_name_index
	on gpt_prompt_record (login_name);


CREATE TABLE `gpt_ops_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '用户邮箱',
  `phone_number` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '手机号码',
  `user_name` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '用户昵称',
  `ops_flag` tinyint(4) DEFAULT NULL COMMENT '是否为运营账号',
  `send_ops_info` tinyint(4) DEFAULT NULL COMMENT '是否发送运营数据邮件',
  `create_by` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='运营信息表';

INSERT INTO piclumen.gpt_ops_info (email, phone_number, user_name, ops_flag, send_ops_info, create_time) VALUES ('<EMAIL>', '', '李想', 0, 1, '2024-08-05 10:25:35');
INSERT INTO piclumen.gpt_ops_info (email, phone_number, user_name, ops_flag, send_ops_info, create_time) VALUES ('<EMAIL>', '', '吕逢春', 0, 1, '2024-08-05 10:26:14');
INSERT INTO piclumen.gpt_ops_info (email, phone_number, user_name, ops_flag, send_ops_info, create_time) VALUES ('<EMAIL>', '', '王怡', 0, 1, '2024-08-05 10:26:57');
INSERT INTO piclumen.gpt_ops_info (email, phone_number, user_name, ops_flag, send_ops_info, create_time) VALUES ('<EMAIL>', '', '张家僖', 0, 1, '2024-08-05 10:27:38');
INSERT INTO piclumen.gpt_ops_info (email, phone_number, user_name, ops_flag, send_ops_info, create_time) VALUES ('<EMAIL>', '', '朱瑶洁', 1, 1, '2024-08-05 10:28:32');
INSERT INTO piclumen.gpt_ops_info (email, phone_number, user_name, ops_flag, send_ops_info, create_time) VALUES ('<EMAIL>', '', '邓奴', 0, 1, '2024-08-05 10:29:02');
INSERT INTO piclumen.gpt_ops_info (email, phone_number, user_name, ops_flag, send_ops_info, create_time) VALUES ('<EMAIL>', '', '蔡寿均', 0, 1, '2024-08-05 10:29:30');
INSERT INTO piclumen.gpt_ops_info (email, phone_number, user_name, ops_flag, send_ops_info, create_time) VALUES ('<EMAIL>', '', '何森林', 0, 1, '2024-08-05 10:29:59');
INSERT INTO piclumen.gpt_ops_info (email, phone_number, user_name, ops_flag, send_ops_info, create_time) VALUES ('<EMAIL>', '', '褚琼霞', 1, 1, '2024-08-05 10:31:18');
INSERT INTO piclumen.gpt_ops_info (email, phone_number, user_name, ops_flag, send_ops_info, create_time) VALUES ('<EMAIL>', '', '宾勇超', 0, 1, '2024-08-05 10:31:44');
INSERT INTO piclumen.gpt_ops_info (email, phone_number, user_name, ops_flag, send_ops_info, create_time) VALUES ('<EMAIL>', '', '李林泽', 0, 1, '2024-08-05 10:32:11');
INSERT INTO piclumen.gpt_ops_info (email, phone_number, user_name, ops_flag, send_ops_info, create_time) VALUES ('<EMAIL>', '', '倪施雨', 0, 1, '2024-08-05 10:32:37');
INSERT INTO piclumen.gpt_ops_info (email, phone_number, user_name, ops_flag, send_ops_info, create_time) VALUES ('<EMAIL>', '', '王籽涵', 0, 1, '2024-08-05 10:34:08');
INSERT INTO piclumen.gpt_ops_info (email, phone_number, user_name, ops_flag, send_ops_info, create_time) VALUES ('<EMAIL>', '', '肖锐', 0, 1, '2024-08-05 10:34:30');
INSERT INTO piclumen.gpt_ops_info (email, phone_number, user_name, ops_flag, send_ops_info, create_time) VALUES ('<EMAIL>', '', '李思源', 0, 1, '2024-08-05 10:35:00');
INSERT INTO piclumen.gpt_ops_info (email, phone_number, user_name, ops_flag, send_ops_info, create_time) VALUES ('<EMAIL>', '', '喻涛', 0, 1, '2024-08-05 10:35:29');
INSERT INTO piclumen.gpt_ops_info (email, phone_number, user_name, ops_flag, send_ops_info, create_time) VALUES ('<EMAIL>', '', '刘纪恩', 0, 1, '2024-08-05 10:35:54');
INSERT INTO piclumen.gpt_ops_info (email, phone_number, user_name, ops_flag, send_ops_info, create_time) VALUES ('<EMAIL>', '', '封锦', 0, 1, '2024-08-05 10:36:29');
INSERT INTO piclumen.gpt_ops_info (email, phone_number, user_name, ops_flag, send_ops_info, create_time) VALUES ('<EMAIL>', '', '李业', 0, 1, '2024-08-05 10:36:53');
INSERT INTO piclumen.gpt_ops_info (email, phone_number, user_name, ops_flag, send_ops_info, create_time) VALUES ('<EMAIL>', '', '帅雨洁', 0, 1, '2024-08-05 10:37:21');
INSERT INTO piclumen.gpt_ops_info (email, phone_number, user_name, ops_flag, send_ops_info, create_time) VALUES ('<EMAIL>', '', '邓詹', 0, 1, '2024-08-05 10:38:03');
INSERT INTO piclumen.gpt_ops_info (email, phone_number, user_name, ops_flag, send_ops_info, create_time) VALUES ('<EMAIL>', '', '谢军', 0, 1, '2024-08-05 10:39:42');
INSERT INTO piclumen.gpt_ops_info (email, phone_number, user_name, ops_flag, send_ops_info, create_time) VALUES ('<EMAIL>', '', '万建华', 0, 1, '2024-08-05 10:40:13');
INSERT INTO piclumen.gpt_ops_info (email, phone_number, user_name, ops_flag, send_ops_info, create_time) VALUES ('<EMAIL>', '', '石艳', 0, 1, '2024-08-05 10:40:45');
INSERT INTO piclumen.gpt_ops_info (email, phone_number, user_name, ops_flag, send_ops_info, create_time) VALUES ('<EMAIL>', '', '王成林', 0, 1, '2024-08-05 10:41:09');



=====================================================2024-8-05 同步生产环境=====================================================================

alter table gpt_event_log modify content varchar(4000) null comment '日志内容';

=====================================================2024-8-13 同步生产环境=====================================================================

create index create_time_index
	on gpt_event_log (create_time);

alter table gpt_event_log
	add operate_ip varchar(32) null comment '用户操作ip' after content;

alter table gpt_event_log
	add ip_country varchar(60) null comment 'ip所属国家' after operate_ip;

=====================================================2024-8-15 同步生产环境=====================================================================

alter table gpt_prompt_record
	add english_prompt JSON null comment '翻译成英文提示词' after prompt;

alter table gpt_prompt_record
	add feature_name varchar(20) null comment '功能类型' after model_id;

alter table gpt_prompt_record
	add prompt_params JSON null comment '调用py入参' after english_prompt;

alter table gpt_user
	add visibility varchar(20) null comment '图片公开还是私有(img_public : 公有 img_private ： 私有)' after daily_count;

update 	gpt_user set visibility = 'img_private' where id is not null;

alter table gpt_user
	add before_today_img_num int null comment '今天之前用户生成图片数' after visibility;

alter table gpt_user alter column before_today_img_num set default 0;

UPDATE gpt_user u
     LEFT JOIN (
     SELECT f.login_name, COUNT(1) AS img_count
        FROM gpt_prompt_file f
        GROUP BY f.login_name
    ) AS subquery ON u.login_name = subquery.login_name
    SET u.total_img_num = COALESCE(subquery.img_count, u.total_img_num),
    u.update_time = NOW();


alter table gpt_prompt_record modify prompt varchar(4000) null comment '正向提示词';

alter table gpt_user_collect modify prompt varchar(4000) null comment '正向提示词';

alter table gpt_user_collect modify negative_prompt varchar(4000) null comment '反向提示词';



create table gpt_lb_features
(
    id             bigint auto_increment comment 'id'
        primary key,
    feature_name   varchar(20)            null comment '功能名称',
    feature_group  varchar(32)            null comment '功能组',
    mark           varchar(20)            null comment '标识',
    feature_status tinyint                null comment '状态（0正常 1异常）',
    create_by      varchar(32) default '' null comment '创建者',
    create_time    datetime               null comment '创建时间',
    update_by      varchar(32) default '' null comment '更新者',
    update_time    datetime               null comment '更新时间'
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='功能列表';


-- auto-generated definition
create table gpt_lb_features_rules
(
    id            bigint auto_increment comment 'id'
        primary key,
    model_group   varchar(32)            not null comment '模型组',
    model_mark    varchar(50)            null comment '模组组标识',
    feature_group varchar(32)            null comment '功能组',
    feature_mark  varchar(200)           null comment '模型组标识',
    instance      varchar(2000)          null comment '实例集(多个英文逗号分隔)',
    create_by     varchar(32) default '' null comment '创建者',
    create_time   datetime               null comment '创建时间',
    update_by     varchar(32) default '' null comment '更新者',
    update_time   datetime               null comment '更新时间',
    rule_status   tinyint                null comment '规则状态',
    constraint idx_unique_feature_model_group
        unique (feature_group, model_group)
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='分配规则';


-- auto-generated definition
create table gpt_lb_instance
(
    id          bigint auto_increment comment 'id'
        primary key,
    ip          varchar(39)            null comment '服务器ip',
    address     varchar(50)            null comment '地址',
    instance    varchar(50)            null comment '实例标识',
    mark        varchar(50)            null comment '标识',
    status      tinyint                null comment '状态（0上架 1下架）',
    create_by   varchar(32) default '' null comment '创建者',
    create_time datetime               null comment '创建时间',
    update_by   varchar(32) default '' null comment '更新者',
    update_time datetime               null comment '更新时间'
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='服务器实例列表';


-- auto-generated definition
create table gpt_lb_model
(
    id             bigint auto_increment comment 'id'
        primary key,
    model_name     varchar(60)            null comment '模型名称',
    model_group    varchar(32)            null comment '模型组',
    mark           varchar(20)            null comment '标识',
    model_instance varchar(200)           null comment '模型实例id',
    model_status   tinyint                null comment '状态（0正常 1异常）',
    model_id       varchar(64)            null comment 'gpt对应模型id',
    model_avatar   varchar(2000)          null comment '模型头像',
    create_by      varchar(32) default '' null comment '创建者',
    create_time    datetime               null comment '创建时间',
    update_by      varchar(32) default '' null comment '更新者',
    update_time    datetime               null comment '更新时间'
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='模型列表';


-- auto-generated definition
create table gpt_lb_special_rules
(
    id            bigint auto_increment comment 'id'
        primary key,
    action        varchar(20)            null comment '规则动作',
    feature_group varchar(32)            null comment '功能组',
    model_group   varchar(32)            not null comment '模型组',
    mark          varchar(20)            null comment '标识',
    rule_status   tinyint                null comment '规则状态',
    create_by     varchar(32) default '' null comment '创建者',
    create_time   datetime               null comment '创建时间',
    update_by     varchar(32) default '' null comment '更新者',
    update_time   datetime               null comment '更新时间'
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='特殊规则';


=====================================================2024-8-30 同步生产环境=====================================================================


alter table gpt_user change before_today_img_num total_img_num int default 0 null comment '用户生成图片总数';


=====================================================2024-9-10 同步生产环境=====================================================================

alter table gpt_user add  clear_prompt tinyint(1) null comment '是否清除提示词';


alter table gpt_prompt_record
	add failure_message varchar(3000) null comment '任务失败信息' after feature_name;

alter table gpt_user
    add feedback_email varchar(80) null comment '用户反馈邮箱';

create table gpt_user_report
(
    id            bigint auto_increment
        primary key,
    login_name    varchar(80)           null comment '用户账号',
    prompt_id     varchar(64)           null,
    file_name     varchar(255)          null comment '文件名称',
    audit_state   smallint(1) default 0 null comment '审核状态（0未审核 1已审核）',
    audit_type    smallint(1)           null comment '审核类型（1.Violence  2. Pornography 3.Racial discrimination  4 Copyright infringement  5 Other）',
    other_content varchar(2000)         null comment '其他内容',
    is_removed    smallint(1) default 0 null comment '是否下架（0不下架 1下架）',
    create_time   datetime              null comment '创建时间',
    update_time   datetime              null comment '修改时间',
    create_by     varchar(80)           null comment '创建者',
    update_by     varchar(80)           null comment '修改者'
)
    comment '用户举报记录表' row_format = COMPACT;

create index login_name_index
    on gpt_user_report (login_name);

ALTER TABLE `piclumen`.`gpt_user`
    ADD COLUMN `local_lang` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '本地语言' AFTER `feedback_email`;

ALTER TABLE `piclumen`.`gpt_prompt_record`
DROP INDEX `login_name_index`,
ADD INDEX `login_name_index`(`login_name` ASC, `del` ASC, `create_time` ASC) USING BTREE;


==============用于验证修改语句的正确性(修改数据前,备份gpt_prompt_record表)===================
SELECT r.*
FROM piclumen.gpt_prompt_record r
LEFT JOIN piclumen.gpt_prompt_file f ON r.prompt_id = f.prompt_id
WHERE r.del = false
  AND f.id IS NULL;

update piclumen.gpt_prompt_record r
left join piclumen.gpt_prompt_file f on r.prompt_id = f.prompt_id
set r.del = true
where r.del = false and f.id is null;

=============================
create table gpt_version_control_ios
(
    id             bigint auto_increment
        primary key,
    version        varchar(20)           null comment '版本号',
    is_current     smallint(1) default 0 null comment '是否在线版本（0否 1是）',
    upgrade_type   smallint(1) default 0 null comment '升级类型（1强制更新 2强提示更新 3弱提示更新）',
    upgrade_prompt varchar(200)          null comment '升级提示语',
    create_time    datetime              null comment '创建时间',
    update_time    datetime              null comment '修改时间',
    create_by      varchar(80)           null comment '创建者',
    update_by      varchar(80)           null comment '修改者'
)
    comment 'ios端版本管理' row_format = COMPACT;

create table gpt_version_control_web
(
    id          bigint auto_increment
        primary key,
    version     varchar(20)           null comment '版本号',
    is_current  smallint(1) default 0 null comment '是否当前版本（0否 1是）',
    create_time datetime              null comment '创建时间',
    update_time datetime              null comment '修改时间',
    create_by   varchar(80)           null comment '创建者',
    update_by   varchar(80)           null comment '修改者'
)
    comment 'web端版本管理' row_format = COMPACT;

=====================================================2024-9-20 同步生产环境=====================================================================


alter table gpt_explore_file
	add high_thumbnail_name varchar(255) null comment '高清缩略图名称' after thumbnail_name;

alter table gpt_explore_file
	add high_thumbnail_url varchar(2500) null comment '高清缩略图路径' after thumbnail_url;


alter table gpt_prompt_file
	add high_thumbnail_name varchar(255) null comment '高清缩略图名称' after thumbnail_name;

alter table gpt_prompt_file
	add high_thumbnail_url varchar(2500) null comment '高清缩略图路径' after thumbnail_url;

create index login_name_index
	on gpt_prompt_file (login_name);

--关闭mysql中的ONLY_FULL_GROUP_BY模式
[mysqld]
sql_mode=STRICT_TRANS_TABLES,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION


alter table gpt_explore_file
	add gen_info JSON null comment '图片入参' after thumbnail_url;

alter table gpt_explore_file
	add origin_create varchar(20) null comment '生图类型' after gen_info ;

alter table gpt_explore_file
	add prompt varchar(4000) null comment '正向提示词';


UPDATE gpt_explore_file f
INNER JOIN gpt_prompt_record r ON f.prompt_id = r.prompt_id
SET
    f.gen_info = r.gen_info,
    f.origin_create = r.origin_create,
    f.prompt = r.prompt
WHERE
    f.id is not null;

select  * from gpt_explore_file where gen_info is null;

delete  gpt_explore_file where gen_info is null;

ALTER TABLE `piclumen`.`gpt_prompt_file`  DROP COLUMN `file_message`;

ALTER TABLE `piclumen`.`gpt_explore_file`  DROP COLUMN `file_message`;

ALTER TABLE gpt_user
ADD black_list_flag BOOLEAN DEFAULT 0 COMMENT '用户是否进入了黑名单' AFTER total_img_num;

alter table gpt_prompt_record
	add fast_hour BOOLEAN default FALSE null comment '是否属于fastHour机制任务' after failure_message;

create table gpt_public_file_review
(
    id                bigint auto_increment
        primary key,
    login_name        varchar(80)      null comment '用户账号',
    prompt_id         varchar(64)      null,
    file_name         varchar(255)     null comment '文件名称',
    review_status     varchar(20)      null comment '状态(review(审核中),pass(通过),rejection(拒绝))',
    is_display        int default 0    null comment '是否展示0否1是',
    rejection_type    varchar(50)      null comment '拒绝类型（Violence(暴力) ，Pornography（色情）， Racial discrimination(种族歧视) ， Copyright infringement (版权)， Other（其他））',
    rejection_content varchar(2000)    null comment '拒绝内容描述',
    file_url            varchar(2000)                null comment '图片路径',
    thumbnail_url       varchar(2500)                null comment '缩略图路径',
    high_thumbnail_url  varchar(2500)                null comment '高清缩略图路径',    create_time       datetime         null comment '创建时间',
    update_time       datetime         null comment '修改时间',
    create_by         varchar(80)      null comment '创建者',
    update_by         varchar(80)      null comment '修改者',
    del               bit default b'0' null comment '0:未删除 1：已删除'
)
    comment '用户图片公开审核表';

create index create_timestamp_uindex
    on gpt_public_file_review (create_time);

create index login_name_index
    on gpt_public_file_review (login_name);

create index prompt_id_index
    on gpt_public_file_review (prompt_id);

alter table gpt_contacts
    add thumbnail_urls varchar(3000) null comment '压缩图路径集(逗号分隔)' after email;

alter table gpt_explore_file
    add create_type int  default  0  null  comment  '创建来源(0运营1用户)'  after like_nums;


alter table gpt_lb_instance
    add removed_shelves_type int(2) default 0 null comment '下架类型(0手动下架1监控下架)';

alter table gpt_user
    alter column black_list_flag set default 0;

alter table gpt_prompt_file
    add is_public bit default b'0' null comment '是否公开(0:未公开 1:已公开)' after like_nums;

alter table gpt_lb_instance
    add down_type char(2) null comment '下架类型(1手动下架 0 默认值 -1监控下架)' after status;

alter table gpt_prompt_file
    add collect_nums int(10) default 0 comment '收藏数量' after like_nums;

alter table gpt_prompt_file add origin_create varchar(20) default null comment '来源类型' after del;


update gpt_lb_instance set down_type = '1' where status = 1;

update gpt_lb_instance set down_type = '0' where status = 0;

create table gpt_custom_file
(
    id                 bigint(20) primary key,
    login_name         varchar(80)      null comment '用户账号',
    prompt_id          varchar(64)      null comment 'prompt ID',
    uid                varchar(20)      null comment '',
    user_id            bigint           null comment '用户Id',
    img_url            varchar(2000)    null comment '图片路径',
    thumbnail_url      varchar(2500)    null comment '缩略图路径',
    high_thumbnail_url varchar(2500)    null comment '高清缩略图路径',
    width              int              null comment '图片结果宽度',
    height             int              null comment '图片结果高度',
    create_time        datetime         null comment '创建时间',
    update_time        datetime         null comment '修改时间',
    create_by          varchar(80)      null comment '创建者',
    update_by          varchar(80)      null comment '修改者',
    del                bit default b'0' null comment '0:未删除 1：已删除'
)  comment '用户上传记录';

create index create_timestamp_uindex
    on gpt_custom_file (create_time);

create index prompt_id_index
    on gpt_custom_file (prompt_id);

=====================================================2024-10-18 同步生产环境=====================================================================

create table gpt_specified_country
(
    id           bigint                                        not null comment '国家id'
        primary key,
    country      varchar(100) collate utf8mb4_bin default ''   null comment '国家名称（英文）',
    country_name varchar(100) collate utf8mb4_bin default ''   null comment '国家名称（中文名称）',
    gdp          varchar(60) collate utf8mb4_bin  default ''   null comment '人均GDP',
    del          bit                              default b'0' null comment '0:未删除 1：已删除',
    create_by    varchar(80) collate utf8mb4_bin  default ''   null comment '创建者',
    create_time  datetime                                      null comment '创建时间',
    update_by    varchar(80) collate utf8mb4_bin  default ''   null comment '更新者',
    update_time  datetime                                      null comment '更新时间'
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='特殊国家配置表';

ALTER TABLE `piclumen`.`gpt_user`
    ADD COLUMN `regist_country` varchar(80) NULL COMMENT '注册用户所在国家' AFTER `local_lang`,
ADD COLUMN `contact_black_list_flag` tinyint(1) NULL DEFAULT 0 COMMENT '反馈黑名单标识' AFTER `regist_country`;


alter table kpi_mix
    add fast_task_avg_time double(15, 4) null comment 'Fast任务，任务平均时间' after lineart_count;

alter table kpi_mix
    add fast_one_pic_time double(15, 4) null comment 'Fast任务，单图生成耗时' after lineart_count;

alter table kpi_mix
    add relax_task_avg_time double(15, 4) null comment 'relax任务，任务平均时间' after lineart_count;

alter table kpi_mix
    add relax_one_pic_time double(15, 4) null comment 'Relax任务，单图生成耗时' after lineart_count;

alter table kpi_mix
    add chart_success_task_count bigint null comment '当日成功完成任务数量' after lineart_count;

alter table kpi_mix
    add chart_success_task_rate double(6,2) null comment '任务执行成功率' after lineart_count;

alter table kpi_mix
    add fast_chart_task_rate bigint null comment '当日Fast任务数量' after lineart_count;

alter table kpi_mix
    add relax_chart_task_rate bigint null comment '当日Relax任务数量' after lineart_count;

alter table kpi_mix
    add custom_upload_tasks int null comment '用户上传任务数' after lineart_count;

alter table kpi_mix
    add enlarge_image_tasks int null comment '阔图任务数' after lineart_count;

alter table kpi_mix
    add line_recolor_tasks int null comment '线稿上色任务数' after lineart_count;

alter table kpi_mix
    add local_redraw_tasks int null comment '局部重绘任务数' after lineart_count;

alter table kpi_mix
    add vary_tasks int null comment '图片渐变任务数' after lineart_count;

alter table kpi_mix
    add flux_count bigint null comment 'flux生图数量' after lineart_count;

alter table kpi_mix
    add pony_count bigint null comment 'pony生图数量' after lineart_count;


=====================================================2024-11-04 同步生产环境=====================================================================

alter table gpt_user_collect
    add mini_thumbnail_url varchar(500) default 0 comment '小图预览地址';

alter table gpt_public_file_review
    add real_height int(5) default 0 comment '高';
alter table gpt_public_file_review
    add real_width int(5) default 0 comment '宽';

alter table gpt_user
	add introduction varchar(2000) null comment '自我介绍';


create table gpt_user_collect_classify_0
(
    id              bigint    not null  primary key,
    login_name         varchar(80)      null,
    collect_name          varchar(100)     null comment '收藏夹名称',
    description                varchar(200)     null comment '描述',
    collect_nums        int default 0    null comment '收藏数量',
    user_id          bigint      null comment '用户id',
    size            bigint(20) default 0 comment '容量bytes',
    del             bit        default b'0'      null comment '0 ： 未删除  1 ：已删除',
    create_time        datetime         null comment '创建时间',
    update_time        datetime         null comment '修改时间',
    create_by          varchar(80)      null comment '创建者',
    update_by          varchar(80)      null comment '修改者'
)
    comment '用户收藏记录分类表';

create index create_timestamp_uindex
    on gpt_user_collect_classify_0(create_time);

create index login_name_index
    on gpt_user_collect_classify_0(login_name);

create index user_id_index
    on gpt_user_collect_classify_0(user_id);


CREATE TABLE gpt_user_collect_classify_1 LIKE   gpt_user_collect_classify_0;
CREATE TABLE gpt_user_collect_classify_2 LIKE   gpt_user_collect_classify_0;
CREATE TABLE gpt_user_collect_classify_3 LIKE   gpt_user_collect_classify_0;
CREATE TABLE gpt_user_collect_classify_4 LIKE   gpt_user_collect_classify_0;
CREATE TABLE gpt_user_collect_classify_5 LIKE   gpt_user_collect_classify_0;
CREATE TABLE gpt_user_collect_classify_6 LIKE   gpt_user_collect_classify_0;
CREATE TABLE gpt_user_collect_classify_7 LIKE   gpt_user_collect_classify_0;
CREATE TABLE gpt_user_collect_classify_8 LIKE   gpt_user_collect_classify_0;
CREATE TABLE gpt_user_collect_classify_9 LIKE   gpt_user_collect_classify_0;
CREATE TABLE gpt_user_collect_classify_10 LIKE  gpt_user_collect_classify_0;
CREATE TABLE gpt_user_collect_classify_11 LIKE  gpt_user_collect_classify_0;
CREATE TABLE gpt_user_collect_classify_12 LIKE  gpt_user_collect_classify_0;
CREATE TABLE gpt_user_collect_classify_13 LIKE  gpt_user_collect_classify_0;
CREATE TABLE gpt_user_collect_classify_14 LIKE  gpt_user_collect_classify_0;
CREATE TABLE gpt_user_collect_classify_15 LIKE  gpt_user_collect_classify_0;
CREATE TABLE gpt_user_collect_classify_16 LIKE  gpt_user_collect_classify_0;
CREATE TABLE gpt_user_collect_classify_17 LIKE  gpt_user_collect_classify_0;
CREATE TABLE gpt_user_collect_classify_18 LIKE  gpt_user_collect_classify_0;
CREATE TABLE gpt_user_collect_classify_19 LIKE  gpt_user_collect_classify_0;




-- auto-generated definition

create table gpt_user_collect_0
(
    id                  bigint                       not null
        primary key,
    login_name          varchar(80)                  null comment '用户账号',
    prompt_id           varchar(64)                  null,
    user_id             bigint                       null comment '用户id',
    classify_id         bigint                       not null comment '收藏夹id',
    file_name           varchar(255)                 null comment '文件名称',
    thumbnail_name      varchar(255)                 null comment '缩略图名称',
    high_thumbnail_name varchar(255)                 null comment '高清缩略图名称',
    file_url            varchar(2000)                null comment '图片路径',
    thumbnail_url       varchar(2500)                null comment '缩略图路径',
    gen_info            longtext collate utf8mb4_bin null comment '图片入参',
    origin_create       varchar(20)                  null comment '生图类型',
    high_thumbnail_url  varchar(2500)                null comment '高清缩略图路径',
    mini_thumbnail_url  varchar(255)                 null,
    sensitive_message   varchar(20)                  null comment '涉黄：NSFW',
    width               int                          null comment '图片结果宽度',
    height              int                          null comment '图片结果高度',
    prompt              varchar(4000)                null comment '正向提示词',
    file_id             bigint                       null comment '文件id',
    size                bigint(20)              default 0 comment '容量bytes',
    del                 bit default b'0'             null comment '0:未删除 1：已删除',
    create_time         datetime                     null comment '创建时间',
    update_time         datetime                     null comment '修改时间',
    create_by           varchar(80)                  null comment '创建者',
    update_by           varchar(80)                  null comment '修改者',
    check (json_valid(`gen_info`))
)
    comment '图片收藏表';

create index classify_id_index
    on gpt_user_collect_0 (classify_id);

create index create_timestamp_uindex
    on gpt_user_collect_0 (create_time);

create index prompt_id_index
    on gpt_user_collect_0 (prompt_id);


CREATE TABLE gpt_user_collect_1 LIKE   gpt_user_collect_0;
CREATE TABLE gpt_user_collect_2 LIKE   gpt_user_collect_0;
CREATE TABLE gpt_user_collect_3 LIKE   gpt_user_collect_0;
CREATE TABLE gpt_user_collect_4 LIKE   gpt_user_collect_0;
CREATE TABLE gpt_user_collect_5 LIKE   gpt_user_collect_0;
CREATE TABLE gpt_user_collect_6 LIKE   gpt_user_collect_0;
CREATE TABLE gpt_user_collect_7 LIKE   gpt_user_collect_0;
CREATE TABLE gpt_user_collect_8 LIKE   gpt_user_collect_0;
CREATE TABLE gpt_user_collect_9 LIKE   gpt_user_collect_0;
CREATE TABLE gpt_user_collect_10 LIKE  gpt_user_collect_0;
CREATE TABLE gpt_user_collect_11 LIKE  gpt_user_collect_0;
CREATE TABLE gpt_user_collect_12 LIKE  gpt_user_collect_0;
CREATE TABLE gpt_user_collect_13 LIKE  gpt_user_collect_0;
CREATE TABLE gpt_user_collect_14 LIKE  gpt_user_collect_0;
CREATE TABLE gpt_user_collect_15 LIKE  gpt_user_collect_0;
CREATE TABLE gpt_user_collect_16 LIKE  gpt_user_collect_0;
CREATE TABLE gpt_user_collect_17 LIKE  gpt_user_collect_0;
CREATE TABLE gpt_user_collect_18 LIKE  gpt_user_collect_0;
CREATE TABLE gpt_user_collect_19 LIKE  gpt_user_collect_0;



alter table gpt_public_file_review
    add gen_info longtext collate utf8mb4_bin null comment '生图信息' after high_thumbnail_url;

alter table gpt_public_file_review
    add prompt varchar(4000) null comment '正向提示词' after high_thumbnail_url;

alter table gpt_public_file_review
    add file_id varchar(30) null comment '图片id' after high_thumbnail_url;

-- 修改每个表的 is_public 字段
ALTER TABLE gpt_prompt_file_0
    MODIFY COLUMN is_public INT DEFAULT 0 NULL COMMENT '是否公开(0:未公开 1:已公开 2:审核中 3:已拒绝)';

ALTER TABLE gpt_prompt_file_1
    MODIFY COLUMN is_public INT DEFAULT 0 NULL COMMENT '是否公开(0:未公开 1:已公开 2:审核中 3:已拒绝)';

ALTER TABLE gpt_prompt_file_2
    MODIFY COLUMN is_public INT DEFAULT 0 NULL COMMENT '是否公开(0:未公开 1:已公开 2:审核中 3:已拒绝)';

ALTER TABLE gpt_prompt_file_3
    MODIFY COLUMN is_public INT DEFAULT 0 NULL COMMENT '是否公开(0:未公开 1:已公开 2:审核中 3:已拒绝)';

ALTER TABLE gpt_prompt_file_4
    MODIFY COLUMN is_public INT DEFAULT 0 NULL COMMENT '是否公开(0:未公开 1:已公开 2:审核中 3:已拒绝)';

ALTER TABLE gpt_prompt_file_5
    MODIFY COLUMN is_public INT DEFAULT 0 NULL COMMENT '是否公开(0:未公开 1:已公开 2:审核中 3:已拒绝)';

ALTER TABLE gpt_prompt_file_6
    MODIFY COLUMN is_public INT DEFAULT 0 NULL COMMENT '是否公开(0:未公开 1:已公开 2:审核中 3:已拒绝)';

ALTER TABLE gpt_prompt_file_7
    MODIFY COLUMN is_public INT DEFAULT 0 NULL COMMENT '是否公开(0:未公开 1:已公开 2:审核中 3:已拒绝)';

ALTER TABLE gpt_prompt_file_8
    MODIFY COLUMN is_public INT DEFAULT 0 NULL COMMENT '是否公开(0:未公开 1:已公开 2:审核中 3:已拒绝)';

ALTER TABLE gpt_prompt_file_9
    MODIFY COLUMN is_public INT DEFAULT 0 NULL COMMENT '是否公开(0:未公开 1:已公开 2:审核中 3:已拒绝)';

ALTER TABLE gpt_prompt_file_10
    MODIFY COLUMN is_public INT DEFAULT 0 NULL COMMENT '是否公开(0:未公开 1:已公开 2:审核中 3:已拒绝)';

ALTER TABLE gpt_prompt_file_11
    MODIFY COLUMN is_public INT DEFAULT 0 NULL COMMENT '是否公开(0:未公开 1:已公开 2:审核中 3:已拒绝)';

ALTER TABLE gpt_prompt_file_12
    MODIFY COLUMN is_public INT DEFAULT 0 NULL COMMENT '是否公开(0:未公开 1:已公开 2:审核中 3:已拒绝)';

ALTER TABLE gpt_prompt_file_13
    MODIFY COLUMN is_public INT DEFAULT 0 NULL COMMENT '是否公开(0:未公开 1:已公开 2:审核中 3:已拒绝)';

ALTER TABLE gpt_prompt_file_14
    MODIFY COLUMN is_public INT DEFAULT 0 NULL COMMENT '是否公开(0:未公开 1:已公开 2:审核中 3:已拒绝)';

ALTER TABLE gpt_prompt_file_15
    MODIFY COLUMN is_public INT DEFAULT 0 NULL COMMENT '是否公开(0:未公开 1:已公开 2:审核中 3:已拒绝)';

ALTER TABLE gpt_prompt_file_16
    MODIFY COLUMN is_public INT DEFAULT 0 NULL COMMENT '是否公开(0:未公开 1:已公开 2:审核中 3:已拒绝)';

ALTER TABLE gpt_prompt_file_17
    MODIFY COLUMN is_public INT DEFAULT 0 NULL COMMENT '是否公开(0:未公开 1:已公开 2:审核中 3:已拒绝)';

ALTER TABLE gpt_prompt_file_18
    MODIFY COLUMN is_public INT DEFAULT 0 NULL COMMENT '是否公开(0:未公开 1:已公开 2:审核中 3:已拒绝)';

ALTER TABLE gpt_prompt_file_19
    MODIFY COLUMN is_public INT DEFAULT 0 NULL COMMENT '是否公开(0:未公开 1:已公开 2:审核中 3:已拒绝)';

-- 手动写出 0-19 表的  拒绝内容描述  SQL
ALTER TABLE gpt_prompt_file_0
    ADD rejection_content VARCHAR(2000) NULL COMMENT '拒绝内容描述' AFTER is_public;
ALTER TABLE gpt_prompt_file_1
    ADD rejection_content VARCHAR(2000) NULL COMMENT '拒绝内容描述' AFTER is_public;
ALTER TABLE gpt_prompt_file_2
    ADD rejection_content VARCHAR(2000) NULL COMMENT '拒绝内容描述' AFTER is_public;
ALTER TABLE gpt_prompt_file_3
    ADD rejection_content VARCHAR(2000) NULL COMMENT '拒绝内容描述' AFTER is_public;
ALTER TABLE gpt_prompt_file_4
    ADD rejection_content VARCHAR(2000) NULL COMMENT '拒绝内容描述' AFTER is_public;
ALTER TABLE gpt_prompt_file_5
    ADD rejection_content VARCHAR(2000) NULL COMMENT '拒绝内容描述' AFTER is_public;
ALTER TABLE gpt_prompt_file_6
    ADD rejection_content VARCHAR(2000) NULL COMMENT '拒绝内容描述' AFTER is_public;
ALTER TABLE gpt_prompt_file_7
    ADD rejection_content VARCHAR(2000) NULL COMMENT '拒绝内容描述' AFTER is_public;
ALTER TABLE gpt_prompt_file_8
    ADD rejection_content VARCHAR(2000) NULL COMMENT '拒绝内容描述' AFTER is_public;
ALTER TABLE gpt_prompt_file_9
    ADD rejection_content VARCHAR(2000) NULL COMMENT '拒绝内容描述' AFTER is_public;
ALTER TABLE gpt_prompt_file_10
    ADD rejection_content VARCHAR(2000) NULL COMMENT '拒绝内容描述' AFTER is_public;
ALTER TABLE gpt_prompt_file_11
    ADD rejection_content VARCHAR(2000) NULL COMMENT '拒绝内容描述' AFTER is_public;
ALTER TABLE gpt_prompt_file_12
    ADD rejection_content VARCHAR(2000) NULL COMMENT '拒绝内容描述' AFTER is_public;
ALTER TABLE gpt_prompt_file_13
    ADD rejection_content VARCHAR(2000) NULL COMMENT '拒绝内容描述' AFTER is_public;
ALTER TABLE gpt_prompt_file_14
    ADD rejection_content VARCHAR(2000) NULL COMMENT '拒绝内容描述' AFTER is_public;
ALTER TABLE gpt_prompt_file_15
    ADD rejection_content VARCHAR(2000) NULL COMMENT '拒绝内容描述' AFTER is_public;
ALTER TABLE gpt_prompt_file_16
    ADD rejection_content VARCHAR(2000) NULL COMMENT '拒绝内容描述' AFTER is_public;
ALTER TABLE gpt_prompt_file_17
    ADD rejection_content VARCHAR(2000) NULL COMMENT '拒绝内容描述' AFTER is_public;
ALTER TABLE gpt_prompt_file_18
    ADD rejection_content VARCHAR(2000) NULL COMMENT '拒绝内容描述' AFTER is_public;
ALTER TABLE gpt_prompt_file_19
    ADD rejection_content VARCHAR(2000) NULL COMMENT '拒绝内容描述' AFTER is_public;




alter table gpt_public_file_review
    add public_type varchar(15) null comment '公开类型：everyone ： 所有人可见  myself ： 自己可见  fullLikes : 满足20点赞后可见' after high_thumbnail_url;


create table gpt_version_control_android
(
    id             bigint auto_increment
        primary key,
    version        varchar(20)        null comment '版本号',
    is_current     smallint default 0 null comment '是否在线版本（0否 1是）',
    upgrade_type   smallint default 0 null comment '升级类型（1强制更新 2强提示更新 3弱提示更新）',
    create_time    datetime           null comment '创建时间',
    update_time    datetime           null comment '修改时间',
    create_by      varchar(80)        null comment '创建者',
    update_by      varchar(80)        null comment '修改者'
)
    comment '安卓端版本管理' row_format = COMPACT;

ALTER TABLE gpt_public_file_review
    ADD COLUMN brief VARCHAR(2500) NULL COMMENT '简要';

----mongodb创建索引---
use piclumen-community

# 检查集合是否存在,如果不存在则创建集合
if (!db.getCollectionNames().includes("user_activity")) {
    db.createCollection("user_activity");
}
# 创建联合唯一索引
db.user_activity.createIndex(
    { "userId": 1, "date": 1 },
    { unique: true, name: "user_date_unique" }
);

# 创建 TTL 索引, 使记录在35天后自动过期
db.user_activity.createIndex(
    { "date": 1 },
    { expireAfterSeconds: 35 * 24 * 60 * 60 }
);

alter table gpt_prompt_file_0 add column `size` bigint(20) default null comment '容量bytes' after `is_public`;
alter table gpt_prompt_file_0 add column `mini_thumbnail_url` varchar(500) default null comment 'mini url' after `high_thumbnail_name`;

alter table gpt_prompt_file_1 add column `size` bigint(20) default null comment '容量bytes' after `is_public`;
alter table gpt_prompt_file_1 add column `mini_thumbnail_url` varchar(500) default null comment 'mini url' after `high_thumbnail_name`;

alter table gpt_prompt_file_2 add column `size` bigint(20) default null comment '容量bytes' after `is_public`;
alter table gpt_prompt_file_2 add column `mini_thumbnail_url` varchar(500) default null comment 'mini url' after `high_thumbnail_name`;

alter table gpt_prompt_file_3 add column `size` bigint(20) default null comment '容量bytes' after `is_public`;
alter table gpt_prompt_file_3 add column `mini_thumbnail_url` varchar(500) default null comment 'mini url' after `high_thumbnail_name`;

alter table gpt_prompt_file_4 add column `size` bigint(20) default null comment '容量bytes' after `is_public`;
alter table gpt_prompt_file_4 add column `mini_thumbnail_url` varchar(500) default null comment 'mini url' after `high_thumbnail_name`;

alter table gpt_prompt_file_5 add column `size` bigint(20) default null comment '容量bytes' after `is_public`;
alter table gpt_prompt_file_5 add column `mini_thumbnail_url` varchar(500) default null comment 'mini url' after `high_thumbnail_name`;

alter table gpt_prompt_file_6 add column `size` bigint(20) default null comment '容量bytes' after `is_public`;
alter table gpt_prompt_file_6 add column `mini_thumbnail_url` varchar(500) default null comment 'mini url' after `high_thumbnail_name`;

alter table gpt_prompt_file_7 add column `size` bigint(20) default null comment '容量bytes' after `is_public`;
alter table gpt_prompt_file_7 add column `mini_thumbnail_url` varchar(500) default null comment 'mini url' after `high_thumbnail_name`;

alter table gpt_prompt_file_8 add column `size` bigint(20) default null comment '容量bytes' after `is_public`;
alter table gpt_prompt_file_8 add column `mini_thumbnail_url` varchar(500) default null comment 'mini url' after `high_thumbnail_name`;

alter table gpt_prompt_file_9 add column `size` bigint(20) default null comment '容量bytes' after `is_public`;
alter table gpt_prompt_file_9 add column `mini_thumbnail_url` varchar(500) default null comment 'mini url' after `high_thumbnail_name`;

alter table gpt_prompt_file_10 add column `size` bigint(20) default null comment '容量bytes' after `is_public`;
alter table gpt_prompt_file_10 add column `mini_thumbnail_url` varchar(500) default null comment 'mini url' after `high_thumbnail_name`;

alter table gpt_prompt_file_11 add column `size` bigint(20) default null comment '容量bytes' after `is_public`;
alter table gpt_prompt_file_11 add column `mini_thumbnail_url` varchar(500) default null comment 'mini url' after `high_thumbnail_name`;

alter table gpt_prompt_file_12 add column `size` bigint(20) default null comment '容量bytes' after `is_public`;
alter table gpt_prompt_file_12 add column `mini_thumbnail_url` varchar(500) default null comment 'mini url' after `high_thumbnail_name`;

alter table gpt_prompt_file_13 add column `size` bigint(20) default null comment '容量bytes' after `is_public`;
alter table gpt_prompt_file_13 add column `mini_thumbnail_url` varchar(500) default null comment 'mini url' after `high_thumbnail_name`;

alter table gpt_prompt_file_14 add column `size` bigint(20) default null comment '容量bytes' after `is_public`;
alter table gpt_prompt_file_14 add column `mini_thumbnail_url` varchar(500) default null comment 'mini url' after `high_thumbnail_name`;

alter table gpt_prompt_file_15 add column `size` bigint(20) default null comment '容量bytes' after `is_public`;
alter table gpt_prompt_file_15 add column `mini_thumbnail_url` varchar(500) default null comment 'mini url' after `high_thumbnail_name`;

alter table gpt_prompt_file_16 add column `size` bigint(20) default null comment '容量bytes' after `is_public`;
alter table gpt_prompt_file_16 add column `mini_thumbnail_url` varchar(500) default null comment 'mini url' after `high_thumbnail_name`;

alter table gpt_prompt_file_17 add column `size` bigint(20) default null comment '容量bytes' after `is_public`;
alter table gpt_prompt_file_17 add column `mini_thumbnail_url` varchar(500) default null comment 'mini url' after `high_thumbnail_name`;

alter table gpt_prompt_file_18 add column `size` bigint(20) default null comment '容量bytes' after `is_public`;
alter table gpt_prompt_file_18 add column `mini_thumbnail_url` varchar(500) default null comment 'mini url' after `high_thumbnail_name`;

alter table gpt_prompt_file_19 add column `size` bigint(20) default null comment '容量bytes' after `is_public`;
alter table gpt_prompt_file_19 add column `mini_thumbnail_url` varchar(500) default null comment 'mini url' after `high_thumbnail_name`;


alter table gpt_user add column `used_size` bigint(20) default 0 comment '收藏夹使用大小bytes url';
alter table gpt_user add column `total_size` bigint(20) default 524288000 comment '收藏夹总容量bytes';


=====================================================2024-11-28 同步生产环境=====================================================================


-----------------------------------------files----------------------------------------------
if (!db.getCollectionNames().includes("files")) {
    db.createCollection("files");
}
-----Popular部分-------
db.files.createIndex({ fileLikeNums: -1, _id: -1}, { name: "fileLikeNums_id_desc_index" });
db.files.createIndex({ createTime: -1, fileLikeNums: -1, _id: -1}, { name: "createTime_fileLikeNums_id_desc_index" });
//去掉该索引db.files.createIndex({ tags: 1, fileLikeNums: -1, _id: -1}, { name: "tags_fileLikeNums_id_desc_index" });
//去掉该索引db.files.createIndex({ tags: 1, createTime: -1, fileLikeNums: -1, _id: -1}, { name: "tags_createTime_fileLikeNums_id_desc_index" });
db.files.createIndex({ prompt: "text" },{ default_language: "english", name: "prompt_text_index" } );


db.files.createIndex({ featured: 1, tags: 1, fileLikeNums: -1, _id: -1}, { name: "featured_tags_fileLikeNums_id_desc_index" });
db.files.createIndex({ featured: 1, fileLikeNums: -1, _id: -1}, { name: "featured_fileLikeNums_id_desc_index" });

-----last部分-------
db.files.createIndex({ tags: 1, _id: -1 }, { name: "tags_id_desc_index" });

----PersonalCommImg部分-------
db.files.createIndex({ "accountInfo.userId": 1, _id: -1 }, { name: "accountInfo.userId_id_desc_index" });
db.files.createIndex({ "accountInfo.userId": 1, tags:1, _id: -1 }, { name: "accountInfo.userId_tags_id_desc_index" });


-----------------------------------------likes-------------------------------------
if (!db.getCollectionNames().includes("likes")) {
    db.createCollection("likes");
}
db.likes.createIndex({ "ownerAcc.userId": 1, _id: -1 }, { name: "ownerAcc.userId_id_desc_index" });
db.likes.createIndex({ "ownerAcc.userId": 1, tags:1, _id: -1 }, { name: "ownerAcc.userId_tags_id_desc_index" });

db.likes.createIndex({ "ownerAcc.userId": 1, fileId: -1 }, { name: "ownerAcc.userId_fileId_desc_index" });
db.likes.createIndex({ "ownerAcc.userId": 1, commentId: -1 }, { name: "ownerAcc.userId_commentId_desc_index" });

db.likes.createIndex({ "fileId": 1, commentId: -1, _id: -1 }, { name: "fileId_commentId_id_desc_index" });
db.likes.createIndex({ "fileId": 1, _id: -1 }, { name: "fileId_id_desc_index" });

db.likes.createIndex({ prompt: "text" },{ default_language: "english", name: "likes_prompt_text_index" });

----------------------------------------comments-----------------------------------
if (!db.getCollectionNames().includes("comments")) {
    db.createCollection("comments");
}
db.comments.createIndex({ fileId: 1, _id: -1 }, { name: "fileId_id_desc_index" });
db.comments.createIndex({ fileId: 1, firstCommentId:1, _id: -1 }, { name: "fileId_firstCommentId_id_desc_index" });


--------------------------------comment_reports-----------------------------------
if (!db.getCollectionNames().includes("comment_reports")) {
    db.createCollection("comment_reports");
}
db.comment_reports.createIndex({ "ownerAcc.userId": 1, commentId:1 }, { name: "ownerAcc.userId_commentId_index" });


---------------------------------img_reports-------------------------------------
if (!db.getCollectionNames().includes("img_reports")) {
    db.createCollection("img_reports");
}
db.img_reports.createIndex({ "ownerAcc.userId": 1, fileId: -1 }, { name: "ownerAcc.userId_fileId_desc_index" });


--------------------------------follows-------------------------------------------
if (!db.getCollectionNames().includes("follows")) {
    db.createCollection("follows");
}
db.follows.createIndex({ "ownerAcc.userId": 1, _id: -1 }, { name: "ownerAcc.userId_id_desc_index" });
db.follows.createIndex({ "targetAcc.userId": 1, _id: -1 }, { name: "targetAcc.userId_id_desc_index" });
db.follows.createIndex({ "ownerAcc.userId": 1, "targetAcc.userId": 1 }, { name: "ownerAcc.userId_targetAcc.userId_index" });


---------------------------------users-------------------------------------
if (!db.getCollectionNames().includes("users")) {
    db.createCollection("users");
}
db.users.createIndex({ "accountInfo.userId": 1}, { name: "accountInfo.userId_index" });


---------------------------------user_activity-------------------------------------
if (!db.getCollectionNames().includes("user_activity")) {
    db.createCollection("user_activity");
}

db.user_activity.createIndex({ "userId": 1, "date": 1 },{ unique: true, name: "user_date_unique" });

db.user_activity.createIndex({ "date": 1 },{ expireAfterSeconds: 35 * 24 * 60 * 60 });


if (!db.getCollectionNames().includes("log_record_address")) {
    db.createCollection("log_record_address");
}
db.log_record_address.createIndex({ "date": 1 },{ expireAfterSeconds: 30 * 24 * 60 * 60 });


=====================================================2024-11-28 同步生产环境=====================================================================


CREATE TABLE `gpt_user_12_9` AS
SELECT * FROM `gpt_user`;


alter table gpt_user drop column user_type;

alter table gpt_user drop column phone_number;

alter table gpt_user drop column salt;

alter table gpt_user drop column state;

alter table gpt_user drop column login_ip;

alter table gpt_user drop column login_date;

alter table gpt_user drop column pwd_update_date;

alter table gpt_user drop column remark;

alter table gpt_user drop column permission;

alter table gpt_user drop column invited_by;

alter table gpt_user drop column balance;

alter table gpt_user drop column gained_count;

alter table gpt_user drop column used_count;

alter table gpt_user drop column charge_count;

alter table gpt_user drop column context_count;

alter table gpt_user drop column context_max_token;

alter table gpt_user drop column used_words_count;

alter table gpt_user drop column vip_valid_start_time;

alter table gpt_user drop column vip_valid_end_time;

alter table gpt_user drop column gained_token_count;

alter table gpt_user drop column used_token_count;

alter table gpt_user drop column daily_count;


-- auto-generated definition
create table gpt_vip_standards
(
    id               bigint                                          not null comment 'id'
        primary key,
    vip_type         varchar(30)                     default 'basic' null comment '会员类型： basic 非会员 standard 普通会员 pro 高级会员 ',
    daily_lumens     int                                             null comment '每日免费点数',
    monthly_lumen    int                                             null comment '会员点数',
    creation_history varchar(30)                                     null comment '历史页图片保留时长： 免费用户: 30 天  会员: 120 天 超级会员: never expire',
    task_queue       int                                             null comment '排队任务数: 免费用户:0 会员: 5 超级会员: 10',
    concurrent_jobs  int                                             null comment '并发任务数: 免费用户:0 会员: 2 超级会员: 5',
    batch_download   bit                             default b'0'    null comment '是否可以批量下载： 0 否 1 是',
    images_per_batch int                                             null comment '批量生图数： 2 ： 非会员 4 ： 普通会员 4 ： 高级会员',
    upscale          bit                             default b'0'    null comment '是否可以超分： 0 否 1 是',
    inpaint          bit                             default b'0'    null comment '是否可以局部重绘： 0 否 1 是',
    expand           bit                             default b'0'    null comment '是否可以局部扩图： 0 否 1 是',
    colorize         bit                             default b'0'    null comment '是否可以线稿上色： 0 否 1 是',
    removeBG         bit                             default b'0'    null comment '是否可以去背景： 0 否 1 是',
    create_by        varchar(64) collate utf8mb4_bin default ''      null comment '创建者',
    create_time      datetime                                        null comment '创建时间',
    update_by        varchar(64) collate utf8mb4_bin default ''      null comment '更新者',
    update_time      datetime                                        null comment '更新时间'
)
    comment '会员资源标准表';


ALTER TABLE `gpt_user`
    ADD COLUMN `vip_type` varchar(30) default 'basic' COMMENT '会员类型： basic 非会员 standard 普通会员 pro 高级会员 ',
    ADD COLUMN `vip_begin_time` DATETIME NULL COMMENT '普通会员生效时间',
    ADD COLUMN `vip_end_time` DATETIME NULL COMMENT '普通会员过期时间',
    ADD COLUMN `user_config` JSON null comment '用户配置信息',
    ADD COLUMN `daily_lumens_time` varchar(40) null comment '每日免费点数日期(只记录年月日)',
    ADD COLUMN `daily_lumens` int null comment '每日免费点数',
    ADD COLUMN `use_daily_lumens` int null comment '每日使用免费点数';


INSERT INTO piclumen.gpt_vip_standards (id, vip_type, daily_lumens, monthly_lumens, creation_history, task_queue, concurrent_jobs, batch_download, images_per_batch, upscale, inpaint, expand, colorize, remove_bg, create_by, create_time, update_by, update_time) VALUES (1, 'basic', 10, 0, '30 days', 0, 0, false, 2, false, false, false, false, true, '', '2024-12-25 13:28:28', '', null);
INSERT INTO piclumen.gpt_vip_standards (id, vip_type, daily_lumens, monthly_lumens, creation_history, task_queue, concurrent_jobs, batch_download, images_per_batch, upscale, inpaint, expand, colorize, remove_bg, create_by, create_time, update_by, update_time) VALUES (2, 'standard', 10, 1800, 'Never expire', 5, 2, true, 4, true, true, true, true, true, '', '2024-12-25 13:28:30', '', null);
INSERT INTO piclumen.gpt_vip_standards (id, vip_type, daily_lumens, monthly_lumens, creation_history, task_queue, concurrent_jobs, batch_download, images_per_batch, upscale, inpaint, expand, colorize, remove_bg, create_by, create_time, update_by, update_time) VALUES (3, 'pro', 10, 4800, 'Never expire', 10, 5, true, 4, true, true, true, true, true, '', '2024-12-25 13:28:31', '', null);


create index gpt_user_apple_id_index
    on gpt_user (apple_id);

create index gpt_user_google_id_index
    on gpt_user (google_id);


=====================================================2024-11-28 同步生产环境=====================================================================

-- 删除redis中不要的数据
unlink mark_id_queue_name
-- create index logic_period_end_index
--     on pay_lumen_record (logic_period_end);

if (!db.getCollectionNames().includes("blacklist")) {
    db.createCollection("blacklist");
}

db.blacklist.createIndex({ "ownerAcc.userId": 1, _id: -1 }, { name: "ownerAcc.userId_id_desc_index" });
db.blacklist.createIndex({ "targetAcc.userId": 1, _id: -1 }, { name: "targetAcc.userId_id_desc_index" });

alter table gpt_user modify daily_lumens_time bigint null comment '每日免费点数日期(只记录年月日)';

ALTER TABLE `piclumen`.`gpt_user`
    MODIFY COLUMN `vip_begin_time` bigint NULL DEFAULT NULL COMMENT '普通会员生效时间' AFTER `vip_level`,
    MODIFY COLUMN `vip_end_time` bigint ZEROFILL NULL DEFAULT NULL COMMENT '普通会员过期时间' AFTER `vip_begin_time`;

ALTER TABLE `piclumen`.`gpt_user`
    ADD COLUMN `price_interval` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'year, month' AFTER `vip_type`;

alter table gpt_vip_standards
    add history_explore bit null comment '是否展示历史页' after remove_bg;

alter table gpt_vip_standards
    add translation bit null comment '是否可以翻译： 0 否 1是' after history_explore;

alter table gpt_vip_standards
    add enhance bit null comment '是否可以增强' after translation;


=====================================================2025-01-02 同步生产环境=====================================================================



alter table gpt_prompt_file_0
    add high_mini_url varchar(500) null comment '高清mini图' after high_thumbnail_url;

alter table gpt_prompt_file_1
    add high_mini_url varchar(500) null comment '高清mini图' after high_thumbnail_url;

alter table gpt_prompt_file_2
    add high_mini_url varchar(500) null comment '高清mini图' after high_thumbnail_url;

alter table gpt_prompt_file_3
    add high_mini_url varchar(500) null comment '高清mini图' after high_thumbnail_url;

alter table gpt_prompt_file_4
    add high_mini_url varchar(500) null comment '高清mini图' after high_thumbnail_url;

alter table gpt_prompt_file_5
    add high_mini_url varchar(500) null comment '高清mini图' after high_thumbnail_url;

alter table gpt_prompt_file_6
    add high_mini_url varchar(500) null comment '高清mini图' after high_thumbnail_url;

alter table gpt_prompt_file_7
    add high_mini_url varchar(500) null comment '高清mini图' after high_thumbnail_url;

alter table gpt_prompt_file_8
    add high_mini_url varchar(500) null comment '高清mini图' after high_thumbnail_url;

alter table gpt_prompt_file_9
    add high_mini_url varchar(500) null comment '高清mini图' after high_thumbnail_url;

alter table gpt_prompt_file_10
    add high_mini_url varchar(500) null comment '高清mini图' after high_thumbnail_url;

alter table gpt_prompt_file_11
    add high_mini_url varchar(500) null comment '高清mini图' after high_thumbnail_url;

alter table gpt_prompt_file_12
    add high_mini_url varchar(500) null comment '高清mini图' after high_thumbnail_url;

alter table gpt_prompt_file_13
    add high_mini_url varchar(500) null comment '高清mini图' after high_thumbnail_url;

alter table gpt_prompt_file_14
    add high_mini_url varchar(500) null comment '高清mini图' after high_thumbnail_url;

alter table gpt_prompt_file_15
    add high_mini_url varchar(500) null comment '高清mini图' after high_thumbnail_url;

alter table gpt_prompt_file_16
    add high_mini_url varchar(500) null comment '高清mini图' after high_thumbnail_url;

alter table gpt_prompt_file_17
    add high_mini_url varchar(500) null comment '高清mini图' after high_thumbnail_url;

alter table gpt_prompt_file_18
    add high_mini_url varchar(500) null comment '高清mini图' after high_thumbnail_url;

alter table gpt_prompt_file_19
    add high_mini_url varchar(500) null comment '高清mini图' after high_thumbnail_url;


alter table gpt_public_file_review modify rejection_content varchar(1000) null comment '拒绝内容描述';

alter table gpt_public_file_review
    add high_mini_url varchar(500) null comment '高清mini图' after high_thumbnail_url;

alter table gpt_public_file_review
    add mini_thumbnail_url  varchar(500) null comment 'mini url' after high_thumbnail_url;

ALTER TABLE `piclumen`.`gpt_user`
    ADD COLUMN `price_interval` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'year, month' AFTER `vip_type`;

=====================================================2025-01-04 同步生产环境=====================================================================


alter table gpt_user alter column sex set default '2';

update gpt_user set sex = '2';


=====================================================2025-01-13 同步生产环境=====================================================================


- 为 gpt_prompt_record_0 到 gpt_prompt_record_19 添加字段 cost_lumens 和 platform

alter table gpt_prompt_record_0 add column cost_lumens int null comment '消耗lumens点数' after fast_hour;
alter table gpt_prompt_record_0 add column platform varchar(255) null comment '来源平台' after cost_lumens;

alter table gpt_prompt_record_1 add column cost_lumens int null comment '消耗lumens点数' after fast_hour;
alter table gpt_prompt_record_1 add column platform varchar(255) null comment '来源平台' after cost_lumens;

alter table gpt_prompt_record_2 add column cost_lumens int null comment '消耗lumens点数' after fast_hour;
alter table gpt_prompt_record_2 add column platform varchar(255) null comment '来源平台' after cost_lumens;

alter table gpt_prompt_record_3 add column cost_lumens int null comment '消耗lumens点数' after fast_hour;
alter table gpt_prompt_record_3 add column platform varchar(255) null comment '来源平台' after cost_lumens;

alter table gpt_prompt_record_4 add column cost_lumens int null comment '消耗lumens点数' after fast_hour;
alter table gpt_prompt_record_4 add column platform varchar(255) null comment '来源平台' after cost_lumens;

alter table gpt_prompt_record_5 add column cost_lumens int null comment '消耗lumens点数' after fast_hour;
alter table gpt_prompt_record_5 add column platform varchar(255) null comment '来源平台' after cost_lumens;

alter table gpt_prompt_record_6 add column cost_lumens int null comment '消耗lumens点数' after fast_hour;
alter table gpt_prompt_record_6 add column platform varchar(255) null comment '来源平台' after cost_lumens;

alter table gpt_prompt_record_7 add column cost_lumens int null comment '消耗lumens点数' after fast_hour;
alter table gpt_prompt_record_7 add column platform varchar(255) null comment '来源平台' after cost_lumens;

alter table gpt_prompt_record_8 add column cost_lumens int null comment '消耗lumens点数' after fast_hour;
alter table gpt_prompt_record_8 add column platform varchar(255) null comment '来源平台' after cost_lumens;

alter table gpt_prompt_record_9 add column cost_lumens int null comment '消耗lumens点数' after fast_hour;
alter table gpt_prompt_record_9 add column platform varchar(255) null comment '来源平台' after cost_lumens;

alter table gpt_prompt_record_10 add column cost_lumens int null comment '消耗lumens点数' after fast_hour;
alter table gpt_prompt_record_10 add column platform varchar(255) null comment '来源平台' after cost_lumens;

alter table gpt_prompt_record_11 add column cost_lumens int null comment '消耗lumens点数' after fast_hour;
alter table gpt_prompt_record_11 add column platform varchar(255) null comment '来源平台' after cost_lumens;

alter table gpt_prompt_record_12 add column cost_lumens int null comment '消耗lumens点数' after fast_hour;
alter table gpt_prompt_record_12 add column platform varchar(255) null comment '来源平台' after cost_lumens;

alter table gpt_prompt_record_13 add column cost_lumens int null comment '消耗lumens点数' after fast_hour;
alter table gpt_prompt_record_13 add column platform varchar(255) null comment '来源平台' after cost_lumens;

alter table gpt_prompt_record_14 add column cost_lumens int null comment '消耗lumens点数' after fast_hour;
alter table gpt_prompt_record_14 add column platform varchar(255) null comment '来源平台' after cost_lumens;

alter table gpt_prompt_record_15 add column cost_lumens int null comment '消耗lumens点数' after fast_hour;
alter table gpt_prompt_record_15 add column platform varchar(255) null comment '来源平台' after cost_lumens;

alter table gpt_prompt_record_16 add column cost_lumens int null comment '消耗lumens点数' after fast_hour;
alter table gpt_prompt_record_16 add column platform varchar(255) null comment '来源平台' after cost_lumens;

alter table gpt_prompt_record_17 add column cost_lumens int null comment '消耗lumens点数' after fast_hour;
alter table gpt_prompt_record_17 add column platform varchar(255) null comment '来源平台' after cost_lumens;

alter table gpt_prompt_record_18 add column cost_lumens int null comment '消耗lumens点数' after fast_hour;
alter table gpt_prompt_record_18 add column platform varchar(255) null comment '来源平台' after cost_lumens;

alter table gpt_prompt_record_19 add column cost_lumens int null comment '消耗lumens点数' after fast_hour;
alter table gpt_prompt_record_19 add column platform varchar(255) null comment '来源平台' after cost_lumens;


alter table gpt_user
    add used_collect_num int default 0 null comment '用户已收藏数量' after total_size;

alter table gpt_user
    add total_collect_num int default 500 null comment '用户能收藏总数量' after total_size;

#将vip_type 默认值设为basic
alter table gpt_user alter column vip_type set default 'basic';

update gpt_user set vip_type = 'basic';

#将 每日的lumen设置相关的默认值
alter table gpt_user alter column daily_lumens set default 10;

alter table gpt_user alter column use_daily_lumens set default 0;

update gpt_user set daily_lumens = 10, use_daily_lumens = 0 where daily_lumens is null;



alter table gpt_contacts
    add user_agent varchar(1024) null comment '用户使用终端' after thumbnail_urls;


alter table kpi_mix
    add flux_dev_count bigint null comment 'flux-dev生图数量' after flux_count;


#note
#将s4的 gpt_vip_standards 同步到生产环境

###  同步表 就需要新增字段  alter table gpt_vip_standards add collect_num int null comment '用户收藏夹张数标准' after batch_download;

#生产的 gpt_user_album ，所有图片的宽高都需要刷上

#清理redis中 user_task_timestamp 开头的数据 redis-cli --scan --pattern user_task_timestamp* | xargs -L 1 redis-cli del



CREATE INDEX login_name_index_0 ON gpt_user_collect_0 (login_name);
CREATE INDEX login_name_index_1 ON gpt_user_collect_1 (login_name);
CREATE INDEX login_name_index_2 ON gpt_user_collect_2 (login_name);
CREATE INDEX login_name_index_3 ON gpt_user_collect_3 (login_name);
CREATE INDEX login_name_index_4 ON gpt_user_collect_4 (login_name);
CREATE INDEX login_name_index_5 ON gpt_user_collect_5 (login_name);
CREATE INDEX login_name_index_6 ON gpt_user_collect_6 (login_name);
CREATE INDEX login_name_index_7 ON gpt_user_collect_7 (login_name);
CREATE INDEX login_name_index_8 ON gpt_user_collect_8 (login_name);
CREATE INDEX login_name_index_9 ON gpt_user_collect_9 (login_name);
CREATE INDEX login_name_index_10 ON gpt_user_collect_10 (login_name);
CREATE INDEX login_name_index_11 ON gpt_user_collect_11 (login_name);
CREATE INDEX login_name_index_12 ON gpt_user_collect_12 (login_name);
CREATE INDEX login_name_index_13 ON gpt_user_collect_13 (login_name);
CREATE INDEX login_name_index_14 ON gpt_user_collect_14 (login_name);
CREATE INDEX login_name_index_15 ON gpt_user_collect_15 (login_name);
CREATE INDEX login_name_index_16 ON gpt_user_collect_16 (login_name);
CREATE INDEX login_name_index_17 ON gpt_user_collect_17 (login_name);
CREATE INDEX login_name_index_18 ON gpt_user_collect_18 (login_name);
CREATE INDEX login_name_index_19 ON gpt_user_collect_19 (login_name);



CREATE TABLE `stripe_failed_cancel_subscription_records`
(
    `id`                  bigint                                  NOT NULL AUTO_INCREMENT COMMENT '主键 ID',
    `user_id`             bigint                                  NOT NULL COMMENT '用户 ID',
    `login_name`          varchar(255) COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户账号',
    `customer_id`         varchar(255) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'Stripe 客户 ID',
    `old_subscription_id` varchar(255) COLLATE utf8mb4_general_ci NOT NULL COMMENT '取消失败的 老的订阅 ID',
    `new_subscription_id` varchar(255) COLLATE utf8mb4_general_ci NOT NULL COMMENT '已经升级成功订阅 ID',
    `new_price_id`        varchar(255) COLLATE utf8mb4_general_ci NOT NULL COMMENT '新产品价格 ID',
    `create_time`         datetime                                                     DEFAULT NULL COMMENT '创建时间',
    `update_time`         datetime                                                     DEFAULT NULL COMMENT '修改时间',
    `create_by`           varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建者',
    `update_by`           varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '修改者',
    `del`                 bit(1)                                                       DEFAULT b'0' COMMENT '0 ： 未删除  1 ：已删除',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT ='升级订阅时取消旧订阅失败记录表';



create table sys_lumen_admin_operate_log
(
    id                  bigint      not null
        primary key,
    lumen_record_id     bigint      not null comment 'lumen实时记录id',
    oper_name           varchar(50) null comment '操作人员',
    dept_name           varchar(50) null comment '部门名称',
    before_change_lumen int         null comment '更改前lumen量',
    after_change_lumen  int         null comment '更改后lumen量',
    lumen               int         null comment 'lumen量',
    type                int         null comment '1: 减少lumen量, 2: 设置无效，3:赠送',
    create_by           varchar(64) null comment '创建者',
    create_time         datetime    null comment '创建时间',
    update_by           varchar(64) null comment '更新者',
    update_time         datetime    null comment '更新时间'
)
    comment 'Lumen管理操作日志' row_format = COMPACT;

create index lumen_record_id_index
    on sys_lumen_admin_operate_log (lumen_record_id);



create table gpt_suspension_message
(
    id           bigint             not null
        primary key,
    version      int                null comment '公告版本号',
    is_efficient smallint default 0 null comment '是否有效（0否 1是）',
    start_time   bigint             null comment '停服开始时间(秒级别时间戳)',
    end_time     bigint             null comment '停服结束时间(秒级别时间戳)',
    create_time  datetime           null comment '创建时间',
    update_time  datetime           null comment '修改时间',
    create_by    varchar(80)        null comment '创建者',
    update_by    varchar(80)        null comment '修改者'
)
    comment '停服公告' row_format = COMPACT;



create table kpi_day_max_task_size
(
    id          bigint auto_increment comment 'ID'
        primary key,
    key_name    varchar(10)              not null comment '规则',
    mark        varchar(100)             null comment '规则标识',
    date        date                     not null comment '日期',
    score       double                   null comment '最大并发数',
    fair        bit         default b'0' null comment '是否公平',
    guarantees  bit         default b'0' null comment '是否保底规则',
    create_by   varchar(64) default ''   null comment '创建者',
    create_time datetime                 null comment '创建时间',
    update_by   varchar(64) default ''   null comment '更新者',
    update_time datetime                 null comment '更新时间'
)
    comment '每日规则最大并发数' charset = utf8mb4;


create index logic_period_end_index
    on pay_lumen_record (logic_period_end);

create index login_name_index
    on pay_lumen_record (login_name);

create index user_id_index
    on pay_lumen_record (user_id);


=====================================================2025-01-15 同步生产环境=====================================================================

###将s4的会员标准信息表gpt_vip_standards 迁移到生产

-- 删除旧的 creation_history 列
ALTER TABLE gpt_vip_standards
    DROP COLUMN creation_history;

-- 新增 creation_history 列，放在 monthly_lumens 列后
ALTER TABLE gpt_vip_standards
    ADD COLUMN creation_history INT AFTER monthly_lumens;


alter table pay_lumen_record
    add original_transaction_id varchar(255) null after vip_plat_form;

alter table pay_lumen_record
    add transaction_id varchar(255) null after original_transaction_id;

alter table subscription_current
    add original_transaction_id varchar(255) null ;

alter table subscription_current
    add transaction_id varchar(255) null;



create table gpt_export_gen_info
(
    id            bigint                                           comment 'ID' primary key,
    user_id       bigint                                      null comment '用户id',
    login_name    varchar(80)                                 null comment '用户名称',
    email         varchar(100) collate utf8mb4_bin default '' null comment '用户邮箱',
    export_flag   tinyint                          default 0  null comment '是否已经导出发给用户： 0 ：否   1 ：是',
    create_by     varchar(80) collate utf8mb4_bin  default '' null comment '创建者',
    create_time   datetime                                    null comment '创建时间',
    update_by     varchar(80) collate utf8mb4_bin  default '' null comment '更新者',
    update_time   datetime                                    null comment '更新时间'
)
    comment '用户导出生图入参表';


create index login_name_index
    on gpt_export_gen_info (login_name);


create index login_name_del_create_time_index
    on gpt_prompt_file_0 (login_name,del, create_time);

create index login_name_del_create_time_index
    on gpt_prompt_file_1 (login_name,del, create_time);

create index login_name_del_create_time_index
    on gpt_prompt_file_2 (login_name,del, create_time);

create index login_name_del_create_time_index
    on gpt_prompt_file_3 (login_name,del, create_time);

create index login_name_del_create_time_index
    on gpt_prompt_file_4 (login_name,del, create_time);

create index login_name_del_create_time_index
    on gpt_prompt_file_5 (login_name,del, create_time);

create index login_name_del_create_time_index
    on gpt_prompt_file_6 (login_name,del, create_time);

create index login_name_del_create_time_index
    on gpt_prompt_file_7 (login_name,del, create_time);

create index login_name_del_create_time_index
    on gpt_prompt_file_8 (login_name,del, create_time);

create index login_name_del_create_time_index
    on gpt_prompt_file_9 (login_name,del, create_time);

create index login_name_del_create_time_index
    on gpt_prompt_file_10 (login_name,del, create_time);

create index login_name_del_create_time_index
    on gpt_prompt_file_11 (login_name,del, create_time);

create index login_name_del_create_time_index
    on gpt_prompt_file_12 (login_name,del, create_time);

create index login_name_del_create_time_index
    on gpt_prompt_file_13 (login_name,del, create_time);

create index login_name_del_create_time_index
    on gpt_prompt_file_14 (login_name,del, create_time);

create index login_name_del_create_time_index
    on gpt_prompt_file_15 (login_name,del, create_time);

create index login_name_del_create_time_index
    on gpt_prompt_file_16 (login_name,del, create_time);

create index login_name_del_create_time_index
    on gpt_prompt_file_17 (login_name,del, create_time);

create index login_name_del_create_time_index
    on gpt_prompt_file_18 (login_name,del, create_time);

create index login_name_del_create_time_index
    on gpt_prompt_file_19 (login_name,del, create_time);


