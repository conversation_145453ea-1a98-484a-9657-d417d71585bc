import os
from flask import Flask, request, jsonify
from nudenet import NudeDetector

app = Flask(__name__)
nude_detector = NudeDetector()

# 指定保存图片的目录
UPLOAD_FOLDER = 'uploads'
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER


@app.route('/detect_nudity', methods=['POST'])
def detect_nudity():
  if 'image' not in request.files:
    return jsonify({'error': 'No image file provided'})

  image_file = request.files['image']

  # 保存图片到指定目录
  image_path = os.path.join(app.config['UPLOAD_FOLDER'], image_file.filename)
  image_file.save(image_path)

  # 加载保存的图片进行检测
  # with open(image_path, 'rb') as f:
  detections = nude_detector.detect(image_path)

  # 删除保存的图片
  os.remove(image_path)

  return jsonify({'detections': detections})


if __name__ == '__main__':
  # 创建上传目录
  if not os.path.exists(UPLOAD_FOLDER):
    os.makedirs(UPLOAD_FOLDER)

  app.run(host='0.0.0.0', port=5000)
