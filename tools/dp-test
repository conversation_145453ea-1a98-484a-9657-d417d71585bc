#!/bin/bash

cd ..
CUR_TIME=$(date +%Y%m%d%H%M)
sed -i "s/_Build/_Build$CUR_TIME/g" src/main/resources/application.properties

mvn clean package -Dmaven.test.skip
ssh root@************* "/workspace/xiang/spring-boot.sh status piclumen-0.0.1-SNAPSHOT.jar"
ssh root@************* "/workspace/xiang/spring-boot.sh stop piclumen-0.0.1-SNAPSHOT.jar"
sleep 10
ssh root@************* "rm -f /workspace/xiang/piclumen-0.0.1-SNAPSHOT.jar"
scp ./target/piclumen-0.0.1-SNAPSHOT.jar root@*************:/workspace/xiang
ssh root@************* "nohup java -jar /workspace/xiang/piclumen-0.0.1-SNAPSHOT.jar --spring.profiles.active=test >> /workspace/xiang/nohup-gpt.out &"

git restore src/main/resources/application.properties
