package com.lx.pl.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/5/23
 * @description 用户lumen详情
 */
@Data
@Schema(description = "用户lumen详情")
public class UserLumenDetailVO {

    private Long userId;

    private String loginName;

    /**
     * 用户剩余可用lumen点数
     */
    @Schema(description = "用户剩余可用lumen点数")
    private Integer remainingAvailableLumen;
}
