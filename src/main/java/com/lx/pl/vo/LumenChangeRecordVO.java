package com.lx.pl.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/6/10
 * @description
 */
@Data
public class LumenChangeRecordVO {
    /**
     * 记录id
     */
    @Schema(description = "记录id")
    private String id;

    /**
     * 用户id
     */
    @Schema(description = "用户id")
    private Long userId;

    /**
     * 登录账号
     */
    @Schema(description = "登录账号")
    private String userLoginName;

    /**
     * 变化类型
     * {@link com.lx.pl.enums.LumenChangeTypeEnum}
     */
    @Schema(description = "变化类型，add-增加，deduct-减少")
    private String changeType;

    /**
     * 变化数量
     */
    @Schema(description = "变化数量")
    private Integer changeLumen;

    /**
     * 变化来源
     * {@link com.lx.pl.enums.LumenChangeSourceEnum}
     */
    @Schema(description = "变化来源")
    private String source;

    /**
     * 变化详情
     */
    @Schema(description = "变化详情")
    private String detail;

    /**
     * 发生时间
     */
    @Schema(description = "发生时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime happenTime;
}
