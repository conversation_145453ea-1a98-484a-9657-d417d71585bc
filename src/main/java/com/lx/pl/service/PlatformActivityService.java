package com.lx.pl.service;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lx.pl.db.mysql.community.entity.AccountInfo;
import com.lx.pl.db.mysql.community.entity.UserPlatformActivity;
import com.lx.pl.db.mysql.community.entity.UserSysUpdate;
import com.lx.pl.db.mysql.gen.entity.GptPlatformActivity;
import com.lx.pl.db.mysql.gen.entity.GptSysUpdate;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.db.mysql.gen.mapper.GptPlatformActivityMapper;
import com.lx.pl.db.mysql.gen.repository.UserPlatformActivityRepository;
import com.lx.pl.dto.CommPageInfo;
import com.lx.pl.enums.VipType;
import com.lx.pl.exception.ServerInternalException;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PlatformActivityService {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private UserPlatformActivityRepository userPlatformActivityRepository;

    @Autowired
    private GptPlatformActivityMapper platformActivityMapper;

    @Autowired
    private RedissonClient redissonClient;

    public Boolean readPlatformActivity(String platformActivityId, User user) {
        RLock lock = redissonClient.getLock("read:platformActivity:" + user.getId());
        try {
            //单个已读平台活动
            if (StringUtil.isNotBlank(platformActivityId)) {
                UserPlatformActivity platformActivity = getUserPlatformActivity(user.getId(), platformActivityId);
                //判断用户是否已读平台活动
                if (Objects.isNull(platformActivity)) {
                    UserPlatformActivity activity = new UserPlatformActivity();

                    //已读发起者用户
                    AccountInfo accountInfo = new AccountInfo();
                    accountInfo.setUserId(user.getId());
                    accountInfo.setUserName(user.getUserName());
                    accountInfo.setUserLoginName(user.getLoginName());
                    accountInfo.setUserAvatarUrl(user.getAvatarUrl());
                    accountInfo.setWhetherPro(Boolean.FALSE);
                    activity.setAccountInfo(accountInfo);

                    activity.setPlatformActivityId(platformActivityId);
                    activity.setCreateTime(LocalDateTime.now());

                    userPlatformActivityRepository.insert(activity);
                }
            } else {
                //全部更新为已读
                LambdaQueryWrapper<GptPlatformActivity> lwq = new LambdaQueryWrapper<>();
                lwq.eq(GptPlatformActivity::getPublish, Boolean.TRUE);
                if (VipType.basic.getValue().equals(user.getVipType())) {
                    lwq.in(GptPlatformActivity::getUserType, new ArrayList<>(Arrays.asList("all", "not_vip")));
                } else {
                    lwq.in(GptPlatformActivity::getUserType, new ArrayList<>(Arrays.asList("all", "vip")));
                }
                List<GptPlatformActivity> gptPlatformActivityList = platformActivityMapper.selectList(lwq);

                if (!CollectionUtils.isEmpty(gptPlatformActivityList)) {
                    List<String> platformActivityList = gptPlatformActivityList.stream()
                            .map(platformActivity -> String.valueOf(platformActivity.getId()))
                            .collect(Collectors.toList());

                    //移除所有的已读数据
                    Query query = new Query();
                    query.addCriteria(Criteria.where("accountInfo.userId").is(user.getId()));
                    // 执行删除操作并判断是否删除了至少一个文档
                    mongoTemplate.remove(query, UserPlatformActivity.class);

                    //批量插入所有平台活动数据
                    List<UserPlatformActivity> userPlatformActivityList = new ArrayList<>();
                    for (String userPlatformActivityId : platformActivityList) {
                        UserPlatformActivity userPlatformActivity = new UserPlatformActivity();

                        //已读发起者用户
                        AccountInfo accountInfo = new AccountInfo();
                        accountInfo.setUserId(user.getId());
                        accountInfo.setUserName(user.getUserName());
                        accountInfo.setUserLoginName(user.getLoginName());
                        accountInfo.setUserAvatarUrl(user.getAvatarUrl());
                        accountInfo.setWhetherPro(Boolean.FALSE);
                        userPlatformActivity.setAccountInfo(accountInfo);

                        userPlatformActivity.setPlatformActivityId(userPlatformActivityId);
                        userPlatformActivity.setCreateTime(LocalDateTime.now());

                        userPlatformActivityList.add(userPlatformActivity);
                    }
                    userPlatformActivityRepository.saveAll(userPlatformActivityList);
                }
            }

            return Boolean.TRUE;
        } catch (Exception e) {
            log.error("用户：{} 对平台活动：{} 已读报错", user.getId(), platformActivityId, e);
            throw new ServerInternalException("Read platformActivity Failed");
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    public UserPlatformActivity getUserPlatformActivity(Long userId, String platformActivityId) {
        try {
            Query query = new Query();
            query.addCriteria(Criteria.where("accountInfo.userId").is(userId));
            query.addCriteria(Criteria.where("platformActivityId").is(platformActivityId));
            UserPlatformActivity platformActivity = mongoTemplate.findOne(query, UserPlatformActivity.class);
            return platformActivity;
        } catch (Exception e) {
            log.error("查询用户已读平台活动报错，平台活动id为：{}，用户id:{}", platformActivityId, userId, e);
            return null;
        }
    }


    public CommPageInfo<GptPlatformActivity> getActivityMessage(String lastActivityId, Integer pageSize, User user) {
        List<GptPlatformActivity> gptPlatformActivityList = new ArrayList<>();
        try {
            LambdaQueryWrapper<GptPlatformActivity> lqw = new LambdaQueryWrapper<>();

            lqw.eq(GptPlatformActivity::getPublish, true); // 仅查询已发布的数据
            if (StringUtil.isNotBlank(lastActivityId)) {
                lqw.lt(GptPlatformActivity::getId, lastActivityId); // 仅查询 id 小于 lastId 的数据（游标分页）
            }
            if (VipType.basic.getValue().equals(user.getVipType())) {
                lqw.in(GptPlatformActivity::getUserType, new ArrayList<>(Arrays.asList("all", "not_vip")));
            } else {
                lqw.in(GptPlatformActivity::getUserType, new ArrayList<>(Arrays.asList("all", "vip")));
            }
            lqw.orderByDesc(GptPlatformActivity::getId); // 按 id 倒序排序
            lqw.last("LIMIT " + pageSize); // 限制查询条数

            gptPlatformActivityList = platformActivityMapper.selectList(lqw);

            //组装图片的地址
            if (!CollectionUtils.isEmpty(gptPlatformActivityList)) {
                List<String> platformActivitIds = gptPlatformActivityList.stream()
                        .map(platformActivity -> String.valueOf(platformActivity.getId()))
                        .collect(Collectors.toList());
                Set<String> activityIdSet = getReadActivityByUser(platformActivitIds, user);

                for (GptPlatformActivity gptPlatformActivity : gptPlatformActivityList) {
                    gptPlatformActivity.setRead((!Objects.isNull(activityIdSet) && activityIdSet.contains(String.valueOf(gptPlatformActivity.getId())))
                            ? Boolean.TRUE : Boolean.FALSE);
                }
            }

        } catch (Exception e) {
            log.error("获取个人通知表报错", e);
            throw new ServerInternalException("Error retrieving the activity likes list");
        }
        return buildPromptPageInfo(pageSize, gptPlatformActivityList);
    }


    public Set<String> getReadActivityByUser(List<String> platformActivitIds, User user) {
        try {
            Set<String> activityIdSet = new HashSet<>();

            Query query = new Query();
            query.addCriteria(Criteria.where("accountInfo.userId").is(user.getId())
                    .and("platformActivityId").in(platformActivitIds));
            List<UserPlatformActivity> activityIdList = mongoTemplate.find(query, UserPlatformActivity.class);

            if (!CollectionUtils.isEmpty(activityIdList)) {
                activityIdSet = activityIdList.stream()
                        .map(UserPlatformActivity::getPlatformActivityId).collect(Collectors.toSet());
            }
            return activityIdSet;
        } catch (Exception e) {
            log.error("查询用户：{} 的公告活动通知报错", user.getLoginName(), e);
            return null;
        }
    }


    // 构建分页结果对象
    private CommPageInfo<GptPlatformActivity> buildPromptPageInfo(Integer pageSize, List<GptPlatformActivity> gptPlatformActivityList) {
        CommPageInfo<GptPlatformActivity> commPageInfo = new CommPageInfo<>();
        commPageInfo.setResultList(CollectionUtils.isEmpty(gptPlatformActivityList) ? Collections.emptyList() : gptPlatformActivityList);
        commPageInfo.setPageSize(pageSize);
        commPageInfo.setLastId(CollectionUtils.isEmpty(gptPlatformActivityList) ? "" : String.valueOf(gptPlatformActivityList.get(gptPlatformActivityList.size() - 1).getId()));
        return commPageInfo;
    }

}
