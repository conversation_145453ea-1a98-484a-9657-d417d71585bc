package com.lx.pl.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lx.pl.db.mysql.community.entity.CommFile;
import com.lx.pl.db.mysql.gen.entity.PromptFile;
import com.lx.pl.db.mysql.gen.entity.PublicFileReview;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.db.mysql.gen.entity.UserCollect;
import com.lx.pl.db.mysql.gen.mapper.HistoryImgMapper;
import com.lx.pl.db.mysql.gen.mapper.PromptFileMapper;
import com.lx.pl.db.mysql.gen.mapper.PublicFileReviewMapper;
import com.lx.pl.db.mysql.gen.mapper.UserCollectMapper;
import com.lx.pl.dto.*;
import com.lx.pl.enums.OriginCreate;
import com.lx.pl.enums.PublicType;
import com.lx.pl.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class HistoryImgService {

    @Autowired
    HistoryImgMapper historyImgMapper;

    @Autowired
    UserCollectMapper userCollectMapper;

    @Autowired
    PromptFileMapper promptFileMapper;

    @Autowired
    PublicFileReviewMapper publicFileReviewMapper;

    @Autowired
    private MongoTemplate mongoTemplate;

    public PromptPageInfo<HistoryImgResult> getHistoryList(Integer pageNum, Integer pageSize, String vagueKey, String collationName, Boolean  notCollection, User user){
        vagueKey = StringUtils.isNotBlank(vagueKey) ? vagueKey.trim() : "";
        // 根据promptId进行分页查询
        Page<HistoryImgResult> page = new Page<>(pageNum, pageSize);
        IPage<HistoryImgResult> iPage = historyImgMapper.getHistoryList(page, user.getLoginName(), vagueKey, collationName,user.getVipType(),notCollection);
        List<HistoryImgResult> records = iPage.getRecords();

        PromptPageInfo<HistoryImgResult> promptPageInfo = new PromptPageInfo<>();
        promptPageInfo.setResultList(records);
        promptPageInfo.setTotal(!Objects.isNull(page.getTotal()) ? Math.toIntExact(page.getTotal()) : 0);
        promptPageInfo.setPageNum(pageNum);
        promptPageInfo.setPageSize(pageSize);
        return promptPageInfo;
    }

    public PromptPageInfo<NotPublishImgResult> getNotPublishImgList(Integer pageNum, Integer pageSize, String classifyId, User user) {

        Page<NotPublishImgResult> page = new Page<>(pageNum, pageSize);
        List<NotPublishImgResult> resultList;

        if ("0".equals(classifyId)) {
            IPage<NotPublishImgResult> iPage = historyImgMapper.getNotCollectionList(page, user.getLoginName(), PublicType.undisclosed.getValue(),user.getVipType());
            resultList = iPage.getRecords();
        } else {
            resultList = getCollectedImgList(page, classifyId, user);
        }

        PromptPageInfo<NotPublishImgResult> pageInfo = new PromptPageInfo<>();
        pageInfo.setResultList(resultList);
        pageInfo.setPageNum(pageNum);
        pageInfo.setPageSize(pageSize);
        pageInfo.setTotal(!Objects.isNull(page.getTotal()) ? Math.toIntExact(page.getTotal()) : 0);
        return pageInfo;
    }

    private List<NotPublishImgResult> getCollectedImgList(Page page, String classifyId, User user) {
        List<NotPublishImgResult> notPublishImgResults = new ArrayList<>();

        LambdaQueryWrapper<UserCollect> qw = new LambdaQueryWrapper<UserCollect>()
                .eq(UserCollect::getClassifyId, classifyId)
                .eq(UserCollect::getLoginName, user.getLoginName())
                .ne(UserCollect::getOriginCreate, OriginCreate.customUpload.getValue())
                .orderByDesc(UserCollect::getCreateTime)
                .orderByDesc(UserCollect::getId);

        IPage<UserCollect> pageCollect = userCollectMapper.selectPage(page, qw);
        List<UserCollect> records = pageCollect.getRecords();

        if (!CollectionUtils.isEmpty(records)) {
            List<Long> fileIds = records.stream()
                    .map(UserCollect::getFileId)
                    .collect(Collectors.toList());

            LambdaQueryWrapper<PromptFile> qfw = new LambdaQueryWrapper<PromptFile>()
                    .eq(PromptFile::getLoginName, user.getLoginName())
                    .in(PromptFile::getId, fileIds);

            List<PromptFile> promptFiles = promptFileMapper.selectList(qfw);

            Map<Long, PromptFile> promptFileMap = promptFiles.stream()
                    .collect(Collectors.toMap(PromptFile::getId, Function.identity()));

            for (UserCollect userCollect : records) {
                PromptFile promptFile = promptFileMap.get(userCollect.getFileId());
                if (promptFile != null && (!PublicType.undisclosed.getValue().equals(promptFile.getIsPublic())||
                       StringUtils.isNotBlank(promptFile.getSensitiveMessage()))){
                    continue;
                }
                NotPublishImgResult result = new NotPublishImgResult();
                BeanUtils.copyProperties(userCollect, result);
                result.setImgName(userCollect.getFileName());
                result.setImgUrl(userCollect.getFileUrl());
                result.setHighMiniUrl(promptFile != null ? promptFile.getHighMiniUrl() : "");
                result.setIsPublic(promptFile != null ? promptFile.getIsPublic() : PublicType.undisclosed.getValue());
                notPublishImgResults.add(result);
            }
        }

        return notPublishImgResults;
    }

    public  List<UserActivityPostsResult> getUserActivityPostsList(String activityId ,User user){

        List<UserActivityPostsResult> resultList = new ArrayList<>();
        String loginName = user.getLoginName();

        // 从审核表中查出正在审核的数据
        LambdaQueryWrapper<PublicFileReview> pfw = new LambdaQueryWrapper<>();
        pfw.eq(PublicFileReview::getActivityId, activityId);
        pfw.eq(PublicFileReview::getLoginName, loginName);
        List<PublicFileReview> publicFileReviewList = publicFileReviewMapper.selectList(pfw);

        for (PublicFileReview publicFileReview : publicFileReviewList) {
            UserActivityPostsResult result = new UserActivityPostsResult();
            BeanUtils.copyProperties(publicFileReview, result);
            result.setId(null);
            result.setIsPublic(PublicType.review.getValue());
            result.setImgUrl(publicFileReview.getFileUrl());
            resultList.add(result);
        }

        // 从mongodb 查询已发布到社区的数据
        Query query = new Query();
        query.addCriteria(Criteria.where("accountInfo.userId").is(user.getId()))
                .addCriteria(Criteria.where("activityId").is(Long.parseLong(activityId)));
        List<CommFile> commFileList = mongoTemplate.find(query, CommFile.class);

        for (CommFile commFile : commFileList) {
            UserActivityPostsResult result = new UserActivityPostsResult();
            BeanUtils.copyProperties(commFile, result);
            result.setIsPublic(PublicType.publicity.getValue());
            result.setImgUrl(commFile.getFileUrl());
            resultList.add(result);
        }
        return resultList;
    }


}
