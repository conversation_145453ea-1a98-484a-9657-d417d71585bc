package com.lx.pl.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.node.TextNode;
import com.lx.pl.dto.GenGenericPara;
import com.lx.pl.exception.BadRequestException;
import com.lx.pl.exception.ServerInternalException;
import com.lx.pl.util.CommonUtils;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Random;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.FileCopyUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

@Slf4j
@Service
public class WorkflowService {

    @Value("${comfyui.host}")
    String comfyUIHost;

    @Value("${image.storage.path.prefix}")
    String storagePathPrefix;

    public static String extractUploadFilename(String json) {
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            JsonNode jsonNode = objectMapper.readTree(json);
            return jsonNode.get("name").asText();
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public static long getSeed(long seed) {
        if (seed == -1) {
            Random random = new Random();
            long min = 100000000000000L; // 最小的 15 位数
            long max = 999999999999999L; // 最大的 15 位数

            return min + ((long) (random.nextDouble() * (max - min)));
        }
        return seed;
    }

    public static int generateFourDigitRandom() {
        Random random = new Random();
        return random.nextInt(9000) + 1000;
    }

    public static String loadWorkflowAsString(String fileName) throws IOException {
        ClassPathResource resource = new ClassPathResource("workflow/" + fileName);
        byte[] fileData = FileCopyUtils.copyToByteArray(resource.getInputStream());
        return new String(fileData, StandardCharsets.UTF_8);
    }

    public String loadWorkflow(GenGenericPara paras, String clientId) throws IOException {
        if (null != paras.getRembg()) {
            // 发现去背景参数，加载去背景工作流
            return loadWorkflowRembg(paras, clientId);
        }

        switch (paras.getModel()) {
            case "albedobaseXL_v21":
                if (paras.getHires_fix() == null) {
                    // txt to img
                    return loadAlbedobaseXLworkflow(paras, clientId);
                } else {
                    // hires fix
                    double scale = paras.getHires_fix().getScale();

                    if (scale <= 0.0f) {
                        throw new BadRequestException("must define the scale if you want use hires fix.");
                    }
                    if (scale > 2.0f) {
                        throw new BadRequestException("the scale can not greater than 2.0f.");
                    }
                    if (paras.getResolution().getWidth() * paras.getResolution().getHeight() > 1500000) {
                        throw new BadRequestException("目前SDXL高清修复最大总像素不能超过150万");
                    }

                    paras.getResolution().setWidth((int) (paras.getResolution().getWidth() * scale));
                    paras.getResolution().setHeight((int) (paras.getResolution().getHeight() * scale));

                    return loadAlbedobaseXLUpscaleworkflow(paras, clientId);

                }
            case "Animagine_XL_v3.1":
                // SDXL manga x1
                if (paras.getHires_fix() == null) {
                    // txt to img
                    return loadMangakaXLworkflow(paras, clientId);
                } else {
                    // hires fix
                    double scale = paras.getHires_fix().getScale();

                    if (scale <= 0.0f) {
                        throw new BadRequestException("must define the scale if you want use hires fix.");
                    }
                    if (scale > 2.0f) {
                        throw new BadRequestException("the scale can not greater than 2.0f.");
                    }
                    if (paras.getResolution().getWidth() * paras.getResolution().getHeight() > 1500000) {
                        throw new BadRequestException("目前SDXL高清修复最大总像素不能超过150万");
                    }

                    paras.getResolution().setWidth((int) (paras.getResolution().getWidth() * scale));
                    paras.getResolution().setHeight((int) (paras.getResolution().getHeight() * scale));

                    return loadMangakaXLUpscaleWorkflow(paras, clientId);

                }
            case "Lineart_XL_v1":
                // SDXL manga x1
                if (paras.getHires_fix() == null) {
                    // txt to img
                    return loadLineartXLworkflow(paras, clientId);
                } else {
                    // hires fix, 复用mangaka的工作流
                    double scale = paras.getHires_fix().getScale();

                    if (scale <= 0.0f) {
                        throw new BadRequestException("must define the scale if you want use hires fix.");
                    }
                    if (scale > 2.0f) {
                        throw new BadRequestException("the scale can not greater than 2.0f.");
                    }
                    if (paras.getResolution().getWidth() * paras.getResolution().getHeight() > 1500000) {
                        throw new BadRequestException("目前SDXL高清修复最大总像素不能超过150万");
                    }

                    paras.getResolution().setWidth((int) (paras.getResolution().getWidth() * scale));
                    paras.getResolution().setHeight((int) (paras.getResolution().getHeight() * scale));

                    return loadLineartXLUpscaleWorkflow(paras, clientId);

                }
        }
        throw new ServerInternalException("未找到对应的模型：" + paras.getModel());
    }

    private String loadAlbedobaseXLUpscaleworkflow(GenGenericPara paras, String clientId) throws IOException {
        // prepare the image
        String filePath = storagePathPrefix + paras.getHires_fix().getFileName();

        String serverUrl = comfyUIHost + "/upload/image";

        String response = uploadFileToServer(filePath, serverUrl, "");
        log.info("upscale background, file uploaded: {}", filePath);

        String fileName = extractUploadFilename(response);
        log.info("uploaded file name: {}", fileName);

        String workFlow = loadWorkflowAsString("albedobaseXL_v21_upscale.json");
        ObjectMapper objectMapper = new ObjectMapper();
        // 将 JSON 字符串解析为 JsonNode 对象
        JsonNode jsonNode = objectMapper.readTree(workFlow);

        // 遍历 JSON 数据中的属性
        jsonNode.fields().forEachRemaining(entry -> {
            String key = entry.getKey();
            JsonNode value = entry.getValue();
            ObjectNode n = (ObjectNode) value.get("inputs");

            // 在这里处理每个属性的逻辑
            switch (key) {
                case "78": // seed
                    n.put("seed", getSeed(paras.getSeed()));
                    break;
                case "79": // positive prompt
                    n.put("string", CommonUtils.removeEscapeCharacters(paras.getPrompt()));
                    break;
                case "81": // negative prompt
                    n.put("string", CommonUtils.removeEscapeCharacters(paras.getNegative_prompt()));
                    break;
                case "82": // width
                    n.put("int", paras.getResolution().getWidth());
                    break;
                case "83": // height
                    n.put("int", paras.getResolution().getHeight());
                    break;
                case "85": // steps
//          n.put("int", paras.getSteps());
                    n.put("int", 12); // upscale use 12 to reduce GPU cost
                    break;
                case "84": // cfg
                    n.put("float", paras.getCfg());
                    break;
                case "77":
                    n.put("scheduler", paras.getScheduler());
                    break;
                case "76":
                    n.put("sampler_name", paras.getSampler_name());
                    break;
                case "86":
                    if (paras.getModel_ability() != null) {
                        n.put("strength_model", paras.getModel_ability().getAnime_style_control());
                        n.put("strength_clip", paras.getModel_ability().getAnime_style_control());
                    }
                    break;
                case "3":
                    n.put("denoise", paras.getHires_fix().getDenoise());
                    break;
                case "90":
                    double scale = paras.getHires_fix().getScale();
                    double aimScale = scale / 4;
                    n.put("scale_by", aimScale);
                    break;
                case "87":
                    n.put("image", fileName);
                    break;
            }
        });

        ObjectNode rootNode = objectMapper.createObjectNode();
        rootNode.set("client_id", TextNode.valueOf(clientId));
        rootNode.set("prompt", jsonNode);

        return objectMapper.writeValueAsString(rootNode);
    }

    private String loadAlbedobaseXLworkflow(GenGenericPara paras, String clientId) throws IOException {
        String workFlow = loadWorkflowAsString("albedobaseXL_v21.json");
        ObjectMapper objectMapper = new ObjectMapper();
        // 将 JSON 字符串解析为 JsonNode 对象
        JsonNode jsonNode = objectMapper.readTree(workFlow);

        // 遍历 JSON 数据中的属性
        jsonNode.fields().forEachRemaining(entry -> {
            String key = entry.getKey();
            JsonNode value = entry.getValue();
            ObjectNode n = (ObjectNode) value.get("inputs");

            // 在这里处理每个属性的逻辑
            switch (key) {
                case "78": // seed
                    n.put("seed", getSeed(paras.getSeed()));
                    break;
                case "79": // positive prompt
                    n.put("string", CommonUtils.removeEscapeCharacters(paras.getPrompt()));
                    break;
                case "81": // negative prompt
                    n.put("string", CommonUtils.removeEscapeCharacters(paras.getNegative_prompt()));
                    break;
                case "82": // width
                    n.put("int", paras.getResolution().getWidth());
                    break;
                case "83": // height
                    n.put("int", paras.getResolution().getHeight());
                    break;
                case "5": // batch size
                    n.put("batch_size", paras.getResolution().getBatch_size());
                    break;
                case "85": // steps
                    n.put("int", paras.getSteps());
                    break;
                case "84": // cfg
                    n.put("float", paras.getCfg());
                    break;
                case "77":
                    n.put("scheduler", paras.getScheduler());
                    break;
                case "76":
                    n.put("sampler_name", paras.getSampler_name());
                    break;
                case "86":
                    if (paras.getModel_ability() != null) {
                        n.put("strength_model", paras.getModel_ability().getAnime_style_control());
                        n.put("strength_clip", paras.getModel_ability().getAnime_style_control());
                    }
                    break;
            }
        });

        ObjectNode rootNode = objectMapper.createObjectNode();
        rootNode.set("client_id", TextNode.valueOf(clientId));
        rootNode.set("prompt", jsonNode);

        return objectMapper.writeValueAsString(rootNode);
    }

    private String loadMangakaXLworkflow(GenGenericPara paras, String clientId) throws IOException {
        String workFlow = loadWorkflowAsString("mangaka_xl_v1.json");
        ObjectMapper objectMapper = new ObjectMapper();
        // 将 JSON 字符串解析为 JsonNode 对象
        JsonNode jsonNode = objectMapper.readTree(workFlow);

        // 遍历 JSON 数据中的属性
        jsonNode.fields().forEachRemaining(entry -> {
            String key = entry.getKey();
            JsonNode value = entry.getValue();
            ObjectNode n = (ObjectNode) value.get("inputs");

            // 在这里处理每个属性的逻辑
            switch (key) {
                case "78": // seed
                    n.put("seed", getSeed(paras.getSeed()));
                    break;
                case "79": // positive prompt
                    n.put("string", CommonUtils.removeEscapeCharacters(paras.getPrompt()));
                    break;
                case "81": // negative prompt
                    n.put("string", CommonUtils.removeEscapeCharacters(paras.getNegative_prompt()));
                    break;
                case "82": // width
                    n.put("int", paras.getResolution().getWidth());
                    break;
                case "83": // height
                    n.put("int", paras.getResolution().getHeight());
                    break;
                case "5": // batch size
                    n.put("batch_size", paras.getResolution().getBatch_size());
                    break;
                case "85": // steps
                    n.put("int", paras.getSteps());
                    break;
                case "84": // cfg
                    n.put("float", paras.getCfg());
                    break;
                case "77":
                    n.put("scheduler", paras.getScheduler());
                    break;
                case "76":
                    n.put("sampler_name", paras.getSampler_name());
                    break;
                case "86":
                    if (paras.getModel_ability() != null) {
                        n.put("strength_model", paras.getModel_ability().getAnime_style_control());
                        n.put("strength_clip", paras.getModel_ability().getAnime_style_control());
                    }
                    break;
            }
        });

        ObjectNode rootNode = objectMapper.createObjectNode();
        rootNode.set("client_id", TextNode.valueOf(clientId));
        rootNode.set("prompt", jsonNode);

        return objectMapper.writeValueAsString(rootNode);
    }

    private String loadMangakaXLUpscaleWorkflow(GenGenericPara paras, String clientId) throws IOException {
        // prepare the image
        String filePath = storagePathPrefix + paras.getHires_fix().getFileName();

        String serverUrl = comfyUIHost + "/upload/image";

        String response = uploadFileToServer(filePath, serverUrl, "");
        log.info("upscale background, file uploaded: {}", filePath);

        String fileName = extractUploadFilename(response);
        log.info("uploaded file name: {}", fileName);

        String workFlow = loadWorkflowAsString("mangaka_xl_v1_upscale.json");
        ObjectMapper objectMapper = new ObjectMapper();
        // 将 JSON 字符串解析为 JsonNode 对象
        JsonNode jsonNode = objectMapper.readTree(workFlow);

        // 遍历 JSON 数据中的属性
        jsonNode.fields().forEachRemaining(entry -> {
            String key = entry.getKey();
            JsonNode value = entry.getValue();
            ObjectNode n = (ObjectNode) value.get("inputs");

            // 在这里处理每个属性的逻辑
            switch (key) {
                case "78": // seed
                    n.put("seed", getSeed(paras.getSeed()));
                    break;
                case "79": // positive prompt
                    n.put("string", CommonUtils.removeEscapeCharacters(paras.getPrompt()));
                    break;
                case "81": // negative prompt
                    n.put("string", CommonUtils.removeEscapeCharacters(paras.getNegative_prompt()));
                    break;
                case "82": // width
                    n.put("int", paras.getResolution().getWidth());
                    break;
                case "83": // height
                    n.put("int", paras.getResolution().getHeight());
                    break;
                case "85": // steps
//          n.put("int", paras.getSteps());
                    n.put("int", 12); // upscale use 12 to reduce GPU cost
                    break;
                case "84": // cfg
                    n.put("float", paras.getCfg());
                    break;
                case "77":
                    n.put("scheduler", paras.getScheduler());
                    break;
                case "76":
                    n.put("sampler_name", paras.getSampler_name());
                    break;
                case "86":
                    if (paras.getModel_ability() != null) {
                        n.put("strength_model", paras.getModel_ability().getAnime_style_control());
                        n.put("strength_clip", paras.getModel_ability().getAnime_style_control());
                    }
                    break;
                case "3":
                    n.put("denoise", paras.getHires_fix().getDenoise());
                    break;
                case "90":
                    double scale = paras.getHires_fix().getScale();
                    double aimScale = scale / 4;
                    n.put("scale_by", aimScale);
                    break;
                case "87":
                    n.put("image", fileName);
                    break;
            }
        });

        ObjectNode rootNode = objectMapper.createObjectNode();
        rootNode.set("client_id", TextNode.valueOf(clientId));
        rootNode.set("prompt", jsonNode);

        return objectMapper.writeValueAsString(rootNode);
    }

    private String loadLineartXLworkflow(GenGenericPara paras, String clientId) throws IOException {
        String workFlow = loadWorkflowAsString("lineart_xl_v1.json");
        ObjectMapper objectMapper = new ObjectMapper();
        // 将 JSON 字符串解析为 JsonNode 对象
        JsonNode jsonNode = objectMapper.readTree(workFlow);

        // 遍历 JSON 数据中的属性
        jsonNode.fields().forEachRemaining(entry -> {
            String key = entry.getKey();
            JsonNode value = entry.getValue();
            ObjectNode n = (ObjectNode) value.get("inputs");

            // 在这里处理每个属性的逻辑
            switch (key) {
                case "78": // seed
                    n.put("seed", getSeed(paras.getSeed()));
                    break;
                case "79": // positive prompt
                    n.put("string", CommonUtils.removeEscapeCharacters(paras.getPrompt()));
                    break;
                case "81": // negative prompt
                    n.put("string", CommonUtils.removeEscapeCharacters(paras.getNegative_prompt()));
                    break;
                case "82": // width
                    n.put("int", paras.getResolution().getWidth());
                    break;
                case "83": // height
                    n.put("int", paras.getResolution().getHeight());
                    break;
                case "5": // batch size
                    n.put("batch_size", paras.getResolution().getBatch_size());
                    break;
                case "85": // steps
                    n.put("int", paras.getSteps());
                    break;
                case "84": // cfg
                    n.put("float", paras.getCfg());
                    break;
                case "77":
                    n.put("scheduler", paras.getScheduler());
                    break;
                case "76":
                    n.put("sampler_name", paras.getSampler_name());
                    break;
            }
        });

        ObjectNode rootNode = objectMapper.createObjectNode();
        rootNode.set("client_id", TextNode.valueOf(clientId));
        rootNode.set("prompt", jsonNode);

        return objectMapper.writeValueAsString(rootNode);
    }

    private String loadLineartXLUpscaleWorkflow(GenGenericPara paras, String clientId) throws IOException {
        // prepare the image
        String filePath = storagePathPrefix + paras.getHires_fix().getFileName();

        String serverUrl = comfyUIHost + "/upload/image";

        String response = uploadFileToServer(filePath, serverUrl, "");
        log.info("upscale background, file uploaded: {}", filePath);

        String fileName = extractUploadFilename(response);
        log.info("uploaded file name: {}", fileName);

        String workFlow = loadWorkflowAsString("lineart_xl_v1_upscale.json");
        ObjectMapper objectMapper = new ObjectMapper();
        // 将 JSON 字符串解析为 JsonNode 对象
        JsonNode jsonNode = objectMapper.readTree(workFlow);

        // 遍历 JSON 数据中的属性
        jsonNode.fields().forEachRemaining(entry -> {
            String key = entry.getKey();
            JsonNode value = entry.getValue();
            ObjectNode n = (ObjectNode) value.get("inputs");

            // 在这里处理每个属性的逻辑
            switch (key) {
                case "78": // seed
                    n.put("seed", getSeed(paras.getSeed()));
                    break;
                case "79": // positive prompt
                    n.put("string", CommonUtils.removeEscapeCharacters(paras.getPrompt()));
                    break;
                case "81": // negative prompt
                    n.put("string", CommonUtils.removeEscapeCharacters(paras.getNegative_prompt()));
                    break;
                case "82": // width
                    n.put("int", paras.getResolution().getWidth());
                    break;
                case "83": // height
                    n.put("int", paras.getResolution().getHeight());
                    break;
                case "85": // steps
//          n.put("int", paras.getSteps());
                    n.put("int", 12); // upscale use 12 to reduce GPU cost
                    break;
                case "84": // cfg
                    n.put("float", paras.getCfg());
                    break;
                case "77":
                    n.put("scheduler", paras.getScheduler());
                    break;
                case "76":
                    n.put("sampler_name", paras.getSampler_name());
                    break;
                case "86":
                    if (paras.getModel_ability() != null) {
                        n.put("strength_model", paras.getModel_ability().getAnime_style_control());
                        n.put("strength_clip", paras.getModel_ability().getAnime_style_control());
                    }
                    break;
                case "3":
                    n.put("denoise", paras.getHires_fix().getDenoise());
                    break;
                case "90":
                    double scale = paras.getHires_fix().getScale();
                    double aimScale = scale / 4;
                    n.put("scale_by", aimScale);
                    break;
                case "87":
                    n.put("image", fileName);
                    break;
            }
        });

        ObjectNode rootNode = objectMapper.createObjectNode();
        rootNode.set("client_id", TextNode.valueOf(clientId));
        rootNode.set("prompt", jsonNode);

        return objectMapper.writeValueAsString(rootNode);
    }

    private String loadWorkflowRembg(GenGenericPara paras, String clientId) throws IOException {
        // prepare the image
        String filePath = storagePathPrefix + paras.getRembg().getFileName();

        String serverUrl = comfyUIHost + "/upload/image";

        String response = uploadFileToServer(filePath, serverUrl, "");
        log.info("remove background, file uploaded: {}", filePath);

        String fileName = extractUploadFilename(response);
        log.info("uploaded file name: {}", fileName);

        String workFlow = loadWorkflowAsString("rembg_anime.json");

        ObjectMapper objectMapper = new ObjectMapper();
        // 将 JSON 字符串解析为 JsonNode 对象
        JsonNode jsonNode = objectMapper.readTree(workFlow);

        // 遍历 JSON 数据中的属性
        jsonNode.fields().forEachRemaining(entry -> {
            String key = entry.getKey();
            JsonNode value = entry.getValue();
            ObjectNode n = (ObjectNode) value.get("inputs");

            // 在这里处理每个属性的逻辑
            switch (key) {
                case "2": // seed
                    n.put("image", fileName);
                    break;
            }
        });

        ObjectNode rootNode = objectMapper.createObjectNode();
        rootNode.set("client_id", TextNode.valueOf(clientId));
        rootNode.set("prompt", jsonNode);

        return objectMapper.writeValueAsString(rootNode);
    }

    public String uploadFileToServer(String imageFilePath, String serverUrl, String subfolder) {
        RestTemplate restTemplate = new RestTemplate();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);

        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add("subfolder", subfolder);
        body.add("image", new FileSystemResource(new File(imageFilePath)));

        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);

        return restTemplate.postForObject(serverUrl, requestEntity, String.class);
    }

    private void setDefaultPara(GenGenericPara paras) {
        if (!StringUtils.hasLength(paras.getSampler_name())) {
            paras.setSampler_name("euler_ancestral");
        }
        if (!StringUtils.hasLength(paras.getScheduler())) {
            paras.setScheduler("karras");
        }
        if (paras.getCfg() == 0) {
            paras.setCfg(6D);
        }
        if (paras.getSeed() == 0) {
            paras.setSeed(new Long(-1));
        }
        if (paras.getSteps() == 0) {
            paras.setSteps(20);
        }
    }

//  private String loadWorkflowSDXLBase2(GenGenericPara paras) throws IOException {
//    String wf = loadWorkflowAsString("workflow_sdxl_base.json");
//    wf = wf.replaceAll("123456789", getSeed(-1) + "").
//        replaceAll("1girl, bare shoulders", CommonUtils.removeEscapeCharacters(paras.getPrompt())).
//        replaceAll("bad_image", CommonUtils.removeEscapeCharacters(paras.getNegative_prompt())).
//        replaceAll("832", String.valueOf(paras.getResolution().getWidth())).
//        replaceAll("\"height\": 1216", "\"height\": " + paras.getResolution().getHeight()).
//        replaceAll("\"batch_size\": 1", "\"batch_size\": " + paras.getResolution().getBatch_size()).
//        replaceAll("\"filename_prefix\": \"mts\"",
//            "\"filename_prefix\": \"mts" + "_" + generateFourDigitRandom() + "\"");
//    wf = "{ \"client_id\": \"" + getClientId() + "\", \"prompt\": " + wf + "}";
//    return wf;
//  }

//  private String loadWorkflowSDXLUpscale(GenGenericPara paras) throws IOException {
//    // prepare the image
//    String filePath = storagePathPrefix + paras.getHires_fix().getFileName();
//    log.info("img upscale, file path: {}", filePath);
//
//    String serverUrl = comfyUIHost + "/upload/image";
//
//    String response = uploadFileToServer(filePath, serverUrl, "");
//    String fileName = extractUploadFilename(response);
//
//    String workFlow = loadWorkflowAsString("workflow_sdxl_upscale.json");
//
//    double scaleBy = paras.getHires_fix().getScale() / 4;
//    DecimalFormat df = new DecimalFormat("#.##");
//
//    workFlow = workFlow.replaceAll("\"scale_by\": 0.38",
//        "\"scale_by\": " + df.format(scaleBy));
//    workFlow = workFlow.replaceAll("ComfyUI_00055_.png", fileName);
//    workFlow = workFlow.replaceAll("830101890083637", getSeed(-1) + "");
//    workFlow = workFlow.replaceAll("1girl, aerith gainsborough",
//        CommonUtils.removeEscapeCharacters(paras.getPrompt()));
//    workFlow = workFlow.replaceAll(
//        "embedding:EasyNegativeV2, embedding:badhandv4, embedding:verybadimagenegative_v1.3",
//        CommonUtils.removeEscapeCharacters(paras.getNegative_prompt()));
//    workFlow = workFlow.replaceAll("\"filename_prefix\": \"mts\"",
//        "\"filename_prefix\": \"mts" + "_" + generateFourDigitRandom() + "\"");
//    workFlow = "{ \"client_id\": \"" + getClientId() + "\", \"prompt\": " + workFlow + "}";
//    return workFlow;
//  }

//  private String loadWorkflowSD15Base(GenGenericPara paras) throws IOException {
//    String workFlow = loadWorkflowAsString("workflow_sd15_base.json");
//    ObjectMapper objectMapper = new ObjectMapper();
//    // 将 JSON 字符串解析为 JsonNode 对象
//    JsonNode jsonNode = objectMapper.readTree(workFlow);
//
//    // 遍历 JSON 数据中的属性
//    jsonNode.fields().forEachRemaining(entry -> {
//      String key = entry.getKey();
//      JsonNode value = entry.getValue();
//      ObjectNode n = (ObjectNode) value.get("inputs");
//
//      // 在这里处理每个属性的逻辑
//      switch (key) {
//        case "80": // seed
//          n.put("seed", getSeed(paras.getSeed()));
//          break;
//        case "78": // positive prompt
//          n.put("string", CommonUtils.removeEscapeCharacters(paras.getPrompt()));
//          break;
//        case "79": // negative prompt
//          n.put("string", CommonUtils.removeEscapeCharacters(paras.getNegative_prompt()));
//          break;
//        case "81": // width
//          n.put("int", paras.getResolution().getWidth());
//          break;
//        case "82": // height
//          n.put("int", paras.getResolution().getHeight());
//          break;
//        case "5": // batch size
//          n.put("batch_size", paras.getResolution().getBatch_size());
//          break;
//        case "75": // steps
//          n.put("int", paras.getSteps());
//          break;
//        case "76": // cfg
//          n.put("float", paras.getCfg());
//          break;
//        case "3":
//          n.put("sampler_name", paras.getSampler_name());
//          n.put("scheduler", paras.getScheduler());
//          break;
//      }
//    });
//
//    ObjectNode rootNode = objectMapper.createObjectNode();
//    rootNode.set("client_id", TextNode.valueOf(getClientId()));
//    rootNode.set("prompt", jsonNode);
//
//    return objectMapper.writeValueAsString(rootNode);
//  }

//  private String loadWorkflowSD15BaseUpscale(GenGenericPara paras) throws IOException {
//    String workFlow = loadWorkflowAsString("workflow_sd15_base_upscale.json");
//    ObjectMapper objectMapper = new ObjectMapper();
//    // 将 JSON 字符串解析为 JsonNode 对象
//    JsonNode jsonNode = objectMapper.readTree(workFlow);
//
//    // 遍历 JSON 数据中的属性
//    jsonNode.fields().forEachRemaining(entry -> {
//      String key = entry.getKey();
//      JsonNode value = entry.getValue();
//      ObjectNode n = (ObjectNode) value.get("inputs");
//
//      // 在这里处理每个属性的逻辑
//      switch (key) {
//        case "80": // seed
//          n.put("seed", getSeed(paras.getSeed()));
//          break;
//        case "78": // positive prompt
//          n.put("string", CommonUtils.removeEscapeCharacters(paras.getPrompt()));
//          break;
//        case "79": // negative prompt
//          n.put("string", CommonUtils.removeEscapeCharacters(paras.getNegative_prompt()));
//          break;
//        case "81": // width
//          n.put("int", paras.getResolution().getWidth());
//          break;
//        case "82": // height
//          n.put("int", paras.getResolution().getHeight());
//          break;
//        case "5": // batch size
//          n.put("batch_size", paras.getResolution().getBatch_size());
//          break;
//        case "75": // steps
//          n.put("int", paras.getSteps());
//          break;
//        case "76": // cfg
//          n.put("float", paras.getCfg());
//          break;
//        case "3":
//          n.put("sampler_name", paras.getSampler_name());
//          n.put("scheduler", paras.getScheduler());
//          break;
//      }
//    });
//
//    ObjectNode rootNode = objectMapper.createObjectNode();
//    rootNode.set("client_id", TextNode.valueOf(getClientId()));
//    rootNode.set("prompt", jsonNode);
//
//    // 因为是upscale工作流，所以提示数据要更新分辨率到2倍
//    double scale = 2.0;
//    paras.getResolution().setWidth((int) (paras.getResolution().getWidth() * scale));
//    paras.getResolution().setHeight((int) (paras.getResolution().getHeight() * scale));
//
//    return objectMapper.writeValueAsString(rootNode);
//  }

//  private String loadWorkflowSD15Upscale(GenGenericPara paras) throws IOException {
//    // prepare the image
//    String filePath = storagePathPrefix + paras.getHires_fix().getFileName();
//    log.info("img upscale, file path: {}", filePath);
//
//    String serverUrl = comfyUIHost + "/upload/image";
//
//    String response = uploadFileToServer(filePath, serverUrl, "");
//    String fileName = extractUploadFilename(response);
//
//    String workFlow = loadWorkflowAsString("workflow_sd15_upscale.json");
//    workFlow = workFlow.replaceAll("\"scale_by\": 0.5",
//        "\"scale_by\": " + paras.getHires_fix().getScale() / 4);
//    workFlow = workFlow.replaceAll("ComfyUI_temp_zcppx_00001_.png", fileName);
//    workFlow = workFlow.replaceAll("1040405478324097", getSeed(-1) + "");
//    workFlow = workFlow.replaceAll("1girl, aerith gainsborough",
//        CommonUtils.removeEscapeCharacters(paras.getPrompt()));
//    workFlow = workFlow.replaceAll(
//        "embedding:EasyNegativeV2, embedding:badhandv4, embedding:verybadimagenegative_v1.3",
//        CommonUtils.removeEscapeCharacters(paras.getNegative_prompt()));
//    workFlow = workFlow.replaceAll("\"filename_prefix\": \"mts\"",
//        "\"filename_prefix\": \"mts" + "_" + generateFourDigitRandom() + "\"");
//    workFlow = "{ \"client_id\": \"" + getClientId() + "\", \"prompt\": " + workFlow + "}";
//    return workFlow;
//  }
}
