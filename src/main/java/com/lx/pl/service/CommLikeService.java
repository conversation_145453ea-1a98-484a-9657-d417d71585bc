package com.lx.pl.service;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.lx.pl.db.mysql.community.entity.*;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.db.mysql.gen.mapper.PromptFileMapper;
import com.lx.pl.db.mysql.gen.repository.CommCommentLikeRepository;
import com.lx.pl.db.mysql.gen.repository.CommLikeRepository;
import com.lx.pl.dto.CommHistoryLike;
import com.lx.pl.dto.CommPageInfo;
import com.lx.pl.exception.ServerInternalException;
import com.mongodb.client.result.UpdateResult;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.TextCriteria;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.lx.pl.service.CommMessageService.COMM_USER_NOT_READ_MESSAGE;
import static com.lx.pl.service.CommMessageService.NOT_READ_LIKE_NUMS;

@Service
@Slf4j
public class CommLikeService {

    @Autowired
    private CommLikeRepository commLikeRepository;

    @Autowired
    private CommCommentLikeRepository commCommentLikeRepository;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private CommImgService commImgService;

    @Autowired
    private CommCommentService commCommentService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private PromptFileMapper promptFileMapper;

    @Autowired
    private RedisService redisService;

    @Autowired
    private CommMessageService commMessageService;
    @Autowired
    private FileScoreService fileScoreService;

    /**
     * 用户对社区图片进行点赞
     *
     * @param commFileId
     * @param user
     * @return
     */
    public Boolean addLikeCommFile(String commFileId, User user) {
        RLock lock = redissonClient.getLock("file:like:" + commFileId);
        try {
            lock.lock();
            CommFile commFile = commImgService.getCommFileById(commFileId);
            if (!Objects.isNull(commFile)) {
                CommLike commLike = new CommLike();
                commLike.setFileId(commFileId);
                commLike.setPrompt(commFile.getPrompt());
                commLike.setTags(commFile.getTags());
                commLike.setCreateTime(LocalDateTime.now());
                //点赞目标用户
                AccountInfo accountInfo = new AccountInfo();
                BeanUtils.copyProperties(commFile.getAccountInfo(), accountInfo);
                commLike.setTargetAcc(accountInfo);
                //点赞发起者用户
                AccountInfo replyAccInfo = new AccountInfo();
                replyAccInfo.setUserId(user.getId());
                replyAccInfo.setUserName(user.getUserName());
                replyAccInfo.setUserLoginName(user.getLoginName());
                replyAccInfo.setUserAvatarUrl(user.getAvatarUrl());
                replyAccInfo.setWhetherPro(Boolean.FALSE);
                commLike.setOwnerAcc(replyAccInfo);
                commLike.setRead(Boolean.FALSE);
                commLikeRepository.insert(commLike);

//                int interactionScore = fileScoreService.calculateInteractionScore(commFile.getFileLikeNums(), commFile.getRemixNums(), commFile.getFileCommentNums(), commFile.getShareNums());
//                commFile.setFileLikeNums(commFile.getFileLikeNums() + 1);
//                fileScoreService.updateSingleFileTrendingScore(commFile, interactionScore);
                fileScoreService.sendFlushMessage(commFileId, commFile.getTrendingScore() == null ? 0 : commFile.getTrendingScore());
                //图片点赞数据+1
                mongoTemplate.updateFirst(
                        new Query(Criteria.where("id").is(commFileId)),
                        new Update().inc("fileLikeNums", 1),
                        CommFile.class
                );

                //用户点赞数据+1
                mongoTemplate.updateFirst(
                        new Query(Criteria.where("accountInfo.userId").is(accountInfo.getUserId())),
                        new Update().inc("likeNums", 1),
                        CommUser.class
                );

                //模板用户的未读数增加1
                redisService.incrementFieldInHash(COMM_USER_NOT_READ_MESSAGE + accountInfo.getUserLoginName(), NOT_READ_LIKE_NUMS, 1);

                //点赞用户lumen任务数据+1
                mongoTemplate.updateFirst(
                        new Query(Criteria.where("userId").is(user.getId())),
                        new Update().inc("likeNums", 1),
                        TaskLumen.class
                );

                //目标用户lumen任务数据+1
                mongoTemplate.updateFirst(
                        new Query(Criteria.where("userId").is(accountInfo.getUserId())),
                        new Update().inc("receiveLikeNums", 1),
                        TaskLumen.class
                );

                //每日点赞用户lumen任务数据+1
                mongoTemplate.updateFirst(
                        new Query(Criteria.where("userId").is(user.getId())),
                        new Update().inc("dailyLikeNums", 1),
                        DailyTaskLumen.class
                );

                return Boolean.TRUE;
            }
        } catch (Exception e) {
            log.error("用户：{} 对图片：{} 点赞报错", user.getId(), commFileId, e);
            throw new ServerInternalException("Like Failed");
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return Boolean.FALSE;
    }

    /**
     * 用户对图片取消点赞
     *
     * @param commFileId
     * @param user
     * @return
     */
    public Boolean reduceLikeCommFile(String commFileId, CommLike commLike, User user) {
        RLock lock = redissonClient.getLock("file:like:" + user.getId());
        try {
            lock.lock();

            Query query = new Query();
            query.addCriteria(Criteria.where("ownerAcc.userId").is(user.getId())
                    .and("fileId").is(commFileId));
            CommFile commFile = commImgService.getCommFileById(commFileId);
//            int interactionScore = fileScoreService.calculateInteractionScore(commFile.getFileLikeNums(), commFile.getRemixNums(), commFile.getFileCommentNums(), commFile.getShareNums());
//            commFile.setFileLikeNums(commFile.getFileLikeNums() - 1);
//            fileScoreService.updateSingleFileTrendingScore(commFile, interactionScore);
            fileScoreService.sendFlushMessage(commFileId, commFile.getTrendingScore());
            //图片点赞数据-1
            mongoTemplate.updateFirst(
                    new Query(Criteria.where("id").is(commFileId)),
                    new Update().inc("fileLikeNums", -1), CommFile.class
            );

            //用户点赞数据-1
            mongoTemplate.updateFirst(
                    new Query(Criteria.where("accountInfo.userId").is(commLike.getTargetAcc().getUserId())),
                    new Update().inc("likeNums", -1),
                    CommUser.class
            );

            //用户的未读数增减1
            commMessageService.decrementFieldSafely(COMM_USER_NOT_READ_MESSAGE + commLike.getTargetAcc().getUserLoginName(), NOT_READ_LIKE_NUMS);

//            //点赞用户lumen任务数据-1
//            mongoTemplate.updateFirst(
//                    new Query(Criteria.where("userId").is(user.getId())),
//                    new Update().inc("likeNums", -1),
//                    TaskLumen.class
//            );
//
//            //目标用户lumen任务数据-1
//            mongoTemplate.updateFirst(
//                    new Query(Criteria.where("userId").is(commLike.getTargetAcc().getUserId())),
//                    new Update().inc("receiveLikeNums", -1),
//                    TaskLumen.class
//            );
//
//            //每日点赞用户lumen任务数据-1
//            mongoTemplate.updateFirst(
//                    new Query(Criteria.where("userId").is(user.getId())),
//                    new Update().inc("dailyLikeNums", -1),
//                    DailyTaskLumen.class
//            );


            // 执行删除操作并判断是否删除了至少一个文档
            return mongoTemplate.remove(query, CommLike.class).getDeletedCount() > 0;
        } catch (Exception e) {
            log.error("用户：{} 对图片：{} 取消点赞报错", user.getId(), commFileId, e);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return Boolean.FALSE;
    }

    /**
     * 对社区评论进行点赞
     *
     * @param commFileId
     * @param commentId
     * @param user
     * @return
     */
    public Boolean addLikeCommComment(String commFileId, String commentId, User user) {
        RLock lock = redissonClient.getLock("comment:like:" + commFileId);
        try {
            lock.lock();
            CommComment commComment = commCommentService.getCommCommentById(commentId);
            if (!Objects.isNull(commComment)) {
                CommCommentLike commCommentLike = new CommCommentLike();
                commCommentLike.setFileId(commFileId);
                commCommentLike.setCommentId(commentId);
                commCommentLike.setCreateTime(LocalDateTime.now());
                //点赞目标用户
                AccountInfo accountInfo = new AccountInfo();
                BeanUtils.copyProperties(commComment.getOwnerAcc(), accountInfo);
                commCommentLike.setTargetAcc(accountInfo);
                //点赞发起者用户
                AccountInfo replyAccInfo = new AccountInfo();
                replyAccInfo.setUserId(user.getId());
                commCommentLike.setOwnerAcc(replyAccInfo);
                commCommentLikeRepository.insert(commCommentLike);

                //图片点赞数据+1
                mongoTemplate.updateFirst(
                        new Query(Criteria.where("id").is(commentId)),
                        new Update().inc("commentLikeNums", 1),
                        CommComment.class
                );

                return Boolean.TRUE;
            }
        } catch (Exception e) {
            log.error("用户：{} 对评论：{} 点赞报错", user.getId(), commentId, e);
            throw new ServerInternalException("Like Failed");
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

        return Boolean.FALSE;
    }


    public Boolean reduceLikeCommComment(String commFileId, String commentId, User user) {
        RLock lock = redissonClient.getLock("comment:like:" + commFileId);
        try {
            lock.lock();
            Query query = new Query();
            CommComment commComment = commCommentService.getCommCommentById(commentId);
            query.addCriteria(Criteria.where("ownerAcc.userId").is(user.getId())
                    .and("commentId").is(commentId));

            if (!Objects.isNull(commComment)) {
                //评论点赞数据 - 1
                mongoTemplate.updateFirst(
                        new Query(Criteria.where("id").is(commentId)),
                        new Update().inc("commentLikeNums", -1),
                        CommComment.class
                );
            }

            // 执行删除操作并判断是否删除了至少一个文档
            return mongoTemplate.remove(query, CommLike.class).getDeletedCount() > 0;
        } catch (Exception e) {
            log.error("用户：{} 对评论：{} 取消点赞报错", user.getId(), commFileId, e);
            throw new ServerInternalException("Reduce Comment Like Failed");
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 查询点赞列表
     *
     * @param commFileId
     * @param commentId
     * @param user
     * @return
     */
    public CommPageInfo<CommLike> getCommLikeList(String lastLikeId, Integer pageSize, String commFileId, String commentId, User user) {
        List<CommLike> commLikeList = new ArrayList<>();
        try {
            Query query = new Query();
            query.addCriteria(Criteria.where("fileId").is(commFileId));

            if (StringUtil.isNotBlank(commentId)) {
                query.addCriteria(Criteria.where("commentId").in(commentId));
            }

            if (StringUtil.isNotBlank(lastLikeId)) {
                query.addCriteria(Criteria.where("id").lt(new ObjectId(lastLikeId)));
            }

            query.with(Sort.by(Sort.Direction.DESC, "id"));

            // 设置分页
            query.limit(pageSize);
            commLikeList = mongoTemplate.find(query, CommLike.class);
        } catch (Exception e) {
            log.error("获取社区图片点赞列表报错", e);
            throw new ServerInternalException("Error retrieving the community image likes list");
        }
        return buildPromptPageInfo(pageSize, commLikeList);
    }

    // 构建分页结果对象
    private CommPageInfo<CommLike> buildPromptPageInfo(Integer pageSize, List<CommLike> commLikeList) {
        CommPageInfo<CommLike> commPageInfo = new CommPageInfo<>();
        commPageInfo.setResultList(CollectionUtils.isEmpty(commLikeList) ? Collections.emptyList() : commLikeList);
        commPageInfo.setPageSize(pageSize);
        commPageInfo.setLastId(CollectionUtils.isEmpty(commLikeList) ? "" : commLikeList.get(commLikeList.size() - 1).getId());
        return commPageInfo;
    }

    /**
     * 根据 userId 查询点赞图片id集合
     *
     * @param userId 用户 ID
     */
    public List<CommLike> findLikeCommFileIdByUserId(String lastLikeId, Integer pageSize, List<String> tags, String vagueKey, Long userId) {
        // 构建查询条件，筛选 replyAccInfo.userId 为指定 userId 的文档
        Query query = new Query();
        query.addCriteria(Criteria.where("ownerAcc.userId").is(userId));

        // 如果提供了标签，则按标签进行过滤
        if (!CollectionUtils.isEmpty(tags)) {
            query.addCriteria(Criteria.where("tags").in(tags));
        }

        //模糊搜索关键字 全文索引
        if (StringUtil.isNotBlank(vagueKey)) {
            TextCriteria textCriteria = TextCriteria.forDefaultLanguage().matching(vagueKey);
            query.addCriteria(textCriteria);
        } else {
            // 游标分页：基于上一页最后一条记录的 ID
            if (StringUtil.isNotBlank(lastLikeId)) {
                query.addCriteria(Criteria.where("id").lt(new ObjectId(lastLikeId))); // 仅获取 ID 小于游标的记录
            }

            query.with(Sort.by(Sort.Direction.DESC, "id"));
        }

        query.limit(pageSize);
        List<CommLike> commLikes = mongoTemplate.find(query, CommLike.class);

        return commLikes;
    }

    /**
     * 更新点赞的用户名称
     *
     * @param userId
     * @param newUsername
     */
    public void updateUsernameByUserId(Long userId, String newUsername) {
        try {
            // 更新 targetAcc.userName
            Query targetAccQuery = new Query();
            targetAccQuery.addCriteria(Criteria.where("targetAcc.userId").is(userId));
            Update targetAccUpdate = new Update();
            targetAccUpdate.set("targetAcc.userName", newUsername);
            UpdateResult targetResult = mongoTemplate.updateMulti(targetAccQuery, targetAccUpdate, CommLike.class);

            log.info("点赞关注更新 targetAcc userName记录数：{}", targetResult.getModifiedCount());

            // 更新 ownerAcc.userName
            Query ownerAccQuery = new Query();
            ownerAccQuery.addCriteria(Criteria.where("ownerAcc.userId").is(userId));
            Update ownerAccUpdate = new Update();
            ownerAccUpdate.set("ownerAcc.userName", newUsername);
            UpdateResult ownerResult = mongoTemplate.updateMulti(ownerAccQuery, ownerAccUpdate, CommLike.class);

            log.info("点赞关注更新 ownerAcc userName 记录数：{}", ownerResult.getModifiedCount());
        } catch (Exception e) {
            log.error("更新用户：{} 的用户名称：{}报错", userId, newUsername, e);
            throw new ServerInternalException("Failed to update username");
        }
    }

    /**
     * 更新点赞的用户头像
     *
     * @param userId
     * @param newUserAvatarUrl
     */
    public void updateUserAvatarUrlByUserId(Long userId, String newUserAvatarUrl) {
        try {
            // 更新 targetAcc.userName
            Query targetAccQuery = new Query();
            targetAccQuery.addCriteria(Criteria.where("targetAcc.userId").is(userId));
            Update targetAccUpdate = new Update();
            targetAccUpdate.set("targetAcc.userAvatarUrl", newUserAvatarUrl);
            UpdateResult targetResult = mongoTemplate.updateMulti(targetAccQuery, targetAccUpdate, CommLike.class);

            log.info("点赞关注更新 targetAcc userAvatarUr 记录数：{}", targetResult.getModifiedCount());

            // 更新 ownerAcc.userName
            Query ownerAccQuery = new Query();
            ownerAccQuery.addCriteria(Criteria.where("ownerAcc.userId").is(userId));
            Update ownerAccUpdate = new Update();
            ownerAccUpdate.set("ownerAcc.userAvatarUrl", newUserAvatarUrl);
            UpdateResult ownerResult = mongoTemplate.updateMulti(ownerAccQuery, ownerAccUpdate, CommLike.class);

            log.info("点赞关注更新 ownerAcc userAvatarUr 记录数：{}", ownerResult.getModifiedCount());
        } catch (Exception e) {
            log.error("更新用户：{} 的用户头像：{}报错", userId, newUserAvatarUrl, e);
            throw new ServerInternalException("Failed to update user avatar");
        }
    }

    public CommLike getCommLikeByUserIdAndFileId(Long userId, String fileId) {
        try {
            Query query = new Query();
            query.addCriteria(Criteria.where("ownerAcc.userId").is(userId));
            query.addCriteria(Criteria.where("fileId").is(fileId));
            CommLike commLike = mongoTemplate.findOne(query, CommLike.class);
            return commLike;
        } catch (Exception e) {
            log.error("查询社区图片点赞报错，图片id为：{}，用户id:{}", fileId, userId, e);
            return null;
        }
    }

    public Boolean dealHistoryCommLike(String tableName) {
        Integer batchSize = 1000; // 每批次处理记录数
        Long lastId = null; // 游标起始点
        while (true) {
            // 查询数据，使用游标分页
            List<CommHistoryLike> records = promptFileMapper.getUserLikePromptFileListByCursor(lastId, batchSize, tableName);
            if (records.isEmpty()) {
                break; // 没有更多数据，退出循环
            }

            // 批量操作 MongoDB
            bulkProcessHistoryLikes(records);

            // 更新游标
            lastId = records.get(records.size() - 1).getOriginLikeId();
        }
        return Boolean.TRUE;
    }

    private void bulkProcessHistoryLikes(List<CommHistoryLike> records) {
        // 查询所有需要的 CommFile 数据
        List<String> promptFileIds = records.stream()
                .map(CommHistoryLike::getOriginImgId)
                .collect(Collectors.toList());
        List<CommFile> commFiles = mongoTemplate.find(
                new Query(Criteria.where("promptFileId").in(promptFileIds)),
                CommFile.class
        );
        Map<String, CommFile> commFileMap = commFiles.stream()
                .collect(Collectors.toMap(CommFile::getFileId, Function.identity()));

        // 构建需要插入的 CommLike 数据和需要更新的统计数据
        List<CommLike> commLikesToInsert = new ArrayList<>();
        Map<String, Long> fileLikeCountUpdates = new HashMap<>();
        Map<Long, Long> userLikeCountUpdates = new HashMap<>();

        for (CommHistoryLike commHistoryLike : records) {
            CommFile commFile = commFileMap.get(commHistoryLike.getOriginImgId());
            if (commFile != null) {
                // 构建 CommLike 数据
                CommLike commLike = new CommLike();
                commLike.setFileId(commFile.getId());
                commLike.setPrompt(commFile.getPrompt());
                commLike.setTags(commFile.getTags());
                commLike.setCreateTime(LocalDateTime.now());

                // 点赞目标用户
                AccountInfo accountInfo = new AccountInfo();
                BeanUtils.copyProperties(commFile.getAccountInfo(), accountInfo);
                commLike.setTargetAcc(accountInfo);

                // 点赞发起者用户
                AccountInfo replyAccInfo = new AccountInfo();
                replyAccInfo.setUserId(commHistoryLike.getUserId());
                replyAccInfo.setUserName(commHistoryLike.getUserName());
                replyAccInfo.setUserLoginName(commHistoryLike.getUserLoginName());
                replyAccInfo.setUserAvatarUrl(commHistoryLike.getUserAvatarUrl());
                replyAccInfo.setWhetherPro(Boolean.FALSE);
                commLike.setOwnerAcc(replyAccInfo);

                commLikesToInsert.add(commLike);

                // 统计更新数据
                fileLikeCountUpdates.put(commHistoryLike.getOriginImgId(),
                        fileLikeCountUpdates.getOrDefault(commHistoryLike.getOriginImgId(), 0L) + 1);
                userLikeCountUpdates.put(accountInfo.getUserId(),
                        userLikeCountUpdates.getOrDefault(accountInfo.getUserId(), 0L) + 1);
            }
        }

        // 批量插入 CommLike 数据
        if (!CollectionUtils.isEmpty(commLikesToInsert)) {
            commLikeRepository.insert(commLikesToInsert);
        }

        // 批量更新 MongoDB 统计数据
        bulkUpdateLikeCounts(fileLikeCountUpdates, userLikeCountUpdates);
    }

    private void bulkUpdateLikeCounts(Map<String, Long> fileLikeCountUpdates, Map<Long, Long> userLikeCountUpdates) {
        // 批量更新文件点赞数
        for (Map.Entry<String, Long> entry : fileLikeCountUpdates.entrySet()) {
            mongoTemplate.updateFirst(
                    new Query(Criteria.where("promptFileId").is(entry.getKey())),
                    new Update().inc("fileLikeNums", entry.getValue()),
                    CommFile.class
            );
        }

        // 批量更新用户点赞数
        for (Map.Entry<Long, Long> entry : userLikeCountUpdates.entrySet()) {
            mongoTemplate.updateFirst(
                    new Query(Criteria.where("accountInfo.userId").is(entry.getKey())),
                    new Update().inc("likeNums", entry.getValue()),
                    CommUser.class
            );
        }
    }


    public Boolean readCommLike(String commLikeId, User user) {
        RLock lock = redissonClient.getLock("read:like:" + user.getId());
        try {
            //修改所有满足条件的
            if (StringUtil.isBlank(commLikeId)) {
                mongoTemplate.updateMulti(
                        new Query(Criteria.where("targetAcc.userId").is(user.getId())
                                .and("read").is(false)),  // read 为 false
                        new Update().set("read", true),
                        CommLike.class
                );

            } else {
                //只修改满足条件的一条
                mongoTemplate.updateFirst(
                        new Query(Criteria.where("targetAcc.userId").is(user.getId())
                                .and("id").is(commLikeId)),
                        new Update().set("read", true),
                        CommLike.class
                );
            }

            return Boolean.TRUE;
        } catch (Exception e) {
            log.error("用户：{} 对点赞：{} 已读报错", user.getId(), commLikeId, e);
            throw new ServerInternalException("Read Like Failed");
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    public CommPageInfo<CommLike> getLikeMessage(String lastLikeId, Integer pageSize, User user) {
        List<CommLike> commLikeList = new ArrayList<>();
        try {
            Query query = new Query();
            query.addCriteria(Criteria.where("targetAcc.userId").is(user.getId()));

            if (StringUtil.isNotBlank(lastLikeId)) {
                query.addCriteria(Criteria.where("id").lt(new ObjectId(lastLikeId)));
            }

            query.with(Sort.by(Sort.Direction.DESC, "id"));

            // 设置分页
            query.limit(pageSize);
            commLikeList = mongoTemplate.find(query, CommLike.class);

            //组装图片的地址
            if (!CollectionUtils.isEmpty(commLikeList)) {
                List<String> fileIds = commLikeList.stream().map(CommLike::getFileId).collect(Collectors.toList());
                Map<String, String> fileUrlMap = commImgService.getCommFileUrlListByCommFileIds(fileIds);

                for (CommLike commLike : commLikeList) {
                    commLike.setMiniThumbnailUrl(fileUrlMap.containsKey(commLike.getFileId()) ? fileUrlMap.get(commLike.getFileId()) : "");
                }
            }
        } catch (Exception e) {
            log.error("获取社区图片点赞列表报错", e);
            throw new ServerInternalException("Error retrieving the community image likes list");
        }
        return buildPromptPageInfo(pageSize, commLikeList);
    }
}
