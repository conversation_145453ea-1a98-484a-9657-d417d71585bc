package com.lx.pl.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.lx.pl.config.CosConfig;
import com.lx.pl.constant.LockPrefixConstant;
import com.lx.pl.db.mysql.gen.entity.*;
import com.lx.pl.db.mysql.gen.mapper.*;
import com.lx.pl.dto.*;
import com.lx.pl.dto.generic.R;
import com.lx.pl.enums.PublicType;
import com.lx.pl.enums.UploadType;
import com.lx.pl.exception.BadRequestException;
import com.lx.pl.exception.ServerInternalException;
import com.lx.pl.util.DateUtils;
import com.lx.pl.util.IdUtils;
import com.lx.pl.util.ShardingCalculatorUtil;
import com.lx.pl.util.StringUtils;
import com.qcloud.cos.model.ObjectMetadata;
import com.qcloud.cos.model.ciModel.persistence.CIUploadResult;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.lx.pl.service.CosService.buildFileNameKey;
import static com.lx.pl.service.ImgUploadCommonService.getFilePath;
import static com.lx.pl.service.ImgUploadCommonService.isOldBucket;
import static com.lx.pl.util.ShardingCalculatorUtil.calculateShardingTable;

@Service
@Slf4j
public class CollectService {

    @Autowired
    UserCollectClassifyMapper userCollectClassifyMapper;

    @Autowired
    UserCollectMapper userCollectMapper;

    @Autowired
    PromptFileMapper promptFileMapper;

    @Autowired
    PromptRecordMapper promptRecordMapper;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private UserService userService;

    @Autowired
    private ImgUploadCommonService imgUploadCommonService;

    @Autowired
    PublicFileReviewMapper publicFileReviewMapper;

    @Autowired
    private CosConfig cosConfig;

    @Autowired
    UserCollectService userCollectService;

    @Autowired
    PromptFileService promptFileService;

    @Autowired
    private ExploreFileMapper exploreFileMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private GenService genService;

    @Resource
    private CosService cosService;

    @Resource
    private ImgService imgService;


    public UserCollectClassify getUserCollectClassify(String classifyId, User user) {
        return userCollectClassifyMapper.selectOne(new LambdaQueryWrapper<UserCollectClassify>()
                .eq(UserCollectClassify::getId, classifyId)
                .eq(UserCollectClassify::getLoginName, user.getLoginName()));
    }

    public UserCollect getUserCollect(String promptId, String imgName, String loginName, String classifyId) {
        LambdaQueryWrapper<UserCollect> prq = new LambdaQueryWrapper<>();
        prq.eq(UserCollect::getFileName, imgName);
        prq.eq(UserCollect::getPromptId, promptId);
        prq.eq(UserCollect::getLoginName, loginName);
        prq.eq(UserCollect::getClassifyId, classifyId);
        return userCollectMapper.selectOne(prq);
    }

    public Long getUserCollectNums(String loginName, String classifyId) {
        LambdaQueryWrapper<UserCollect> prq = new LambdaQueryWrapper<>();
        prq.eq(UserCollect::getLoginName, loginName);
        prq.eq(UserCollect::getClassifyId, classifyId);
        return userCollectMapper.selectCount(prq);
    }

    public PromptFile getPromptFile(String promptId, String imgName, String loginName) {
        LambdaQueryWrapper<PromptFile> qw = new LambdaQueryWrapper<>();
        qw.eq(PromptFile::getFileName, imgName);
        qw.eq(PromptFile::getPromptId, promptId);
        qw.eq(PromptFile::getLoginName, loginName);
        qw.eq(PromptFile::getDel, Boolean.FALSE);
        qw.isNull(PromptFile::getSensitiveMessage);
        return promptFileMapper.selectOne(qw);
    }

    public PromptRecord getPromptRecord(String promptId, String loginName) {
        LambdaQueryWrapper<PromptRecord> queryWrapper = new LambdaQueryWrapper<PromptRecord>();
        queryWrapper.eq(PromptRecord::getPromptId, promptId);
        queryWrapper.eq(PromptRecord::getLoginName, loginName);
        queryWrapper.eq(PromptRecord::getDel, Boolean.FALSE);
        return promptRecordMapper.selectOne(queryWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    public CollectUseSize addCollect(String classifyId, String promptId,
                                     String getImgName, User user,
                                     PromptRecord originPromptRecord,
                                     PromptFile promptFile) {
        RLock lock = redissonClient.getLock(LockPrefixConstant.USER_DO_LOCK_PREFIX + user.getId());
        UserCollect userCollect = null;
        CollectUseSize collectUseSize = new CollectUseSize();
        try {
            lock.lock();
            //收藏夹数量加1
            user = userService.getUserById(user.getId());
            Integer usedCollectNum = user.getUsedCollectNum();
            VipStandards vipStandards = genService.getVipStandards(user);
            Integer totalCollectNum = user.getTotalCollectNum();
            if (!Objects.isNull(vipStandards)) {
                totalCollectNum = vipStandards.getCollectNum();
            }
//            Long totalSize = user.getTotalSize();
//            Long usedSize = user.getUsedSize();
//            Long size = promptFile.getSize();
//            if (size == null || size == 0) {
//                long t1 = System.currentTimeMillis();
//                ObjectMetadata metadata = imgUploadCommonService.getMetadata(getFilePath(promptFile.getFileUrl()), isOldBucket(promptFile.getFileUrl()));
//                log.info("get metadata cost: {}", (System.currentTimeMillis() - t1) / 1000D);
//                if (metadata != null) {
//                    size = metadata.getContentLength();
//                    // 更新file size
//                    promptFile.setSize(size);
//                    LambdaUpdateWrapper<PromptFile> uw = new LambdaUpdateWrapper<>();
//                    uw.eq(PromptFile::getId, promptFile.getId());
//                    uw.eq(PromptFile::getLoginName, promptFile.getLoginName());
//                    uw.set(PromptFile::getSize, size);
//                    promptFileMapper.update(null, uw);
//                } else {
//                    log.info("get metadata failed {}", promptFile.getId());
//                    return null;
//                }
//            }
//            if (usedSize + size > totalSize) {
//                throw new BadRequestException("Your collections capacity are full, please organize and try again.");
//            }
            if (usedCollectNum + 1 > totalCollectNum) {
                throw new BadRequestException("Your image storage limit has been reached.");
            }

            String newMiniThumbnailUrl = null;
            LambdaQueryWrapper<UserCollectClassify> cfw = new LambdaQueryWrapper();
            cfw.eq(UserCollectClassify::getId, classifyId);
            cfw.eq(UserCollectClassify::getLoginName, user.getLoginName());
            UserCollectClassify userCollectClassify = userCollectClassifyMapper.selectOne(cfw);
            if (userCollectClassify.getCollectNums() <= 0 && StringUtils.isBlank(userCollectClassify.getCover())) {
                copyImgToCollectAsync(StringUtils.isNotBlank(promptFile.getMiniThumbnailUrl()) ? promptFile.getMiniThumbnailUrl() : promptFile.getThumbnailUrl(),
                        userCollectClassify);
                newMiniThumbnailUrl = userCollectClassify.getCover();
            }
            // 更新收藏夹数量 及 收藏夹容量 + size
            LambdaUpdateWrapper<UserCollectClassify> uw = new LambdaUpdateWrapper();
            uw.set(StringUtils.isNotBlank(newMiniThumbnailUrl), UserCollectClassify::getCover, newMiniThumbnailUrl);
            uw.setSql("collect_nums = collect_nums + 1");
            uw.eq(UserCollectClassify::getId, classifyId);
            uw.eq(UserCollectClassify::getLoginName, user.getLoginName());
            userCollectClassify.setCollectNums(userCollectClassify.getCollectNums() + 1);
            userCollectClassifyMapper.update(null, uw);

            // update User size
            userService.updateUsedCollectNum(user.getId(), usedCollectNum + 1);
            userCollect = buildUserCollect(classifyId, user, originPromptRecord, promptFile);
            // 插入收藏数据
            userCollectMapper.insert(userCollect);

            // 更新原图片的收藏数+1
            LambdaUpdateWrapper<PromptFile> ufw = new LambdaUpdateWrapper();
            ufw.setSql("collect_nums = collect_nums + 1");
            ufw.eq(PromptFile::getId, promptFile.getId());
            ufw.eq(PromptFile::getLoginName, user.getLoginName());
            promptFileMapper.update(null, ufw);

            collectUseSize.setNum(1);
            collectUseSize.setUsedCollectNum(usedCollectNum + 1);
            collectUseSize.setTotalCollectNum(totalCollectNum);

        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

        addMiniUrl(user, promptFile, userCollect);
        return collectUseSize;
    }

    private UserCollect buildUserCollect(String classifyId, User user, PromptRecord originPromptRecord, PromptFile promptFile) {
        UserCollect userCollect = new UserCollect();
        userCollect.setLoginName(user.getLoginName());
        userCollect.setUserId(user.getId());
        userCollect.setPromptId(promptFile.getPromptId());
        userCollect.setFileName(promptFile.getFileName());
        userCollect.setClassifyId(Long.valueOf(classifyId));
        userCollect.setThumbnailName(promptFile.getThumbnailName());
        userCollect.setHighThumbnailName(promptFile.getHighThumbnailName());
        userCollect.setFileUrl(promptFile.getFileUrl());
        userCollect.setThumbnailUrl(promptFile.getThumbnailUrl());
        userCollect.setHighThumbnailUrl(promptFile.getHighThumbnailUrl());
        userCollect.setMiniThumbnailUrl(promptFile.getMiniThumbnailUrl());

        userCollect.setWidth(promptFile.getWidth());
        userCollect.setHeight(promptFile.getHeight());
        userCollect.setSize(promptFile.getSize());
        userCollect.setSensitiveMessage(promptFile.getSensitiveMessage());

        userCollect.setCreateTime(LocalDateTime.now());
        userCollect.setCreateBy(user.getLoginName());
        userCollect.setPrompt(originPromptRecord.getPrompt());
        userCollect.setGenInfo(originPromptRecord.getGenInfo());
        userCollect.setOriginCreate(originPromptRecord.getOriginCreate());
        userCollect.setFileId(promptFile.getId());
        return userCollect;
    }

    private void addMiniUrl(User user, PromptFile promptFile, UserCollect userCollect) {
        if (StrUtil.isBlank(promptFile.getMiniThumbnailUrl())) {
            String miniPath = buildFileNameKey(null, "webp", UploadType.community.getValue());
            Long collectId = userCollect.getId();
            if (StrUtil.isNotBlank(promptFile.getHighThumbnailUrl())) {
                CompletableFuture<CIUploadResult> ciUploadResultCompletableFuture = imgUploadCommonService.imageProcessToMingUrlAsync(URLUtil.getPath(promptFile.getHighThumbnailUrl()), miniPath, isOldBucket(promptFile.getHighThumbnailUrl()));
                ciUploadResultCompletableFuture.whenCompleteAsync((ciUploadResult, throwable) -> {
                    String miniThumbnailUrl = promptFile.getHighThumbnailUrl();
                    if (ciUploadResult != null) {
                        miniThumbnailUrl = imgUploadCommonService.cosConfig.getCosAccelerateDomain() + miniPath;
                    }
                    LambdaUpdateWrapper<UserCollect> uw = new LambdaUpdateWrapper<>();
                    uw.eq(UserCollect::getLoginName, userCollect.getLoginName());
                    uw.eq(UserCollect::getId, collectId);
                    uw.set(UserCollect::getMiniThumbnailUrl, miniThumbnailUrl);
                    userCollectMapper.update(null, uw);

                    LambdaUpdateWrapper<PromptFile> uw2 = new LambdaUpdateWrapper<>();
                    uw2.eq(PromptFile::getId, promptFile.getId());
                    uw2.eq(PromptFile::getLoginName, promptFile.getLoginName());
                    uw2.set(PromptFile::getMiniThumbnailUrl, miniThumbnailUrl);
                    promptFileMapper.update(null, uw2);
                });
            } else {
                String highThumbnailName = buildFileNameKey(String.valueOf(user.getId()), "webp");
                imgUploadCommonService.imageProcessToWebp90(URLUtil.getPath(promptFile.getFileUrl()), highThumbnailName, isOldBucket(promptFile.getFileUrl()));
                String highThumbnailUrl = cosConfig.getCosAccelerateDomain() + highThumbnailName;
                CompletableFuture<CIUploadResult> ciUploadResultCompletableFuture = imgUploadCommonService.imageProcessToMingUrlAsync(URLUtil.getPath(highThumbnailUrl), miniPath, isOldBucket(highThumbnailUrl));
                ciUploadResultCompletableFuture.whenCompleteAsync((ciUploadResult, throwable) -> {
                    String miniThumbnailUrl = highThumbnailUrl;
                    if (ciUploadResult != null) {
                        miniThumbnailUrl = imgUploadCommonService.cosConfig.getCosAccelerateDomain() + miniPath;
                    }
                    LambdaUpdateWrapper<UserCollect> uw = new LambdaUpdateWrapper<>();
                    uw.eq(UserCollect::getLoginName, userCollect.getLoginName());
                    uw.eq(UserCollect::getId, collectId);
                    uw.set(UserCollect::getMiniThumbnailUrl, miniThumbnailUrl);
                    userCollectMapper.update(null, uw);

                    LambdaUpdateWrapper<PromptFile> uw2 = new LambdaUpdateWrapper<>();
                    uw2.eq(PromptFile::getId, promptFile.getId());
                    uw2.eq(PromptFile::getLoginName, promptFile.getLoginName());
                    uw2.set(PromptFile::getMiniThumbnailUrl, miniThumbnailUrl);
                    uw2.set(PromptFile::getHighThumbnailUrl, highThumbnailUrl);
                    promptFileMapper.update(null, uw2);
                });
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public CollectUseSize reduceCollect(UserCollect userCollect, User user) {
        RLock lock = redissonClient.getLock(LockPrefixConstant.USER_DO_LOCK_PREFIX + user.getId());

        CollectUseSize collectUseSize = new CollectUseSize();
        //取消收藏操作
        try {
            lock.lock();
            LambdaQueryWrapper<UserCollect> qw = new LambdaQueryWrapper<>();
            qw.eq(UserCollect::getId, userCollect.getId())
                    .eq(UserCollect::getLoginName, user.getLoginName());
            userCollectMapper.delete(qw);

            // 更新收藏夹数量 及 收藏夹容量 - size
            LambdaUpdateWrapper<UserCollectClassify> uw = new LambdaUpdateWrapper();
            uw.setSql("collect_nums = collect_nums - 1");
            uw.eq(UserCollectClassify::getId, userCollect.getClassifyId());
            uw.eq(UserCollectClassify::getLoginName, userCollect.getLoginName());
            userCollectClassifyMapper.update(null, uw);

            // 更新原图片的收藏数 - 1
            LambdaUpdateWrapper<PromptFile> ufw = new LambdaUpdateWrapper();
            ufw.setSql("collect_nums = collect_nums - 1");
            ufw.eq(PromptFile::getId, userCollect.getFileId());
            ufw.eq(PromptFile::getLoginName, user.getLoginName());
            promptFileMapper.update(null, ufw);

            // 更新用户容量
            user = userService.getUserById(user.getId());
            userService.updateUsedCollectNum(user.getId(), user.getUsedCollectNum() - 1 > 0 ? user.getUsedCollectNum() - 1 : 0);

            LambdaQueryWrapper<PromptFile> eq = new LambdaQueryWrapper<PromptFile>()
                    .eq(PromptFile::getId, userCollect.getFileId())
                    .eq(PromptFile::getLoginName, user.getLoginName())
                    .eq(PromptFile::getCollectNums, 0)
                    .eq(PromptFile::getDel, 1);
            Long aLong = promptFileMapper.selectCount(eq);

            // 如果原图被删并且没有收藏 删除审核记录
            if (aLong > 0) {
                LambdaQueryWrapper<PublicFileReview> ruw = new LambdaQueryWrapper<>();
                ruw.eq(PublicFileReview::getFileId, userCollect.getFileId());
                ruw.eq(PublicFileReview::getLoginName, user.getLoginName());
                publicFileReviewMapper.delete(ruw);
            }

            //返回给前端的参数
            collectUseSize.setNum(1);
            collectUseSize.setUsedCollectNum(user.getUsedCollectNum() - 1);
            VipStandards vipStandards = genService.getVipStandards(user);
            if (!Objects.isNull(vipStandards)) {
                collectUseSize.setTotalCollectNum(vipStandards.getCollectNum());
            } else {
                collectUseSize.setTotalCollectNum(500);
            }
        } catch (Exception e) {
            log.error("用户：{}，取消收藏图片操作失败", user.getLoginName());
            return null;
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return collectUseSize;
    }


    public PromptPageInfo<UserCollectResult> getCollectList(Integer pageNum, Integer pageSize, String vagueKey, String classifyId, String collationName, User user) {
        List<UserCollectResult> userCollectResultList = new ArrayList<>();

        LambdaQueryWrapper<UserCollect> qw = new LambdaQueryWrapper<>();
        vagueKey = StringUtils.isNotBlank(vagueKey) ? vagueKey.trim() : "";
        if (StringUtils.isNotBlank(vagueKey)) {
            qw.like(UserCollect::getPrompt, vagueKey);
        }
        qw.eq(UserCollect::getClassifyId, classifyId);
        qw.eq(UserCollect::getLoginName, user.getLoginName());

        if (StringUtils.isBlank(collationName) || "Descending".equals(collationName)) {
            qw.orderByDesc(UserCollect::getCreateTime);
        } else if ("Ascending".equals(collationName)) {
            qw.orderByAsc(UserCollect::getCreateTime);
        }

        IPage<UserCollect> page = userCollectMapper.selectPage(new Page<>(pageNum, pageSize), qw);
        List<UserCollect> records = page.getRecords();

        if (!CollectionUtils.isEmpty(records)) {

            // 查询 查询出此收藏 原始图片的状态
            List<Long> fileIds = records.stream().map(UserCollect::getFileId).collect(Collectors.toList());
            LambdaQueryWrapper<PromptFile> qfw = new LambdaQueryWrapper();
            qfw.eq(PromptFile::getLoginName, user.getLoginName());
            qfw.in(PromptFile::getId, fileIds);
            List<PromptFile> promptFiles = promptFileMapper.selectList(qfw);

            Map<Long, PromptFile> promptFileMap = promptFiles.stream().collect(Collectors.toMap(PromptFile::getId, Function.identity()));

            for (UserCollect userCollect : records) {
                PromptFile promptFile = promptFileMap.get(userCollect.getFileId());
                UserCollectResult userCollectResult = new UserCollectResult();
                BeanUtils.copyProperties(userCollect, userCollectResult);
                userCollectResult.setIsPublic(!Objects.isNull(promptFile) ? promptFile.getIsPublic() : PublicType.undisclosed.getValue());
                userCollectResult.setRejectionContent(!Objects.isNull(promptFile) ? promptFile.getRejectionContent() : "rejected");
                userCollectResultList.add(userCollectResult);
            }
        }

        PromptPageInfo<UserCollectResult> promptPageInfo = new PromptPageInfo<>();
        promptPageInfo.setResultList(userCollectResultList);
        promptPageInfo.setPageNum(pageNum);
        promptPageInfo.setPageSize(pageSize);
        promptPageInfo.setTotal(!Objects.isNull(page.getTotal()) ? Math.toIntExact(page.getTotal()) : 0);
        return promptPageInfo;
    }

    public PromptPageInfo<HistoryImgResult> getHistoryCollectList(Integer pageNum, Integer pageSize, String vagueKey, String classifyId, String collationName, User user) {
        List<HistoryImgResult> historyImgResults = new ArrayList<>();

        LambdaQueryWrapper<UserCollect> qw = new LambdaQueryWrapper<>();
        vagueKey = StringUtils.isNotBlank(vagueKey) ? vagueKey.trim() : "";
        if (StringUtils.isNotBlank(vagueKey)) {
            qw.like(UserCollect::getPrompt, vagueKey);
        }
        qw.eq(UserCollect::getClassifyId, classifyId);
        qw.eq(UserCollect::getLoginName, user.getLoginName());

        if (StringUtils.isBlank(collationName) || "Descending".equals(collationName)) {
            qw.orderByDesc(UserCollect::getCreateTime);
            qw.orderByDesc(UserCollect::getId);
        } else if ("Ascending".equals(collationName)) {
            qw.orderByAsc(UserCollect::getCreateTime);
            qw.orderByAsc(UserCollect::getId);
        }

        IPage<UserCollect> page = userCollectMapper.selectPage(new Page<>(pageNum, pageSize), qw);
        List<UserCollect> records = page.getRecords();

        if (!CollectionUtils.isEmpty(records)) {

            // 查询 查询出此收藏 原始图片的状态
            List<Long> fileIds = records.stream().map(UserCollect::getFileId).collect(Collectors.toList());
            LambdaQueryWrapper<PromptFile> qfw = new LambdaQueryWrapper();
            qfw.eq(PromptFile::getLoginName, user.getLoginName());
            qfw.in(PromptFile::getId, fileIds);
            List<PromptFile> promptFiles = promptFileMapper.selectList(qfw);

            Map<Long, PromptFile> promptFileMap = promptFiles.stream().collect(Collectors.toMap(PromptFile::getId, Function.identity()));

            for (UserCollect userCollect : records) {
                PromptFile promptFile = promptFileMap.get(userCollect.getFileId());
                HistoryImgResult historyImgResult = new HistoryImgResult();
                BeanUtils.copyProperties(userCollect, historyImgResult);
                historyImgResult.setImgName(userCollect.getFileName());
                historyImgResult.setImgUrl(userCollect.getFileUrl());
                historyImgResult.setHighMiniUrl(!Objects.isNull(promptFile) ? promptFile.getHighMiniUrl() : "");
                historyImgResult.setRealHeight(!Objects.isNull(promptFile) ? promptFile.getHeight() : 0);
                historyImgResult.setRealWidth(!Objects.isNull(promptFile) ? promptFile.getWidth() : 0);

                historyImgResult.setIsPublic(!Objects.isNull(promptFile) ? promptFile.getIsPublic() : PublicType.undisclosed.getValue());
                historyImgResult.setRejectionContent(!Objects.isNull(promptFile) ? promptFile.getRejectionContent() : "rejected");
                historyImgResults.add(historyImgResult);
            }
        }

        PromptPageInfo<HistoryImgResult> promptPageInfo = new PromptPageInfo<>();
        promptPageInfo.setResultList(historyImgResults);
        promptPageInfo.setPageNum(pageNum);
        promptPageInfo.setPageSize(pageSize);
        promptPageInfo.setTotal(!Objects.isNull(page.getTotal()) ? Math.toIntExact(page.getTotal()) : 0);
        return promptPageInfo;
    }


    public Boolean addCollectClassify(String collectName, String description, User user, String cover) {
        UserCollectClassify userCollectClassify = new UserCollectClassify();
        userCollectClassify.setUserId(user.getId());
        userCollectClassify.setCover(cover);
        userCollectClassify.setLoginName(user.getLoginName());
        userCollectClassify.setDescription(description);
        userCollectClassify.setCollectName(collectName);
        userCollectClassify.setCreateTime(LocalDateTime.now());
        userCollectClassify.setCreateBy(user.getLoginName());
        userCollectClassifyMapper.insert(userCollectClassify);
        return Boolean.TRUE;
    }

    public Boolean renameCollectClassify(String id, String collectName, String description, User user, String cover) {
        LambdaUpdateWrapper<UserCollectClassify> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(UserCollectClassify::getLoginName, user.getLoginName());
        updateWrapper.eq(UserCollectClassify::getId, id);
        updateWrapper.set(UserCollectClassify::getCollectName, collectName);
        updateWrapper.set(UserCollectClassify::getDescription, description);
        updateWrapper.set(StringUtils.isNotBlank(cover), UserCollectClassify::getCover, cover);
        updateWrapper.set(UserCollectClassify::getUpdateTime, LocalDateTime.now());
        updateWrapper.set(UserCollectClassify::getUpdateBy, user.getLoginName());
        int update = userCollectClassifyMapper.update(null, updateWrapper);
        if (update <= 0) {
            log.info("renameCollectClassify error");
            throw new ServerInternalException("Failed to rename");
        }
        return Boolean.TRUE;
    }

    public List<UserCollectClassify> getCollectClassifyList(User user) {

        // 查询该用户收藏分类
        LambdaQueryWrapper<UserCollectClassify> qccw = new LambdaQueryWrapper<>();
        qccw.eq(UserCollectClassify::getLoginName, user.getLoginName());
        qccw.ne(UserCollectClassify::getDel, Boolean.TRUE);
        List<UserCollectClassify> userCollectClassifies = userCollectClassifyMapper.selectList(qccw);

        if (CollectionUtils.isEmpty(userCollectClassifies)) {
            // 没有文件夹新增一个
            try {
                UserCollectClassify userCollectClassify = new UserCollectClassify();
                userCollectClassify.setUserId(user.getId());
                userCollectClassify.setCollectName("default");
                userCollectClassify.setLoginName(user.getLoginName());
                userCollectClassify.setCreateTime(LocalDateTime.now());
                userCollectClassify.setCreateBy("system");
                userCollectClassifyMapper.insert(userCollectClassify);
                userCollectClassifies.add(userCollectClassify);
            } catch (Exception e) {
                log.error("新增默认收藏夹：{}，报错信息为：", DateUtils.getTime(), e);
                return null;
            }
        }
        return userCollectClassifies;
    }

    public CollectUseSize deleteCollectClassify(String id, User user) {

        RLock lock = redissonClient.getLock(LockPrefixConstant.USER_DO_LOCK_PREFIX + user.getId());
        CollectUseSize collectUseSize = new CollectUseSize();
        try {
            lock.lock();
            // 物理删除该用户收藏分类
            LambdaQueryWrapper<UserCollectClassify> luw = new LambdaQueryWrapper<>();
            luw.eq(UserCollectClassify::getId, id);
            luw.eq(UserCollectClassify::getLoginName, user.getLoginName());

            int update = userCollectClassifyMapper.delete(luw);

            if (update < 1) {
                throw new ServerInternalException("This favorite has been deleted");
            }

            LambdaQueryWrapper<UserCollect> cqw = new LambdaQueryWrapper<>();
            cqw.eq(UserCollect::getClassifyId, id);
            cqw.eq(UserCollect::getLoginName, user.getLoginName());

            List<UserCollect> userCollects = userCollectMapper.selectList(cqw);

            user = userService.getUserById(user.getId());

            if (CollectionUtils.isEmpty(userCollects)) {
                collectUseSize.setNum(0);
                collectUseSize.setUsedCollectNum(user.getUsedCollectNum());
                VipStandards vipStandards = genService.getVipStandards(user);
                if (!Objects.isNull(vipStandards)) {
                    collectUseSize.setTotalCollectNum(vipStandards.getCollectNum());
                } else {
                    collectUseSize.setTotalCollectNum(500);
                }
                return collectUseSize;
            }

            List<Long> fileIds = userCollects.stream().map(UserCollect::getFileId).collect(Collectors.toList());


            LambdaUpdateWrapper<PromptFile> ufw = new LambdaUpdateWrapper();
            ufw.setSql("collect_nums = collect_nums - 1");
            ufw.in(PromptFile::getId, fileIds);
            ufw.eq(PromptFile::getLoginName, user.getLoginName());
            promptFileMapper.update(null, ufw);

            // 更新收藏量
            userService.updateUsedCollectNum(user.getId(), user.getUsedCollectNum() - userCollects.size() > 0 ? user.getUsedCollectNum() - userCollects.size() : 0);

            // 删除该收藏夹下的所有收藏
            LambdaQueryWrapper<UserCollect> luw2 = new LambdaQueryWrapper<>();
            luw2.eq(UserCollect::getClassifyId, id);
            luw2.eq(UserCollect::getLoginName, user.getLoginName());
            userCollectMapper.delete(luw2);


            LambdaQueryWrapper<PromptFile> eq = new LambdaQueryWrapper<PromptFile>()
                    .in(PromptFile::getId, fileIds)
                    .eq(PromptFile::getLoginName, user.getLoginName())
                    .eq(PromptFile::getCollectNums, 0)
                    .eq(PromptFile::getDel, 1);
            List<PromptFile> promptFiles = promptFileMapper.selectList(eq);

            // 如果原图被删并且没有收藏 删除审核记录
            if (!CollectionUtils.isEmpty(promptFiles)) {
                List<Long> deleteFileIds = promptFiles.stream().map(PromptFile::getId).collect(Collectors.toList());
                LambdaQueryWrapper<PublicFileReview> ruw = new LambdaQueryWrapper<>();
                ruw.in(PublicFileReview::getFileId, deleteFileIds);
                ruw.eq(PublicFileReview::getLoginName, user.getLoginName());
                publicFileReviewMapper.delete(ruw);
            }

            collectUseSize.setNum(userCollects.size());
            collectUseSize.setUsedCollectNum(user.getUsedCollectNum() - userCollects.size());
            VipStandards vipStandards = genService.getVipStandards(user);
            if (!Objects.isNull(vipStandards)) {
                collectUseSize.setTotalCollectNum(vipStandards.getCollectNum());
            } else {
                collectUseSize.setTotalCollectNum(500);
            }
            return collectUseSize;
        } catch (Exception e) {
            log.error("用户：{},删除文件夹{} ", user.getId(), id, e);
            throw new ServerInternalException("delete Failed");
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

    }

    public Long getCollectCount(String classifyId, User user) {
        LambdaQueryWrapper<UserCollect> luw = new LambdaQueryWrapper<>();
        luw.eq(UserCollect::getClassifyId, classifyId);
        luw.eq(UserCollect::getLoginName, user.getLoginName());
        return userCollectMapper.selectCount(luw);
    }

    public CollectUseSize addCollectBatchSegmentation(List<CollectBatch> collectBatchList, String classifyId, User user) {
        CollectUseSize collectUseSize = new CollectUseSize();
        List<List<CollectBatch>> partition = Lists.partition(collectBatchList, 20);
        for (List<CollectBatch> list : partition) {
            collectUseSize = addCollectBatch(list, classifyId, user);
        }
        return collectUseSize;
    }

    @Transactional(rollbackFor = Exception.class)
    public CollectUseSize addCollectBatch(List<CollectBatch> collectBatchList, String classifyId, User user) {

        List<String> promptIds = new ArrayList<>();
        List<String> fileNames = new ArrayList<>();

        CollectUseSize collectUseSize = new CollectUseSize();

        for (CollectBatch collectBatch : collectBatchList) {
            promptIds.add(collectBatch.getPromptId());
            fileNames.add(collectBatch.getFileName());
        }

        List<UserCollect> collectByFileNameList = getCollectByFileNameList(fileNames, promptIds, user, classifyId);

        // 选中的图片有被收藏的
        if (!CollectionUtils.isEmpty(collectByFileNameList)) {
            // 选中的图片有全被收藏
            if (collectByFileNameList.size() >= collectBatchList.size()) {
                // 全部收藏 直接返回当前大小的size
                collectUseSize.setSize(0l);
                collectUseSize.setUsedSize(user.getUsedSize());
                collectUseSize.setTotalSize(user.getTotalSize());
                return collectUseSize;
            }

            // 剔除已被收藏的fileName 和 promptId
            fileNames = filterByCollect(fileNames, collectByFileNameList, "fileName");
            if (CollectionUtils.isEmpty(fileNames) || CollectionUtils.isEmpty(promptIds)) {
                // 全部收藏 直接返回当前大小的size
                collectUseSize.setSize(0l);
                collectUseSize.setUsedSize(user.getUsedSize());
                collectUseSize.setTotalSize(user.getTotalSize());
                return collectUseSize;
            }
        }

        // 查询对应的文件和任务信息
        List<PromptFile> fileListByFileNameList = getFileListByFileNameList(fileNames, promptIds, user);
        List<PromptRecord> recordListByPromptIdList = getRecordListByPromptIdList(promptIds, user);

        if (CollectionUtils.isEmpty(fileListByFileNameList) || CollectionUtils.isEmpty(recordListByPromptIdList)) {
            // 如果源数据被删除 直接返回当前大小的size
            collectUseSize.setSize(0l);
            collectUseSize.setUsedSize(user.getUsedSize());
            collectUseSize.setTotalSize(user.getTotalSize());
            return collectUseSize;
        }

        // 将 List<PromptRecord> 转换为 Map<String, PromptRecord>
        Map<String, PromptRecord> promptRecordMap = recordListByPromptIdList.stream()
                .collect(Collectors.toMap(PromptRecord::getPromptId, record -> record));

        RLock lock = redissonClient.getLock(LockPrefixConstant.USER_DO_LOCK_PREFIX + user.getId());
        Map<Long, UserCollect> fileIdCollectMap = new HashMap<>();

        try {
            lock.lock();
            // 加相应的收藏夹数量
            user = userService.getUserById(user.getId());
            Integer usedCollectNum = user.getUsedCollectNum();
            VipStandards vipStandards = genService.getVipStandards(user);
            Integer totalCollectNum = user.getTotalCollectNum();
            if (!Objects.isNull(vipStandards)) {
                totalCollectNum = vipStandards.getCollectNum();
            }//            Long totalSize = user.getTotalSize();
//            Long usedSize = user.getUsedSize();
//            Long size = 0l;
//            List<PromptFile> filesToUpdate = new ArrayList<>(); // 用于存储需要更新的文件信息
//            for (PromptFile file : fileListByFileNameList) {
//                Long fileSize = file.getSize();
//                if (Objects.isNull(fileSize) || fileSize == 0) {
//                    // 获取文件size
//                    long t1 = System.currentTimeMillis();
//                    ObjectMetadata metadata = imgUploadCommonService.getMetadata(getFilePath(file.getFileUrl()), isOldBucket(file.getFileUrl()));
//                    log.info("get metadata cost: {}", (System.currentTimeMillis() - t1) / 1000D);
//                    if (metadata != null) {
//                        fileSize = metadata.getContentLength();
//                        // 更新file size
//                        file.setSize(fileSize);
//                        filesToUpdate.add(file); // 记录需要更新的文件
//                    } else {
//                        log.info("get metadata failed {}", file.getId());
//                        return null; // 如果有文件获取metadata失败，可以考虑继续处理其他文件或者返回错误
//                    }
//                 }
//                size = fileSize + size;
//            }
//
//            // 批量更新数据库
//            if (!filesToUpdate.isEmpty()) {
//                String tableName = ShardingCalculatorUtil.calculateShardingTable(user.getLoginName(), "gpt_prompt_file", 20);
////                // 假设promptFileMapper有批量更新方法，你可以根据实际情况使用批量更新方法
//                promptFileMapper.batchUpdate(filesToUpdate,tableName);
//            }

            // 如果超过size 则不让收藏
            if (usedCollectNum + fileListByFileNameList.size() > totalCollectNum) {
                throw new BadRequestException("Your image storage limit has been reached.");
            }

            //  判断是否为新收藏夹 如果是则复制封面
            LambdaQueryWrapper<UserCollectClassify> cfw = new LambdaQueryWrapper();
            cfw.eq(UserCollectClassify::getId, classifyId);
            cfw.eq(UserCollectClassify::getLoginName, user.getLoginName());
            UserCollectClassify userCollectClassify = userCollectClassifyMapper.selectOne(cfw);
            if (userCollectClassify.getCollectNums() <= 0 && StringUtils.isBlank(userCollectClassify.getCover())) {
                String newMiniThumbnailUrl = "";
                PromptFile fileFirst = fileListByFileNameList.get(0);
                copyImgToCollectAsync(
                        StringUtils.isNotBlank(fileFirst.getMiniThumbnailUrl()) ? fileFirst.getMiniThumbnailUrl() : fileFirst.getThumbnailUrl(),
                        userCollectClassify);
                newMiniThumbnailUrl = userCollectClassify.getCover();
                LambdaUpdateWrapper<UserCollectClassify> uw = new LambdaUpdateWrapper();
                uw.set(StringUtils.isNotBlank(newMiniThumbnailUrl), UserCollectClassify::getCover, newMiniThumbnailUrl);
                uw.eq(UserCollectClassify::getId, classifyId);
                uw.eq(UserCollectClassify::getLoginName, user.getLoginName());
                userCollectClassifyMapper.update(null, uw);
            }
            List<Long> fileIds = new ArrayList<>();
            List<UserCollect> userCollectList = new ArrayList<>();
            for (PromptFile file : fileListByFileNameList) {
                PromptRecord promptRecord = promptRecordMap.get(file.getPromptId());
                if (!Objects.isNull(promptRecord)) {
                    UserCollect userCollect = buildUserCollect(classifyId, user, promptRecord, file);
                    userCollectList.add(userCollect);
                    fileIds.add(file.getId());
                    fileIdCollectMap.put(file.getId(), userCollect);
                }
            }
            // 批量插入
            userCollectService.saveBatch(userCollectList);

            // 更新文件夹数量
            userCollectClassifyMapper.updateSizeAndNumAdd(Long.valueOf(classifyId), user.getLoginName(), userCollectList.size());

            // update User num
            userService.updateUsedCollectNum(user.getId(), usedCollectNum + userCollectList.size());
            // 更新原图片的收藏数+1
            LambdaUpdateWrapper<PromptFile> ufw = new LambdaUpdateWrapper();
            ufw.setSql("collect_nums = collect_nums + 1");
            ufw.in(PromptFile::getId, fileIds);
            ufw.eq(PromptFile::getLoginName, user.getLoginName());
            promptFileMapper.update(null, ufw);
            collectUseSize.setNum(userCollectList.size());
            collectUseSize.setUsedCollectNum(usedCollectNum + userCollectList.size());
            collectUseSize.setTotalCollectNum(totalCollectNum);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

        // 更新微小缩略图
        for (PromptFile promptFile : fileListByFileNameList) {
            UserCollect userCollect = fileIdCollectMap.get(promptFile.getId());
            if (!Objects.isNull(userCollect)) {
                addMiniUrl(user, promptFile, fileIdCollectMap.get(promptFile.getId()));
            }
        }
        return collectUseSize;
    }

    public CollectUseSize reduceCollectBatchSegmentation(List<CollectBatch> collectBatchList, String classifyId, User user) {
        CollectUseSize collectUseSize = new CollectUseSize();
        List<List<CollectBatch>> partition = Lists.partition(collectBatchList, 20);
        for (List<CollectBatch> list : partition) {
            collectUseSize = reduceCollectBatch(list, classifyId, user);
        }
        return collectUseSize;
    }

    @Transactional(rollbackFor = Exception.class)
    public CollectUseSize reduceCollectBatch(List<CollectBatch> collectBatchList, String classifyId, User user) {

        List<String> promptIds = new ArrayList<>();
        List<String> fileNames = new ArrayList<>();

        for (CollectBatch collectBatch : collectBatchList) {
            promptIds.add(collectBatch.getPromptId());
            fileNames.add(collectBatch.getFileName());
        }

        List<UserCollect> collectByFileNameList = getCollectByFileNameList(fileNames, promptIds, user, classifyId);

        if (CollectionUtils.isEmpty(collectByFileNameList)) {
            throw new BadRequestException("Please collect first !");
        }
        RLock lock = redissonClient.getLock(LockPrefixConstant.USER_DO_LOCK_PREFIX + user.getId());
        CollectUseSize collectUseSize = new CollectUseSize();
        //取消收藏操作
        try {
            lock.lock();

            // 计算size 和提取 collectId 和 fileId
            List<Long> collectIds = new ArrayList<>();
            List<Long> fileIds = new ArrayList<>();
            for (UserCollect userCollect : collectByFileNameList) {
                collectIds.add(userCollect.getId());
                fileIds.add(userCollect.getFileId());
            }

            // 删除收藏夹
            LambdaQueryWrapper<UserCollect> qw = new LambdaQueryWrapper<>();
            qw.in(UserCollect::getId, collectIds)
                    .eq(UserCollect::getLoginName, user.getLoginName());
            userCollectMapper.delete(qw);

            // 更新收藏夹表
            userCollectClassifyMapper.updateSizeAndNumReduce(Long.valueOf(classifyId), user.getLoginName(), collectByFileNameList.size());

            // 更新原图片的收藏数 - 1
            LambdaUpdateWrapper<PromptFile> ufw = new LambdaUpdateWrapper();
            ufw.setSql("collect_nums = collect_nums - 1");
            ufw.in(PromptFile::getId, fileIds);
            ufw.eq(PromptFile::getLoginName, user.getLoginName());
            promptFileMapper.update(null, ufw);

            // 更新用户容量
            user = userService.getUserById(user.getId());
            userService.updateUsedCollectNum(user.getId(),
                    user.getUsedCollectNum() - collectByFileNameList.size() > 0 ? user.getUsedCollectNum() - collectByFileNameList.size() : 0);

            // 如果原图被删并且没有收藏 删除审核记录
            LambdaQueryWrapper<PromptFile> eq = new LambdaQueryWrapper<PromptFile>()
                    .in(PromptFile::getId, fileIds)
                    .eq(PromptFile::getLoginName, user.getLoginName())
                    .eq(PromptFile::getCollectNums, 0)
                    .eq(PromptFile::getDel, 1);
            List<PromptFile> promptFiles = promptFileMapper.selectList(eq);

            // 不为空 删除审核记录
            if (!CollectionUtils.isEmpty(promptFiles)) {
                List<Long> deleteFileIds = promptFiles.stream().map(PromptFile::getId).collect(Collectors.toList());
                LambdaQueryWrapper<PublicFileReview> ruw = new LambdaQueryWrapper<>();
                ruw.in(PublicFileReview::getFileId, deleteFileIds);
                ruw.eq(PublicFileReview::getLoginName, user.getLoginName());
                publicFileReviewMapper.delete(ruw);
            }

            //返回给前端的参数
            collectUseSize.setNum(collectByFileNameList.size());
            collectUseSize.setUsedCollectNum(user.getUsedCollectNum() - collectByFileNameList.size());
            VipStandards vipStandards = genService.getVipStandards(user);
            if (!Objects.isNull(vipStandards)) {
                collectUseSize.setTotalCollectNum(vipStandards.getCollectNum());
            } else {
                collectUseSize.setTotalCollectNum(500);
            }
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return collectUseSize;
    }

    public Long getCollectClassify(String collectName, String id, User user) {

        LambdaQueryWrapper<UserCollectClassify> qccw = new LambdaQueryWrapper<>();
        qccw.eq(UserCollectClassify::getLoginName, user.getLoginName());
        qccw.eq(UserCollectClassify::getDel, Boolean.FALSE);

        if (StringUtils.isNotBlank(collectName)) {
            qccw.eq(UserCollectClassify::getCollectName, collectName);
        }

        if (StringUtils.isNotBlank(id)) {
            qccw.ne(UserCollectClassify::getId, id);
        }

        return userCollectClassifyMapper.selectCount(qccw);
    }


    public List<PromptFile> getFileListByFileNameList(List<String> fileNameList, List<String> promptIdList, User user) {
        LambdaQueryWrapper<PromptFile> lpw = new LambdaQueryWrapper();
        lpw.eq(PromptFile::getLoginName, user.getLoginName());
        lpw.in(PromptFile::getFileName, fileNameList);
        lpw.in(PromptFile::getPromptId, promptIdList);
        lpw.eq(PromptFile::getDel, Boolean.FALSE);
//        lpw.isNull(PromptFile::getSensitiveMessage);

        return promptFileMapper.selectList(lpw);
    }

    public List<PromptRecord> getRecordListByPromptIdList(List<String> promptIdList, User user) {
        LambdaQueryWrapper<PromptRecord> lrw = new LambdaQueryWrapper();
        lrw.eq(PromptRecord::getLoginName, user.getLoginName());
        lrw.in(PromptRecord::getPromptId, promptIdList);
//        lrw.eq(PromptRecord::getDel, Boolean.FALSE);

        return promptRecordMapper.selectList(lrw);
    }

    public List<UserCollect> getCollectByFileNameList(List<String> fileNameList, List<String> promptIdList, User user, String classifyId) {
        LambdaQueryWrapper<UserCollect> luw = new LambdaQueryWrapper<>();
        luw.eq(UserCollect::getLoginName, user.getLoginName());
        luw.eq(UserCollect::getClassifyId, classifyId);
        luw.in(UserCollect::getPromptId, promptIdList);
        luw.in(UserCollect::getFileName, fileNameList);

        return userCollectMapper.selectList(luw);

    }

    // 过滤 已收藏的文件名
    public List<String> filterByCollect(List<String> list, List<UserCollect> userCollectList, String type) {
        // 获取 userCollectList 中所有的 fileName 或 promptId
        List<String> collectedItems = userCollectList.stream()
                .map(userCollect -> {
                    if ("fileName".equals(type)) {
                        return userCollect.getFileName();  // 获取 UserCollect 中的 fileName
                    } else if ("promptId".equals(type)) {
                        return userCollect.getPromptId();  // 获取 UserCollect 中的 promptId
                    } else {
                        return "";
                    }
                })
                .collect(Collectors.toList());

        // 过滤 list 中存在于 collectedItems 中的元素
        return list.stream()
                .filter(item -> !collectedItems.contains(item))  // 剔除已经收藏的文件名或 promptId
                .collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    public CollectUseSize moveCollect(UserCollect userCollect, String classifyId, User user) {

        RLock lock = redissonClient.getLock(LockPrefixConstant.USER_DO_LOCK_PREFIX + user.getId());

        CollectUseSize collectUseSize = new CollectUseSize();

        //移到收藏夹操作
        try {
            lock.lock();

            // 记录老收藏夹id
            Long oldClassifyId = userCollect.getClassifyId();

            // 更改收藏数据的收藏夹id
            LambdaUpdateWrapper<UserCollect> luw = new LambdaUpdateWrapper();
            luw.set(UserCollect::getClassifyId, classifyId);
            luw.eq(UserCollect::getId, userCollect.getId());
            luw.eq(UserCollect::getLoginName, user.getLoginName());
            userCollectMapper.update(null, luw);

            // 更新老收藏夹数量 及 收藏夹容量 - size
            LambdaUpdateWrapper<UserCollectClassify> uw = new LambdaUpdateWrapper();
            uw.setSql("collect_nums = collect_nums - 1");
            uw.eq(UserCollectClassify::getId, oldClassifyId);
            uw.eq(UserCollectClassify::getLoginName, userCollect.getLoginName());
            userCollectClassifyMapper.update(null, uw);


            //  判断是否为新收藏夹 如果是则复制封面
            LambdaQueryWrapper<UserCollectClassify> cfw = new LambdaQueryWrapper();
            cfw.eq(UserCollectClassify::getId, classifyId);
            cfw.eq(UserCollectClassify::getLoginName, user.getLoginName());
            UserCollectClassify userCollectClassify = userCollectClassifyMapper.selectOne(cfw);
            if (userCollectClassify.getCollectNums() <= 0 && StringUtils.isBlank(userCollectClassify.getCover())) {
                String newMiniThumbnailUrl = "";

                // 复制文件
                copyImgToCollectAsync(
                        StringUtils.isNotBlank(userCollect.getMiniThumbnailUrl()) ? userCollect.getMiniThumbnailUrl() : userCollect.getThumbnailUrl(),
                        userCollectClassify);
                newMiniThumbnailUrl = userCollectClassify.getCover();
                LambdaUpdateWrapper<UserCollectClassify> ucw = new LambdaUpdateWrapper();

                ucw.set(StringUtils.isNotBlank(newMiniThumbnailUrl), UserCollectClassify::getCover, newMiniThumbnailUrl);
                ucw.eq(UserCollectClassify::getId, classifyId);
                ucw.eq(UserCollectClassify::getLoginName, user.getLoginName());

                userCollectClassifyMapper.update(null, ucw);

            }

            // 更新新收藏夹数量 及 收藏夹容量 + size
            LambdaUpdateWrapper<UserCollectClassify> unw = new LambdaUpdateWrapper();
            unw.setSql("collect_nums = collect_nums + 1");
            unw.eq(UserCollectClassify::getId, classifyId);
            unw.eq(UserCollectClassify::getLoginName, userCollect.getLoginName());
            userCollectClassifyMapper.update(null, unw);

            // 用户可能在其他端操作了 收藏夹，需要更新用户信息
            user = userService.getUserById(user.getId());
            collectUseSize.setNum(0);
            collectUseSize.setUsedCollectNum(user.getUsedCollectNum());
            VipStandards vipStandards = genService.getVipStandards(user);
            if (!Objects.isNull(vipStandards)) {
                collectUseSize.setTotalCollectNum(vipStandards.getCollectNum());
            } else {
                collectUseSize.setTotalCollectNum(500);
            }
        } catch (Exception e) {
            log.error("用户：{}，取消收藏图片操作失败", user.getLoginName());
            return null;
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return collectUseSize;

    }

    public CollectUseSize moveCollectBatchSegmentation(List<CollectBatch> collectBatchList, String classifyId, String oldClassifyId, User user) {

        CollectUseSize collectUseSize = new CollectUseSize();
        List<List<CollectBatch>> partition = Lists.partition(collectBatchList, 20);
        for (List<CollectBatch> list : partition) {
            collectUseSize = moveCollectBatch(list, classifyId, oldClassifyId, user);
        }
        return collectUseSize;
    }

    @Transactional(rollbackFor = Exception.class)
    public CollectUseSize moveCollectBatch(List<CollectBatch> collectBatchList, String classifyId, String oldClassifyId, User user) {

        CollectUseSize collectUseSize = new CollectUseSize();

        List<String> promptIds = new ArrayList<>();
        List<String> fileNames = new ArrayList<>();

        for (CollectBatch collectBatch : collectBatchList) {
            promptIds.add(collectBatch.getPromptId());
            fileNames.add(collectBatch.getFileName());
        }

        // 获取该收藏夹已经存在的记录
        List<UserCollect> collectExist = getCollectByFileNameList(fileNames, promptIds, user, classifyId);
        VipStandards vipStandards = genService.getVipStandards(user);

        // 选中的图片有被收藏的
        if (!CollectionUtils.isEmpty(collectExist)) {
            // 选中的图片有全被收藏
            if (collectExist.size() >= collectBatchList.size()) {
                // 全部收藏 直接返回当前大小的num
                collectUseSize.setNum(0);
                collectUseSize.setUsedCollectNum(user.getUsedCollectNum());

                if (!Objects.isNull(vipStandards)) {
                    collectUseSize.setTotalCollectNum(vipStandards.getCollectNum());
                } else {
                    collectUseSize.setTotalCollectNum(500);
                }
                return collectUseSize;
            }

            // 剔除已被收藏的fileName 和 promptId
            fileNames = filterByCollect(fileNames, collectExist, "fileName");
            if (CollectionUtils.isEmpty(fileNames) || CollectionUtils.isEmpty(promptIds)) {
                // 全部收藏 直接返回当前大小的num
                collectUseSize.setNum(0);
                collectUseSize.setUsedCollectNum(user.getUsedCollectNum());

                if (!Objects.isNull(vipStandards)) {
                    collectUseSize.setTotalCollectNum(vipStandards.getCollectNum());
                } else {
                    collectUseSize.setTotalCollectNum(500);
                }

                return collectUseSize;
            }
        }


        List<UserCollect> collectByFileNameList = getCollectByFileNameList(fileNames, promptIds, user, oldClassifyId);

        if (CollectionUtils.isEmpty(collectByFileNameList)) {
            throw new BadRequestException("Please collect first !");
        }

        RLock lock = redissonClient.getLock(LockPrefixConstant.USER_DO_LOCK_PREFIX + user.getId());

        try {
            lock.lock();

            // 计算size 和提取 collectId 和 fileId
            List<Long> collectIds = new ArrayList<>();
            for (UserCollect userCollect : collectByFileNameList) {
                collectIds.add(userCollect.getId());
            }

            // 更改收藏数据的收藏夹id
            LambdaUpdateWrapper<UserCollect> luw = new LambdaUpdateWrapper();
            luw.set(UserCollect::getClassifyId, classifyId);
            luw.in(UserCollect::getId, collectIds);
            luw.eq(UserCollect::getLoginName, user.getLoginName());
            userCollectMapper.update(null, luw);

            // 更新老收藏夹数量 及 收藏夹容量 - size
            userCollectClassifyMapper.updateSizeAndNumReduce(Long.valueOf(oldClassifyId), user.getLoginName(), collectByFileNameList.size());

            //  判断是否为新收藏夹 如果是则复制第一张图片为封面
            LambdaQueryWrapper<UserCollectClassify> cfw = new LambdaQueryWrapper();
            cfw.eq(UserCollectClassify::getId, classifyId);
            cfw.eq(UserCollectClassify::getLoginName, user.getLoginName());
            UserCollectClassify userCollectClassify = userCollectClassifyMapper.selectOne(cfw);
            if (userCollectClassify.getCollectNums() <= 0 && StringUtils.isBlank(userCollectClassify.getCover())) {
                String newMiniThumbnailUrl = "";
                UserCollect userCollect = collectByFileNameList.get(0);
                // 复制文件
                copyImgToCollectAsync(
                        StringUtils.isNotBlank(userCollect.getMiniThumbnailUrl()) ? userCollect.getMiniThumbnailUrl() : userCollect.getThumbnailUrl(),
                        userCollectClassify);
                newMiniThumbnailUrl = userCollectClassify.getCover();
                LambdaUpdateWrapper<UserCollectClassify> ucw = new LambdaUpdateWrapper();

                ucw.set(StringUtils.isNotBlank(newMiniThumbnailUrl), UserCollectClassify::getCover, newMiniThumbnailUrl);
                ucw.eq(UserCollectClassify::getId, classifyId);
                ucw.eq(UserCollectClassify::getLoginName, user.getLoginName());
                userCollectClassifyMapper.update(null, ucw);
            }

            // 更新新收藏夹数量 及 收藏夹容量 + size
            userCollectClassifyMapper.updateSizeAndNumAdd(Long.valueOf(classifyId), user.getLoginName(), collectByFileNameList.size());

            // 用户可能在其他端操作了 收藏夹，需要更新用户信息
            user = userService.getUserById(user.getId());
            collectUseSize.setNum(0);
            collectUseSize.setUsedCollectNum(user.getUsedCollectNum());
            if (!Objects.isNull(vipStandards)) {
                collectUseSize.setTotalCollectNum(vipStandards.getCollectNum());
            } else {
                collectUseSize.setTotalCollectNum(500);
            }
        } catch (Exception e) {
            log.error("用户：{}，取消收藏图片操作失败", user.getLoginName());
            return null;
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return collectUseSize;
    }

    public Long getUserCollectNum(String loginName) {
        LambdaQueryWrapper<UserCollect> ucw = new LambdaQueryWrapper();
        ucw.eq(UserCollect::getLoginName, loginName);
        return userCollectMapper.selectCount(ucw);
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean updateUserCollectNum() {
        Long lastId = 0L;  // 初始时从id为0开始
        int pageSize = 1000; // 每批次处理的数量

        while (true) {
            // 查询 id 大于 lastId 且 UsedSize 不为0 的用户数据，按 id 排序
            LambdaQueryWrapper<User> uw = new LambdaQueryWrapper<>();
            uw.ne(User::getUsedSize, 0L)  // 过滤 UsedSize 为 0 的用户
                    .gt(User::getId, lastId)    // 查询 id 大于 lastId 的数据
                    .orderByAsc(User::getId)    // 按 id 升序排列
                    .last("LIMIT " + pageSize); // 每次查询 1000 条

            List<User> userList = userMapper.selectList(uw);
            if (userList.isEmpty()) {
                break;  // 如果没有更多数据，退出循环
            }

            // 记录最后一个用户的 id 以便下一次查询
            lastId = userList.get(userList.size() - 1).getId();

            // 使用一个集合批量处理
            Map<String, List<String>> tableNameMap = new HashMap<>();
            List<User> usersToUpdate = new ArrayList<>();

            // 遍历用户列表，整理需要更新的表和用户信息
            for (User user : userList) {
                String loginName = user.getLoginName();
                String tableName = calculateShardingTable(loginName, "gpt_user_collect", 20);

                // 将用户添加到对应的表名列表中
                tableNameMap.computeIfAbsent(tableName, k -> new ArrayList<>()).add(loginName);
            }
            // 批量查询每个分片表的用户数据
            for (Map.Entry<String, List<String>> entry : tableNameMap.entrySet()) {
                List<CollectLoginNameNum> collectLoginNameNums = userCollectMapper.selectNumByLoginNames(entry.getKey(), entry.getValue());
                if (!CollectionUtils.isEmpty(collectLoginNameNums)) {
                    // 将查询结果更新到 usersToUpdate 列表
                    for (CollectLoginNameNum numEntry : collectLoginNameNums) {
                        usersToUpdate.add(new User() {{
                            setLoginName(numEntry.getLoginName());
                            setUsedCollectNum(numEntry.getNum());
                        }});
                    }
                }
            }

            // 批量更新用户数据
            if (!usersToUpdate.isEmpty()) {
                // 执行批量更新
                userMapper.batchCollectionUpdateUsers(usersToUpdate);
            }
        }

        return Boolean.TRUE;
    }

    private CompletableFuture<Void> copyImgToCollectAsync(String url, UserCollectClassify userCollectClassify) {

        Map<String, String> copyMap = new HashMap<>();
        Map<String, String> copyMapOld = new HashMap<>();

        if (url != null) {
            String highThumbnailKey = buildFileKey("community", "webp");
            userCollectClassify.setCover(cosConfig.getCosAccelerateDomain() + highThumbnailKey);
            addToMap(url, highThumbnailKey, copyMap, copyMapOld);
        }

        CompletableFuture<Void> voidFuture = cosService.copyObject(copyMap, copyMapOld);
        return voidFuture;
    }

    private CompletableFuture<Void> copyImgToCoverAsync(String url, String highThumbnailKey) {

        Map<String, String> copyMap = new HashMap<>();
        Map<String, String> copyMapOld = new HashMap<>();

        if (url != null) {
            addToMap(url, highThumbnailKey, copyMap, copyMapOld);
        }

        CompletableFuture<Void> voidFuture = cosService.copyObject(copyMap, copyMapOld);
        return voidFuture;
    }

    private static void addToMap(String fileUrl, String fileKey, Map<String, String> copyMap, Map<String, String> copyMapOld) {
        if (org.apache.commons.lang3.StringUtils.isBlank(fileUrl)) {
            return;
        }
        if (fileUrl.contains("piclumen-1324066212")) {
            copyMapOld.put(URLUtil.getPath(fileUrl), fileKey);
        } else {
            copyMap.put(URLUtil.getPath(fileUrl), fileKey);
        }
    }

    public static String buildFileKey(String prefix, String fileExt) {
        StringBuilder sb = new StringBuilder("/" + prefix + "/");
        LocalDateTime now = LocalDateTime.now();
        String path = sb.append(DateUtil.format(now, "yyyyMMdd"))
                .append("/")
                .append(DateUtil.format(now, "HH"))
                .append("/")
                .append(IdUtils.randomUUID())
                .append(".")
                .append(fileExt)
                .toString();
        return path;
    }

    public Boolean setCollectClassifyCover(String id, User user, String cover) {
        // 拼接封面
        String highThumbnailKey = buildFileKey("community", "webp");
        String newCover = cosConfig.getCosAccelerateDomain() + highThumbnailKey;

        // 复制图片
        copyImgToCoverAsync(cover, highThumbnailKey);
        LambdaUpdateWrapper<UserCollectClassify> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(UserCollectClassify::getLoginName, user.getLoginName());
        updateWrapper.eq(UserCollectClassify::getId, id);
        updateWrapper.set(UserCollectClassify::getCover, newCover);
        updateWrapper.set(UserCollectClassify::getUpdateTime, LocalDateTime.now());
        updateWrapper.set(UserCollectClassify::getUpdateBy, user.getLoginName());
        int update = userCollectClassifyMapper.update(null, updateWrapper);
        if (update <= 0) {
            log.info("renameCollectClassify error");
            throw new ServerInternalException("Failed to cover");
        }
        return Boolean.TRUE;
    }

    public CollectUseSize reduceCollectDeleteBatch(List<CollectBatch> collectBatchList, String classifyId, User user) {
        CollectUseSize collectUseSize = reduceCollectBatchSegmentation(collectBatchList, classifyId, user);

        // 若 result 为空，则直接返回失败
        if (Objects.isNull(collectUseSize)) {
            return null;
        }
        // 通过 Stream API 处理 collectBatchList -> imgDeleteList
        List<ImgDelete> imgDeleteList = collectBatchList.stream()
                .map(collect -> {
                    ImgDelete imgDelete = new ImgDelete();
                    imgDelete.setImgName(collect.getFileName());
                    imgDelete.setPromptId(collect.getPromptId());
                    return imgDelete;
                })
                .collect(Collectors.toList());

        imgService.deleteByList(imgDeleteList, user);
        return collectUseSize;
    }

}
