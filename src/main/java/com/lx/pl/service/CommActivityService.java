package com.lx.pl.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lx.pl.constant.LogicConstants;
import com.lx.pl.db.mysql.community.activity.CommActivity;
import com.lx.pl.db.mysql.community.entity.CommBlacklist;
import com.lx.pl.db.mysql.community.entity.CommUserActivityRecord;
import com.lx.pl.db.mysql.gen.entity.Album;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.db.mysql.gen.mapper.CommActivityMapper;
import com.lx.pl.db.mysql.gen.mapper.CommActivityPrizeSettingsMapper;
import com.lx.pl.dto.AlbumResult;
import com.lx.pl.dto.PromptPageInfo;
import com.lx.pl.dto.community.activity.CommActivityDetailResult;
import com.lx.pl.dto.community.activity.CommActivityPostingResult;
import com.lx.pl.dto.community.activity.CommActivityResult;
import com.lx.pl.exception.ServerInternalException;
import com.mongodb.client.result.UpdateResult;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;

@Service
@Slf4j
public class CommActivityService extends ServiceImpl<CommActivityMapper, CommActivity> {

    @Autowired
    private CommActivityMapper commActivityMapper;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private RedisService redisService;

    public PromptPageInfo<CommActivityResult> getCommActivityList(Integer pageNum, Integer pageSize, User user) {
        //分页查询任务
        LocalDateTime now = LocalDateTime.now();
        LambdaQueryWrapper<CommActivity> qw = new LambdaQueryWrapper<>();
        qw.orderByDesc(CommActivity::getCreateTime);
        qw.le(CommActivity::getBeginTime, now);
        qw.eq(CommActivity::getPublish, true);
        qw.select(CommActivity::getId, CommActivity::getTitle, CommActivity::getBeginTime, CommActivity::getEndTime, CommActivity::getPostEndTime,
                CommActivity::getTags, CommActivity::getCover, CommActivity::getMaxSubmissions,
                CommActivity::getPublish, CommActivity::getReward, CommActivity::getImageNum);

        IPage<CommActivity> page = commActivityMapper.selectPage(new Page<>(pageNum, pageSize), qw);
        List<CommActivity> commActivityList = page.getRecords();

        List<CommActivityResult> activityResultList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(commActivityList)) {
            for (CommActivity activity : commActivityList) {
                CommActivityResult activityResult = new CommActivityResult();
                BeanUtils.copyProperties(activity, activityResult);
                activityResultList.add(activityResult);
            }
        }

        PromptPageInfo<CommActivityResult> promptPageInfo = new PromptPageInfo<>();
        promptPageInfo.setResultList(activityResultList);
        promptPageInfo.setPageNum(pageNum);
        promptPageInfo.setPageSize(pageSize);
        updateCommActivityRecord(user);
        return promptPageInfo;
    }

    public List<CommActivityPostingResult> getCommActivityPostingList() {
        LocalDateTime now = LocalDateTime.now();
        LambdaQueryWrapper<CommActivity> qw = new LambdaQueryWrapper<>();
        qw.eq(CommActivity::getPublish, true);
        qw.le(CommActivity::getBeginTime, now);
        qw.gt(CommActivity::getPostEndTime, now);
        qw.select(CommActivity::getId, CommActivity::getTitle);

        List<CommActivity> commActivities = commActivityMapper.selectList(qw);

        List<CommActivityPostingResult> activityPostingResultList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(commActivities)) {
            for (CommActivity activity : commActivities) {
                CommActivityPostingResult activityResult = new CommActivityPostingResult();
                BeanUtils.copyProperties(activity, activityResult);
                activityPostingResultList.add(activityResult);
            }
        }

        return activityPostingResultList;
    }

    public CommActivityDetailResult getCommActivityDetail(Long id, User user) {
        CommActivity activity = commActivityMapper.selectById(id);

        CommActivityDetailResult activityResult = new CommActivityDetailResult();
        BeanUtils.copyProperties(activity, activityResult);

        Boolean notExitKeyAndValueInSet = redisService.isNotExitKeyAndValueInSet(LogicConstants.COMM_ACTIVITY_PUBLIC, id);

        // 此活动在缓存当中 处理用户已读情况
        if (notExitKeyAndValueInSet) {
            addActivityIdToUser(user, id);
        }
        return activityResult;
    }

    /**
     * 向指定用户的 activityIds 集合中添加一个 activityId（不会重复添加）
     *
     * @param user       登录用户
     * @param activityId 活动ID
     */
    public void addActivityIdToUser(User user, Long activityId) {
        Set<Long> publicActivityIds = redisService.getDataFromSet(LogicConstants.COMM_ACTIVITY_PUBLIC);
        if (CollectionUtils.isEmpty(publicActivityIds)) {
            return;
        }

        int userActivityCount = (int) Optional.ofNullable(
                redisService.getDataFromHash(LogicConstants.COMM_ACTIVITY_USER_NUM, user.getLoginName())
        ).orElse(0);

        if (publicActivityIds.size() > userActivityCount) {
            Query query = new Query(Criteria.where("userId").is(user.getId()));

            Update update = new Update()
                    .addToSet("activityIds", activityId)
                    .setOnInsert("userId", user.getId());

            UpdateResult result = mongoTemplate.upsert(query, update, CommUserActivityRecord.class);

            // 如果是新增记录或成功添加了新 activityId，则更新缓存
            if (result.getUpsertedId() != null || result.getModifiedCount() > 0) {
                redisService.incrementFieldInHash(
                        LogicConstants.COMM_ACTIVITY_USER_NUM,
                        user.getLoginName(),
                        1
                );
            }
        }
    }


    public void updateCommActivityRecord(User user) {
        Set<Long> publicActivityIds = redisService.getDataFromSet(LogicConstants.COMM_ACTIVITY_PUBLIC);
        if (CollectionUtils.isEmpty(publicActivityIds)) {
            return;
        }

        int userActivityCount = (int) Optional.ofNullable(
                redisService.getDataFromHash(LogicConstants.COMM_ACTIVITY_USER_NUM, user.getLoginName())
        ).orElse(0);

        if (publicActivityIds.size() > userActivityCount) {
            Query query = new Query(Criteria.where("userId").is(user.getId()));

            Update update = new Update()
                    .addToSet("activityIds").each(publicActivityIds)
                    .setOnInsert("userId", user.getId());

            mongoTemplate.upsert(query, update, CommUserActivityRecord.class);

            redisService.putDataToHash(
                    LogicConstants.COMM_ACTIVITY_USER_NUM,
                    user.getLoginName(),
                    publicActivityIds.size()
            );
        }
    }


}
