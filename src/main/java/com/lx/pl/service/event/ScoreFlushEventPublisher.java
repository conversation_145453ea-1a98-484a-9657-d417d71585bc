package com.lx.pl.service.event;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class ScoreFlushEventPublisher {

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    public void publishCustomEvent(final String commFileId) {
        ScoreFlushEvent customEvent = new ScoreFlushEvent(this, commFileId);
        applicationEventPublisher.publishEvent(customEvent);
    }
}
