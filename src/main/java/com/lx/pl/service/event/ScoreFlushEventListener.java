package com.lx.pl.service.event;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.RandomUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class ScoreFlushEventListener implements ApplicationListener<ScoreFlushEvent> {

    @Async(value = "scoreFlushExecutor")
    @Override
    public void onApplicationEvent(ScoreFlushEvent event) {
        System.out.println("########start Received custom event - " + event.getCommFileId() + " in thread " + Thread.currentThread().getName());
        ThreadUtil.sleep(RandomUtil.randomInt(3000, 7000));
        System.out.println("########end Received custom event - " + event.getCommFileId() + " in thread " + Thread.currentThread().getName());
    }
}
