//package com.lx.pl.service.event;
//
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.CommandLineRunner;
//import org.springframework.stereotype.Component;
//
//@Component
//public class EventRunner implements CommandLineRunner {
//
//    @Autowired
//    private ScoreFlushEventPublisher customEventPublisher;
//
//    @Override
//    public void run(String... args) throws Exception {
//        new Thread(() -> customEventPublisher.publishCustomEvent("Event from Thread 1")).start();
//        new Thread(() -> customEventPublisher.publishCustomEvent("Event from Thread 2")).start();
//    }
//}
