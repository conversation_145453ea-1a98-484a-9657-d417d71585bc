package com.lx.pl.service;

import com.lx.pl.config.CosConfig;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.model.COSObject;
import com.qcloud.cos.model.GetObjectRequest;
import com.qcloud.cos.model.ObjectMetadata;
import com.qcloud.cos.utils.IOUtils;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.InputStream;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ImgDownloadCommonService {

    @Resource
    public COSClient nwCosClient;
    @Resource(name = "baseCosClient")
    @Getter
    private COSClient baseCosClient;
    @Resource
    public CosConfig cosConfig;

    /**
     * 直接走内网
     *
     * @param fileKey
     * @return
     */
    public ObjectMetadata downloadToFileNoAccelerate(String fileKey, File saveFile) {
        GetObjectRequest getObjectRequest = new GetObjectRequest(cosConfig.getBucket(), fileKey);
        return baseCosClient.getObject(getObjectRequest, saveFile);
    }

    /**
     * 内网全球加速
     *
     * @param fileKey
     * @return
     */
    public ObjectMetadata downloadToFileWithAccelerate(String fileKey, File saveFile) {
        GetObjectRequest getObjectRequest = new GetObjectRequest(cosConfig.getBucket(), fileKey);
        return nwCosClient.getObject(getObjectRequest, saveFile);
    }

    public byte[] downloadNoAccelerate(String fileKey) {
        GetObjectRequest getObjectRequest = new GetObjectRequest(cosConfig.getBucket(), fileKey);
        COSObject cosObject = baseCosClient.getObject(getObjectRequest);
        try (InputStream cosObjectInput = cosObject.getObjectContent()) {
            // 处理下载到的流
            byte[] bytes = IOUtils.toByteArray(cosObjectInput);
            return bytes;
        } catch (Exception e) {
            log.error("下载文件失败，fileKey: {}", fileKey);
        }
        return null;
    }
}
