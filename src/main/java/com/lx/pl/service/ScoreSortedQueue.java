package com.lx.pl.service;

import java.util.Comparator;
import java.util.PriorityQueue;
import java.util.Queue;

/**
 * <AUTHOR>
 */
public class ScoreSortedQueue {
    public static final int MAX_SIZE = 5_0000;
    private Queue<ScoreEntry> scoreQueue = new PriorityQueue<>(MAX_SIZE + 1, Comparator.comparingDouble(ScoreEntry::getScore));

    public void addScore(String value, double score) {
        scoreQueue.offer(new ScoreEntry(value, score));
        if (scoreQueue.size() > MAX_SIZE) {
            // 如果超过最大容量，移除最小的元素
            scoreQueue.poll();
        }
    }

    public void clear() {
        scoreQueue.clear();
    }

    public boolean isEmpty() {
        return scoreQueue.isEmpty();
    }

    public Queue<ScoreEntry> getScoreQueue() {
        return scoreQueue;
    }

    public static void main(String[] args) {
        ScoreSortedQueue scoreSortedQueue = new ScoreSortedQueue();

        // 添加一些示例数据
        scoreSortedQueue.addScore("value1", 62.5);
        scoreSortedQueue.addScore("value2", 100.0);
        scoreSortedQueue.addScore("value2", 70.0);
        scoreSortedQueue.addScore("value2", 90.0);
        scoreSortedQueue.addScore("value2", 90.0);
        scoreSortedQueue.addScore("value3", 55.0);
        scoreSortedQueue.addScore("value3", 11.0);
        scoreSortedQueue.addScore("value3", 11.0);
        scoreSortedQueue.addScore("value3", 100.0);


        // 打印结果
        for (ScoreEntry entry : scoreSortedQueue.getScoreQueue()) {
            System.out.println("Score: " + entry.getScore() + ", Value: " + entry.getValue());
        }
    }

    static class ScoreEntry {
        private String value;
        private double score;

        public ScoreEntry(String value, double score) {
            this.value = value;
            this.score = score;
        }

        public String getValue() {
            return value;
        }

        public double getScore() {
            return score;
        }
    }
}
