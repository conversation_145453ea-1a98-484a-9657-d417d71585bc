package com.lx.pl.service;


import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

import com.fasterxml.jackson.databind.JsonNode;
import com.lx.pl.util.JsonUtils;
import com.lx.pl.util.StringUtils;
import com.lx.pl.util.TimeWheelTool;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.*;

@Slf4j
@Component
public class WebSocketServer implements WebSocketHandler {

    private static final Logger logger = LoggerFactory.getLogger("ws-task");

    /**
     * 静态变量，用来记录当前在线连接数，线程安全的类。
     */
    private static AtomicInteger onlineSessionClientCount = new AtomicInteger(0);

    /**
     * 存放所有在线的客户端
     */
    private static Map<String, WebSocketSession> onlineSessionClientMap = new ConcurrentHashMap<>();
    private static Map<String, WebSocketSession> comfyUISessionMap = new ConcurrentHashMap<>();

//  private static Map<String, WebSocketSession> clientSessionMap = new ConcurrentHashMap<>();

    public static final String ACK = "ack";

    @Autowired
    RedisService redisService;

    /**
     * 群发消息
     *
     * @param message 消息
     */
    private void sendToAll(String message) {
        // 遍历在线map集合
        onlineSessionClientMap.forEach((onlineSid, toSession) -> {
            // 排除掉自己
            logger.debug("服务端给客户端群发消息 ==> sid = {}, message = {}", onlineSid,
                    message);
            try {
                toSession.sendMessage(new TextMessage(message));
            } catch (IOException e) {
                e.printStackTrace();
            }
        });
    }

    /**
     * 指定发送消息(去除websocket连接)
     *
     * @param toSid
     * @param message
     */
    public synchronized void sendToOne(String toSid, String message) {
//        // 通过sid查询map中是否存在
//        WebSocketSession toSession = onlineSessionClientMap.get(toSid);
//        if (toSession == null) {
//            logger.error("服务端给客户端发送消息 ==> toSid = {} 不存在, message = {}", toSid, message);
//            return;
//        }
//        // 异步发送
//        logger.debug("服务端给客户端发送消息 ==> toSid = {}, message = {}", toSid, message);
////    toSession.getAsyncRemote().sendText(message);
//
//        // 同步发送
//        try {
//            toSession.sendMessage(new TextMessage(message));
//        } catch (IOException e) {
//            logger.error("发送消息失败，WebSocket IO异常");
//            e.printStackTrace();
//        }
    }

    /**
     * 建立连接
     *
     * @param session
     * @throws Exception
     */
    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        //获取路径sid
        String sid = (session.getUri().getPath()).split("/")[2];
        logger.debug("connect establishing... ==> session_id = {}， sid = {}", session.getId(), sid);
        onlineSessionClientMap.put(sid, session);

        //在线数加1
        onlineSessionClientCount.incrementAndGet();
        sendToOne(sid, "连接成功");
        logger.debug(
                "用户websocket连接成功，online count：{} ==> start listen new connect：session_id = {}， sid = {},。",
                onlineSessionClientCount, session.getId(), sid);
    }

    /**
     * 处理前端发来的消息
     *
     * @param session
     * @param message
     * @throws Exception
     */
    @Override
    public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {
        /**
         * html界面传递来得数据格式，可以自定义.
         * {"sid":"user-1","message":"hello websocket"}
         */
        String toSid = "";

        String msg = "";
        if (message.getPayload() instanceof String) {
            // 处理文本消息
            TextMessage textMessage = (TextMessage) message;
            System.out.println("Received text message: " + textMessage.getPayload());
            JsonNode jsonNode = JsonUtils.writeStringToJsonNode(textMessage.getPayload().toString());
            toSid = jsonNode.path("sid").asText();
            msg = jsonNode.path("message").asText();
            if (StringUtils.isNotBlank(msg) && msg.equals(ACK)) {
                String markId = jsonNode.path("markId").asText();
                doAck(toSid, markId);
                return;
            }

        }
        logger.debug("服务端收到客户端消息 ==> fromSid = {}, toSid = {}, message = {}", toSid, toSid,
                message);

        /**
         * 模拟约定：如果未指定sid信息，则群发，否则就单独发送
         */
        if (toSid == null || toSid == "" || "".equalsIgnoreCase(toSid)) {
            sendToAll(msg);
        } else {
            sendToOne(toSid, msg);
        }
    }

    private void doAck(String toSid, String markId) {
        String key = toSid + markId;
        TimeWheelTool.stopTask(key);
        redisService.deleteFieldFromHash(GenService.WsErrorMessagekey, key);
    }


    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        logger.error("WebSocket发生错误，错误信息为：" + exception.getMessage());
        exception.printStackTrace();
    }

    /**
     * 断开连接
     *
     * @param session
     * @param closeStatus
     * @throws Exception
     */
    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
        //获取路径sid
        String sid = (session.getUri().getPath()).split("/")[2];
        onlineSessionClientMap.remove(sid);
        //在线数减1
        onlineSessionClientCount.decrementAndGet();
        logger.debug(
                "用户websocket关闭成功，online count：{} ==> close connection info：session_id = {}， sid = {},。",
                onlineSessionClientCount, session.getId(), sid);
    }

    @Override
    public boolean supportsPartialMessages() {
        return false;
    }
}
