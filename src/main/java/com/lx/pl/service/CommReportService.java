package com.lx.pl.service;

import com.lx.pl.db.mysql.community.entity.AccountInfo;
import com.lx.pl.db.mysql.community.entity.CommComment;
import com.lx.pl.db.mysql.community.entity.CommCommentReport;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.db.mysql.gen.repository.CommCommentReportRepository;
import com.lx.pl.exception.ServerInternalException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
@Slf4j
public class CommReportService {

    @Autowired
    private CommCommentReportRepository commCommentReportRepository;

    @Autowired
    private CommCommentService commCommentService;

    @Autowired
    private MongoTemplate mongoTemplate;

    /**
     * 对社区评论进行举报
     *
     * @param commFileId
     * @param commentId
     * @param user
     * @return
     */
    public Boolean reportCommComment(String commFileId, String commentId, Integer auditType, String otherContent, User user) {
        try {
            CommComment commComment = commCommentService.getCommCommentById(commentId);
            if (!Objects.isNull(commComment)) {
                CommCommentReport commCommentReport = new CommCommentReport();
                commCommentReport.setFileId(commFileId);
                commCommentReport.setCommentId(commentId);
                //举报发起者用户
                AccountInfo reportAccInfo = AccountInfo.builder()
                        .userId(user.getId())
                        .userName(user.getUserName())
                        .userLoginName(user.getLoginName())
                        .userAvatarUrl(user.getAvatarUrl())
                        .whetherPro(Boolean.FALSE)
                        .build();
                commCommentReport.setOwnerAcc(reportAccInfo);
                commCommentReport.setAuditType(auditType);
                commCommentReport.setOtherContent(otherContent);
                commCommentReportRepository.insert(commCommentReport);
                return Boolean.TRUE;
            }
        } catch (Exception e) {
            log.error("用户：{} 对评论：{} 举报报错", user.getId(), commentId, e);
            throw new ServerInternalException("Report Failed");
        }

        return Boolean.FALSE;
    }


    public CommCommentReport getCommReportByUserIdAndCommentId(Long userId, String commentId) {
        try {
            Query query = new Query();
            query.addCriteria(Criteria.where("commentId").is(commentId))
                    .addCriteria(Criteria.where("ownerAcc.userId").is(userId));
            CommCommentReport commReport = mongoTemplate.findOne(query, CommCommentReport.class);
            return commReport;
        } catch (Exception e) {
            log.error("查询社区评论举报报错，评论id为：{}，用户id:{}", commentId, userId, e);
            return null;
        }
    }

}
