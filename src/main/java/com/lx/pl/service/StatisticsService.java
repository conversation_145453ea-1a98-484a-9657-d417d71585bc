package com.lx.pl.service;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lx.pl.client.EmailApi;
import com.lx.pl.client.SendCloudEmailClient;
import com.lx.pl.db.mysql.gen.entity.*;
import com.lx.pl.db.mysql.gen.mapper.*;
import com.lx.pl.dto.SendCloudMailResult;
import com.lx.pl.dto.common.Statistics;
import com.lx.pl.enums.GenModeType;
import com.lx.pl.enums.ModelType;
import com.lx.pl.enums.OriginCreate;
import com.lx.pl.util.DateUtils;
import com.lx.pl.util.JsonUtils;
import com.sendcloud.sdk.util.ResponseData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import retrofit2.Response;

import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;


@Service
@Slf4j
public class StatisticsService {

    @Value("${sendcloud.api.key}")
    String apiKey;
    @Value("${sendcloud.api.user}")
    String apiUser;
    @Value("${opex.loginName}")
    String opexLoginName;

    @Autowired
    PromptFileMapper promptFileMapper;

    @Autowired
    EventLogMapper eventLogMapper;

    @Autowired
    UserMapper userMapper;

    @Autowired
    SendCloudEmailClient sendCloudEmailClient;

    @Autowired
    ResourceLoader resourceLoader;

    @Autowired
    RedisService<String> redisService;

    @Autowired
    PromptRecordMapper promptRecordMapper;

    @Autowired
    KpiMixMapper kpiMixMapper;

    @Autowired
    OpsInfoMapper opsInfoMapper;

    @Autowired
    GptFileMapper gptFileMapper;

    @Autowired
    EventLogTestMapper eventLogTestMapper;

    @Autowired
    EventLogService eventLogService;

    @Autowired
    PromptFileService promptFileService;

    @Autowired
    PromptRecordService promptRecordService;

    @Autowired
    EventLogImportDataService eventLogImportDataService;

    @Autowired
    GptRecordMapper gptRecordMapper;

    @Autowired
    ExportGenInfoMapper exportGenInfoMapper;


    public void statisticsCreatePicture() {
        Date beijingDate = new Date();
        //当天时间凌晨
        String theDayMidnight = DateUtils.formatDateByPattern(beijingDate, DateUtils.YYYY_MM_DD_00_00_00);
        //昨天时间凌晨
        Date yesterdayDate = new Date(beijingDate.getTime() - 1000 * 60 * 60 * 24);
        String yesterdayMidnight = DateUtils.formatDateByPattern(new Date(beijingDate.getTime() - 1000 * 60 * 60 * 24), DateUtils.YYYY_MM_DD_00_00_00);
        //当前7天前凌晨
        String sevenDayDateBefore = DateUtils.formatDateByPattern(DateUtils.getNumsDayDateBefore(yesterdayDate, -7), DateUtils.YYYY_MM_DD_00_00_00);
        //当前30天前凌晨
        String thirtyDayDateBefore = DateUtils.formatDateByPattern(DateUtils.getNumsDayDateBefore(yesterdayDate, -30), DateUtils.YYYY_MM_DD_00_00_00);

        //不统计运维账户信息
        List<String> opexLoginNameList = new ArrayList<>();
        List<String> opexUserIdList = new ArrayList<>();
        if (StringUtil.isNotBlank(opexLoginName)) {
            opexLoginNameList = Arrays.stream(opexLoginName.split(",")).collect(Collectors.toList());
            opexUserIdList = userMapper.getUserIdByLoginNames(opexLoginNameList);
        }

        Map<String, Object> statisticsMap = new HashMap<>();
        Double taskAvgTime = null;
        try {
            //用户发起生图到生图完成的平均时间
            taskAvgTime = promptFileMapper.getTaskAvgTime(yesterdayMidnight, theDayMidnight, opexLoginNameList);
            if (Objects.isNull(taskAvgTime)) {
                taskAvgTime = 0.0;
            }
            statisticsMap.put("taskAvgTime", taskAvgTime);
        } catch (Exception e) {
            log.error("用户发起生图到生图完成的平均时间报错,时间为：{}，报错信息为：{}", DateUtils.getTime(), e);
        }

        Long createPictureNums = null;
        try {
            //每日生图的数量
            createPictureNums = promptFileMapper.getCreatePictureNums(yesterdayMidnight, theDayMidnight, opexLoginNameList);
            if (Objects.isNull(createPictureNums)) {
                createPictureNums = 0L;
            }
            statisticsMap.put("createPictureNums", createPictureNums);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("每日生图的数量报错,时间为：{}，报错信息为：{}", DateUtils.getTime(), e);
        }

        Long useOperateNumsByDay = null;
        try {
            //日活
            useOperateNumsByDay = eventLogMapper.getUseOperateNumsByTime(DateUtils.stringToLocalDate(yesterdayMidnight), DateUtils.stringToLocalDate(theDayMidnight), opexUserIdList);
            if (Objects.isNull(useOperateNumsByDay)) {
                useOperateNumsByDay = 0L;
            }
            statisticsMap.put("useOperateNumsByDay", useOperateNumsByDay);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("日活报错,时间为：{}，报错信息为：{}", DateUtils.getTime(), e);
        }

        Long useOperateNumsByWeek = null;
        try {
            //周活
            useOperateNumsByWeek = eventLogMapper.getUseOperateNumsByTime(DateUtils.stringToLocalDate(sevenDayDateBefore), DateUtils.stringToLocalDate(theDayMidnight), opexUserIdList);
            if (Objects.isNull(useOperateNumsByWeek)) {
                useOperateNumsByWeek = 0L;
            }
            statisticsMap.put("useOperateNumsByWeek", useOperateNumsByWeek);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("周活报错,时间为：{}，报错信息为：{}", DateUtils.getTime(), e);
        }

        Long useOperateNumsByMonth = null;
        try {
            //月活
            useOperateNumsByMonth = eventLogMapper.getUseOperateNumsByTime(DateUtils.stringToLocalDate(thirtyDayDateBefore), DateUtils.stringToLocalDate(theDayMidnight), opexUserIdList);
            if (Objects.isNull(useOperateNumsByMonth)) {
                useOperateNumsByMonth = 0L;
            }
            statisticsMap.put("useOperateNumsByMonth", useOperateNumsByMonth);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("月活报错,时间为：{}，报错信息为：{}", DateUtils.getTime(), e);
        }

        Long userNums = null;
        try {
            //每日新注册用户数量
            userNums = userMapper.getUserNums(yesterdayMidnight, theDayMidnight, opexLoginNameList);
            if (Objects.isNull(userNums)) {
                userNums = 0L;
            }
            statisticsMap.put("userNums", userNums);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("每日新注册用户数量报错,时间为：{}，报错信息为：{}", DateUtils.getTime(), e);
        }

        Long allUserNums = null;
        try {
            //当前注册用户总数量
            allUserNums = userMapper.getUserNums("", "", opexLoginNameList);
            if (Objects.isNull(allUserNums)) {
                allUserNums = 0L;
            }
            statisticsMap.put("allUserNums", allUserNums);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("当前注册用户总数量报错,时间为：{}，报错信息为：{}", DateUtils.getTime(), e);
        }

        Long avgCratePictureNums = null;
        try {
            //单个用户生图平均数量
            avgCratePictureNums = promptFileMapper.getUserAvgCratePictureNums(yesterdayMidnight, theDayMidnight, opexLoginNameList);
            if (Objects.isNull(avgCratePictureNums)) {
                avgCratePictureNums = 0L;
            }
            statisticsMap.put("avgCratePictureNums", avgCratePictureNums);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("单个用户生图平均数量报错,时间为：{}，报错信息为：{}", DateUtils.getTime(), e);
        }


        try {
            LambdaQueryWrapper<OpsInfo> lwq = new LambdaQueryWrapper();
            lwq.eq(OpsInfo::getSendOpsInfo, Boolean.TRUE);
            List<OpsInfo> opsInfoList = opsInfoMapper.selectList(lwq);
            if (!CollectionUtils.isEmpty(opsInfoList)) {
                String statisticsUserMail = String.join(";", opsInfoList.stream()
                        .map(OpsInfo::getEmail)
                        .collect(Collectors.toList()));
                sendMail(statisticsUserMail, yesterdayDate, statisticsMap);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

        //生图最多的用户排名前100
        //List<StatisticsUser> statisticsUserList = userMapper.getUserTopPictureNums("2024-05-01 00:00:00","2024-07-10 00:00:00");

        //用户平均等待时间（点击生图到用户开始生图的平均时间）
        Double genPicAvgTime = promptRecordMapper.getGenPicAvgTime(yesterdayMidnight, theDayMidnight, opexLoginNameList);
        if (Objects.isNull(genPicAvgTime)) {
            genPicAvgTime = 0.0;
        }
        statisticsMap.put("genPicAvgTime", genPicAvgTime);

        //单个用户生图最大数量
        Long userMaxCratePictureNums = promptFileMapper.getUserMaxCratePictureNums(yesterdayMidnight, theDayMidnight, opexLoginNameList);
        if (Objects.isNull(userMaxCratePictureNums)) {
            userMaxCratePictureNums = 0L;
        }
        statisticsMap.put("userMaxCratePictureNums", userMaxCratePictureNums);

        //当天生图任务总数
        Long genNums = promptRecordMapper.getGenCreate(yesterdayMidnight, theDayMidnight, opexLoginNameList, "");

        //当天快速生图任务总数
        Long genFastNums = promptRecordMapper.getGenCreate(yesterdayMidnight, theDayMidnight, opexLoginNameList, GenModeType.fast.getValue());

        //分别统计用户生图喜欢张数
        List<Statistics> genBatchSizeList = promptRecordMapper.getGenBatchSize(yesterdayMidnight, theDayMidnight, opexLoginNameList);
        StringBuilder sb = new StringBuilder();
        Map<String, String> batchSizeMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(genBatchSizeList)) {
            for (Statistics statistics : genBatchSizeList) {
                batchSizeMap.put(statistics.getLabel(), statistics.getValue());
                sb.append("生图数" + statistics.getLabel() + ": " + String.format("%.2f%%",
                        (Double.valueOf(statistics.getValue()) / genNums) * 100)).append("  ");
                statisticsMap.put("batchSize" + statistics.getLabel(), "生图数" + statistics.getLabel() + ": " + String.format("%.2f%%",
                        (Double.valueOf(statistics.getValue()) / genNums) * 100));
            }
        }

        //分别统计用户不同生图类型
        List<Statistics> genOriginCreateList = promptRecordMapper.getGenOriginCreate(yesterdayMidnight, theDayMidnight, opexLoginNameList);
        StringBuilder sbOrigin = new StringBuilder();
        Map<String, String> genOriginCreateMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(genOriginCreateList)) {
            for (Statistics statistics : genOriginCreateList) {
                genOriginCreateMap.put(statistics.getLabel(), statistics.getValue());
                sbOrigin.append("生图类型为" + OriginCreate.getLabelByValue(statistics.getLabel()) + "占比 : " + String.format("\"%.2f%%\"",
                        (Double.valueOf(statistics.getValue()) / genNums) * 100)).append("  ");
            }
        }

        //分别统计用户生图模型
        List<Statistics> genModelIdList = promptRecordMapper.getGenModelId(yesterdayMidnight, theDayMidnight, opexLoginNameList);
        Map<String, String> modelIdMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(genModelIdList)) {
            for (Statistics statistics : genModelIdList) {
                modelIdMap.put(ModelType.getValueByLabel(statistics.getLabel()), statistics.getValue());
            }
        }

        //统计用户最喜欢的生图比例
        List<Statistics> maxAspectRatioList = promptRecordMapper.getMaxAspectRatio(yesterdayMidnight, theDayMidnight, opexLoginNameList);
        Statistics statistics = new Statistics();
        if (!CollectionUtils.isEmpty(maxAspectRatioList)) {
            statistics = maxAspectRatioList.get(0);
        }

        //生成一张图所需要的平均时间
        Double batchSizeOneAvgTime = promptRecordMapper.getGenBatchSizePicAvgTime(yesterdayMidnight, theDayMidnight, 1, opexLoginNameList);
        //生成二张图所需要的平均时间
        Double batchSizeTwoAvgTime = promptRecordMapper.getGenBatchSizePicAvgTime(yesterdayMidnight, theDayMidnight, 2, opexLoginNameList);
        //生成三张图所需要的平均时间
        Double batchSizeThreeAvgTime = promptRecordMapper.getGenBatchSizePicAvgTime(yesterdayMidnight, theDayMidnight, 3, opexLoginNameList);
        //生成四张图所需要的平均时间
        Double batchSizeFourAvgTime = promptRecordMapper.getGenBatchSizePicAvgTime(yesterdayMidnight, theDayMidnight, 4, opexLoginNameList);
        //生成四张图所需要的平均时间
        Long characterReferNums = promptRecordMapper.getImgToImgStyleNums(yesterdayMidnight, theDayMidnight, "characterRefer", opexLoginNameList);
        //生成四张图所需要的平均时间
        Long styleReferNums = promptRecordMapper.getImgToImgStyleNums(yesterdayMidnight, theDayMidnight, "styleRefer", opexLoginNameList);
        //生成四张图所需要的平均时间
        Long contentReferNums = promptRecordMapper.getImgToImgStyleNums(yesterdayMidnight, theDayMidnight, "contentRefer", opexLoginNameList);

        Double dayMaxTaskSizeValue = (Double) redisService.getDataFromZset("dayMaxTaskSize", DateFormatUtils.format(yesterdayDate, "yyyyMMdd"));
        Long dayMaxTaskSize = Objects.isNull(dayMaxTaskSizeValue) ? 0L : dayMaxTaskSizeValue.longValue();

        KpiMix kpiMix = new KpiMix();
        kpiMix.setRecordDate(DateUtils.dateToLocalDate(yesterdayDate));
        kpiMix.setAvgChartTime(taskAvgTime);
        kpiMix.setAvgWait(genPicAvgTime);
        kpiMix.setAvgChartOneTime(batchSizeOneAvgTime);
        kpiMix.setAvgChartTwoTime(batchSizeTwoAvgTime);
        kpiMix.setAvgChartThreeTime(batchSizeThreeAvgTime);
        kpiMix.setAvgChartFourTime(batchSizeFourAvgTime);
        kpiMix.setChartCount(createPictureNums);
        kpiMix.setDau(useOperateNumsByDay);
        kpiMix.setWau(useOperateNumsByWeek);
        kpiMix.setMau(useOperateNumsByMonth);
        kpiMix.setNewRegisters(userNums);
        kpiMix.setTotalRegisters(allUserNums);
        kpiMix.setAvgChartsPerUser(avgCratePictureNums);
        kpiMix.setMaxConcurrentChartTasks(dayMaxTaskSize);
        kpiMix.setMaxChartsPerUser(userMaxCratePictureNums);
        kpiMix.setChartCountOne(batchSizeMap.containsKey("1") ? Long.valueOf(batchSizeMap.get("1")) : 0L);
        kpiMix.setChartCountTwo(batchSizeMap.containsKey("2") ? Long.valueOf(batchSizeMap.get("2")) : 0L);
        kpiMix.setChartCountThree(batchSizeMap.containsKey("3") ? Long.valueOf(batchSizeMap.get("3")) : 0L);
        kpiMix.setChartCountFour(batchSizeMap.containsKey("4") ? Long.valueOf(batchSizeMap.get("4")) : 0L);
        kpiMix.setText2picTasks(genOriginCreateMap.containsKey("create") ? Long.valueOf(genOriginCreateMap.get("create")) : 0L);
        kpiMix.setHiresfixTasks(genOriginCreateMap.containsKey("hiresFix") ? Long.valueOf(genOriginCreateMap.get("hiresFix")) : 0L);
        kpiMix.setRemovebgTasks(genOriginCreateMap.containsKey("removeBackground") ? Long.valueOf(genOriginCreateMap.get("removeBackground")) : 0L);
        kpiMix.setPic2picTasks(genOriginCreateMap.containsKey("picCreate") ? Long.valueOf(genOriginCreateMap.get("picCreate")) : 0L);
        kpiMix.setFastTasks(genFastNums);
        kpiMix.setChartTaskCount(genNums);
        kpiMix.setPic2picCharacterRef(characterReferNums);
        kpiMix.setPic2picStyleRef(styleReferNums);
        kpiMix.setPic2picContentRef(contentReferNums);
        kpiMix.setFavoriteAspectRatio(statistics.getLabel());
        kpiMix.setRealisticCount(modelIdMap.containsKey(ModelType.realistic.getValue()) ? Long.valueOf(modelIdMap.get(ModelType.realistic.getValue())) : 0L);
        kpiMix.setAnimeCount(modelIdMap.containsKey(ModelType.anime.getValue()) ? Long.valueOf(modelIdMap.get(ModelType.anime.getValue())) : 0L);
        kpiMix.setLineartCount(modelIdMap.containsKey(ModelType.lineart.getValue()) ? Long.valueOf(modelIdMap.get(ModelType.lineart.getValue())) : 0L);
        kpiMix.setCreateTime(LocalDateTime.now());
        kpiMix.setCreateBy("system");
        kpiMixMapper.insert(kpiMix);
    }

    public void sendMail(String mail, Date yesterdayDate, Map<String, Object> statisticsMap) throws IOException {
        String from = "<EMAIL>";
        String fromName = "<EMAIL>";
        String to = mail;
        String subject = "piclumen " + DateUtils.formatDateByPattern(yesterdayDate, DateUtils.YYYY_MM_DD) + " 数据统计";

        Resource resource = resourceLoader.getResource("classpath:templates/statisticsTemplate.html");
        InputStreamReader reader = new InputStreamReader(resource.getInputStream(), StandardCharsets.UTF_8);
        StringBuilder htmlBuilder = new StringBuilder();
        int c;
        while ((c = reader.read()) != -1) {
            htmlBuilder.append((char) c);
        }

        String taskAvgTime = statisticsMap.containsKey("taskAvgTime") ? String.valueOf((Double) statisticsMap.get("taskAvgTime")) : "数据获取失败";
        String createPictureNums = statisticsMap.containsKey("createPictureNums") ? String.valueOf((Long) statisticsMap.get("createPictureNums")) : "数据获取失败";
        String useOperateNumsByDay = statisticsMap.containsKey("useOperateNumsByDay") ? String.valueOf((Long) statisticsMap.get("useOperateNumsByDay")) : "数据获取失败";
        String useOperateNumsByWeek = statisticsMap.containsKey("useOperateNumsByWeek") ? String.valueOf((Long) statisticsMap.get("useOperateNumsByWeek")) : "数据获取失败";
        String useOperateNumsByMonth = statisticsMap.containsKey("useOperateNumsByMonth") ? String.valueOf((Long) statisticsMap.get("useOperateNumsByMonth")) : "数据获取失败";
        String userNums = statisticsMap.containsKey("userNums") ? String.valueOf((Long) statisticsMap.get("userNums")) : "数据获取失败";
        String allUserNums = statisticsMap.containsKey("allUserNums") ? String.valueOf((Long) statisticsMap.get("allUserNums")) : "数据获取失败";
        String avgCratePictureNums = statisticsMap.containsKey("avgCratePictureNums") ? String.valueOf((Long) statisticsMap.get("avgCratePictureNums")) : "数据获取失败";

        Double dayMaxTaskSizeValue = (Double) redisService.getDataFromZset("dayMaxTaskSize", DateFormatUtils.format(yesterdayDate, "yyyyMMdd"));

        String html = htmlBuilder.toString().replace("${taskAvgTime}", String.valueOf(taskAvgTime))
                .replace("${createPictureNums}", createPictureNums)
                .replace("${useOperateNumsByDay}", useOperateNumsByDay)
                .replace("${useOperateNumsByWeek}", useOperateNumsByWeek)
                .replace("${useOperateNumsByMonth}", useOperateNumsByMonth)
                .replace("${userNums}", userNums)
                .replace("${allUserNums}", allUserNums)
                .replace("${avgCratePictureNums}", avgCratePictureNums)
                .replace("${nowDate}", DateUtils.formatDateByPattern(yesterdayDate, DateUtils.YYYY_MM_DD))
                .replace("${dayMaxTaskSize}", Objects.isNull(dayMaxTaskSizeValue) ? "0" : String.valueOf(dayMaxTaskSizeValue.longValue()));

        log.info("发送邮件请求参数: mail :{}", mail);
        ResponseData response = sendCloudEmailClient.sendEmail(from, fromName, to, subject, html);
        log.info("调用发送邮件返回信息: {}", JsonUtils.writeToString(response));

        if (200 == response.getStatusCode() && Boolean.TRUE == response.getResult()) {
            log.info("邮件发送成功，接收邮箱为：{}", mail);
        } else {
            log.error("邮件发送失败，接收邮箱为：{}", mail);
        }
    }

    public Integer getGenInfoExport(User user) {
        try {
            LambdaQueryWrapper<ExportGenInfo> lqw = new LambdaQueryWrapper<>();
            lqw.eq(ExportGenInfo::getLoginName, user.getLoginName());
            return exportGenInfoMapper.selectCount(lqw).intValue();
        } catch (Exception e) {
            log.error(e.getMessage());
        }

        return Integer.valueOf(0);
    }


    public Boolean genInfoExport(String userEmail, User user) {
        try {
            //新增用户的导出记录
            ExportGenInfo exportGenInfo = new ExportGenInfo();
            exportGenInfo.setUserId(user.getId());
            exportGenInfo.setLoginName(user.getLoginName());
            exportGenInfo.setEmail(userEmail);
            exportGenInfo.setExportFlag(Boolean.FALSE);
            exportGenInfo.setCreateTime(LocalDateTime.now());
            exportGenInfoMapper.insert(exportGenInfo);
        } catch (Exception e) {
            log.error(e.getMessage());
            return Boolean.FALSE;
        }

        return Boolean.TRUE;
    }
}
