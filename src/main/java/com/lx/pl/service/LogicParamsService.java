package com.lx.pl.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lx.pl.db.mysql.gen.entity.LogicParams;
import com.lx.pl.db.mysql.gen.mapper.LogicParamsMapper;
import com.lx.pl.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class LogicParamsService {

    @Resource
    private LogicParamsMapper logicParamsMapper;

    private final String AD_CONFIG = "adconfig:%s";

    @Resource
    private RedisService<String> redisService;

    public String getAdConfig(String platform) {
        platform=platform.toLowerCase();
        String format = String.format(AD_CONFIG, platform);
        String key = redisService.get(format);
        if (StringUtils.isBlank(key)){
            LambdaQueryWrapper<LogicParams> lgw = new LambdaQueryWrapper<>();
            lgw.eq(LogicParams::getKeyName,format);
            LogicParams logicParams = logicParamsMapper.selectOne(lgw);
            if (logicParams!=null){
                redisService.set(format,logicParams.getValueData());
                key = logicParams.getValueData();
            }else {
                key = "";
            }
        }
        return key;
    }
}