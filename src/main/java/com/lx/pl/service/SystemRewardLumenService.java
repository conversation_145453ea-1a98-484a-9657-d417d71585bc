package com.lx.pl.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lx.pl.constant.LockPrefixConstant;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.db.mysql.gen.mapper.UserMapper;
import com.lx.pl.exception.ServerInternalException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/5/22
 * @description
 */
@Slf4j
@Service
public class SystemRewardLumenService {
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private UserMapper userMapper;

    /**
     * 用户领取系统奖励lumen
     */
    public void receiveSystemRewardLumen(String loginName, Integer rewardLumen) {
        if (StringUtils.isBlank(loginName) || rewardLumen == null || rewardLumen <= 0) {
            log.error("用户领取系统奖励lumen失败, 参数异常, loginName: {}, rewardLumen: {}", loginName, rewardLumen);
            throw new ServerInternalException("The lumen claim failed and the parameters were illegal.");
        }
        RLock lock = redissonClient.getLock(LockPrefixConstant.DEAL_USER_LUMEN_LOCK_PREFIX + loginName);
        try {
            lock.lock();
            User user = userMapper.selectOne(new LambdaQueryWrapper<>(User.class)
                    .eq(User::getLoginName, loginName));
            if (user == null) {
                throw new ServerInternalException("The lumen claim failed and, the user does not exist.");
            }
            Integer userLumen = user.getSystemRewardLumen();
            if (userLumen == null) {
                userLumen = 0;
            }
            user.setSystemRewardLumen(userLumen + rewardLumen);
            user.setUpdateTime(LocalDateTime.now());
            userMapper.updateById(user);
        } catch (Exception e) {
            log.error("用户领取系统奖励lumen异常, loginName: {}, rewardLumen: {}", loginName, rewardLumen, e);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }
}
