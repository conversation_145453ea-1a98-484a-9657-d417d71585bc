package com.lx.pl.service;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.lx.pl.db.mysql.community.entity.*;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.db.mysql.gen.repository.CommCommentRepository;
import com.lx.pl.db.mysql.gen.repository.CommFileRepository;
import com.lx.pl.dto.CommPageInfo;
import com.lx.pl.exception.ServerInternalException;
import com.mongodb.client.result.UpdateResult;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.lx.pl.service.CommMessageService.COMM_USER_NOT_READ_MESSAGE;
import static com.lx.pl.service.CommMessageService.NOT_READ_COMMENT_NUMS;

@Service
@Slf4j
public class CommCommentService {

    @Autowired
    private CommCommentRepository commCommentRepository;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private CommFileRepository commFileRepository;
    @Autowired
    private FileScoreService fileScoreService;
    @Lazy
    @Autowired
    private CommImgService commImgService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private RedisService redisService;

    @Autowired
    private CommMessageService commMessageService;


    /**
     * 用户对图片进行评论
     *
     * @param commFileId
     * @param content
     * @param user
     * @return
     */
    public Boolean addCommentCommFile(String commFileId, String content, User user) {
        RLock lock = redissonClient.getLock("file:comment:" + commFileId);

        try {
            lock.lock();
            CommFile commFile = commImgService.getCommFileById(commFileId);
            if (!Objects.isNull(commFile)) {
                CommComment commComment = new CommComment();
                commComment.setFileId(commFileId);
                commComment.setContent(content);
                commComment.setCommentLikeNums(0);
                commComment.setReplyNums(0);
                commComment.setDeleted(Boolean.FALSE);
                commComment.setCreateTime(LocalDateTime.now());
                //评论图片目标用户
                AccountInfo accountInfo = new AccountInfo();
                BeanUtils.copyProperties(commFile.getAccountInfo(), accountInfo);
                commComment.setTargetAcc(accountInfo);
                //评论图片发起者用户
                AccountInfo replyAccInfo = new AccountInfo();
                replyAccInfo.setUserId(user.getId());
                replyAccInfo.setUserName(user.getUserName());
                replyAccInfo.setUserLoginName(user.getLoginName());
                replyAccInfo.setUserAvatarUrl(user.getAvatarUrl());
                replyAccInfo.setWhetherPro(Boolean.FALSE);
                commComment.setOwnerAcc(replyAccInfo);
                commComment.setRead(Boolean.FALSE);
                commCommentRepository.insert(commComment);
                //
//                int interactionScore = fileScoreService.calculateInteractionScore(commFile.getFileLikeNums(), commFile.getRemixNums(), commFile.getFileCommentNums(), commFile.getShareNums());
//                commFile.setFileCommentNums(commFile.getFileCommentNums() + 1);
//                fileScoreService.updateSingleFileTrendingScore(commFile, interactionScore);
                fileScoreService.sendFlushMessage(commFileId, commFile.getTrendingScore());
                //图片评论数据+1
                mongoTemplate.updateFirst(
                        new Query(Criteria.where("id").is(commFileId)),
                        new Update().inc("fileCommentNums", 1).
                                inc("fileFirstCommentNums", 1),
                        CommFile.class
                );

                //用户的未读数增加1
                redisService.incrementFieldInHash(COMM_USER_NOT_READ_MESSAGE + accountInfo.getUserLoginName(), NOT_READ_COMMENT_NUMS, 1);

                //每日评论数增加1
                mongoTemplate.updateFirst(
                        new Query(Criteria.where("userId").is(user.getId())),
                        new Update().inc("dailyCommentNums", 1),
                        DailyTaskLumen.class
                );

                return Boolean.TRUE;
            }
        } catch (Exception e) {
            log.error("用户：{}，评论图片：{}，失败", user.getId(), commFileId, e);
            throw new ServerInternalException("Reply Failed");
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return Boolean.FALSE;
    }

    /**
     * 删除用户对图片的评论
     *
     * @param commFileId
     * @param commentId
     * @param user
     * @return
     */
    public Boolean deleteCommentCommFile(String commFileId, String commentId, User user) {
        RLock lock = redissonClient.getLock("file:comment:" + commFileId);
        try {
            lock.lock();
            CommFile commFile = commImgService.getCommFileById(commFileId);
            AccountInfo accountInfo = commFile.getAccountInfo();

            Query query = new Query();
            query.addCriteria(Criteria.where("ownerAcc.userId").is(user.getId())
                    .and("id").is(commentId));

            //对图片评论数 - 1
//            mongoTemplate.updateFirst(
//                    new Query(Criteria.where("fileId").is(commFileId)),
//                    new Update().inc("fileCommentNums", -1),
//                    CommFile.class
//            );

            // 执行逻辑删除
            mongoTemplate.updateFirst(query, new Update().set("deleted", Boolean.TRUE), CommComment.class);

            //用户的未读数增减1
            commMessageService.decrementFieldSafely(COMM_USER_NOT_READ_MESSAGE + accountInfo.getUserLoginName(), NOT_READ_COMMENT_NUMS);

            return Boolean.TRUE;
        } catch (Exception e) {
            log.error("用户：{} 对图片：{} 删除评论报错", user.getId(), commFileId);
            throw new ServerInternalException("Reduce Comment Failed");
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 对图片的评论进行评论
     *
     * @param commFileId
     * @param commentId
     * @param content
     * @param user
     * @return
     */
    public Boolean addCommentCommComment(String commFileId, String firstCommentId, String commentId, String content, User user) {
        RLock lock = redissonClient.getLock("comment:reply:" + commentId);
        try {
            lock.lock();
            CommComment targetCommComment = getCommCommentById(commentId);
            if (!Objects.isNull(commentId)) {
                CommComment commComment = new CommComment();
                commComment.setFileId(commFileId);
                commComment.setFirstCommentId(firstCommentId);
                commComment.setCommentId(commentId);
                commComment.setContent(content);
                commComment.setCommentLikeNums(0);
                commComment.setReplyNums(0);
                commComment.setCreateTime(LocalDateTime.now());
                commComment.setDeleted(Boolean.FALSE);
                // 点赞目标用户
                AccountInfo accountInfo = new AccountInfo();
                BeanUtils.copyProperties(targetCommComment.getOwnerAcc(), accountInfo);
                commComment.setTargetAcc(accountInfo);
                // 点赞发起者用户
                AccountInfo replyAccInfo = new AccountInfo();
                replyAccInfo.setUserId(user.getId());
                replyAccInfo.setUserName(user.getUserName());
                replyAccInfo.setUserLoginName(user.getLoginName());
                replyAccInfo.setUserAvatarUrl(user.getAvatarUrl());
                replyAccInfo.setWhetherPro(Boolean.FALSE);
                commComment.setOwnerAcc(replyAccInfo);
                commComment.setRead(Boolean.FALSE);
                commCommentRepository.insert(commComment);

                //用户的未读数增加1
                redisService.incrementFieldInHash(COMM_USER_NOT_READ_MESSAGE + accountInfo.getUserLoginName(), NOT_READ_COMMENT_NUMS, 1);
            }
            CommFile commFile = commImgService.getCommFileById(commFileId);
//            int interactionScore = fileScoreService.calculateInteractionScore(commFile.getFileLikeNums(), commFile.getRemixNums(), commFile.getFileCommentNums(), commFile.getShareNums());
//            commFile.setFileCommentNums(commFile.getFileCommentNums() + 1);
//            fileScoreService.updateSingleFileTrendingScore(commFile, interactionScore);
            fileScoreService.sendFlushMessage(commFileId, commFile.getTrendingScore());
            // 图片的评论数据+1
            mongoTemplate.updateFirst(
                    new Query(Criteria.where("id").is(commFileId)),
                    new Update().inc("fileCommentNums", 1),
                    CommFile.class
            );

            //一级评论回复数据+1
            mongoTemplate.updateFirst(
                    new Query(Criteria.where("id").is(firstCommentId)),
                    new Update().inc("replyNums", 1),
                    CommComment.class
            );
            //每日评论数增加1
            mongoTemplate.updateFirst(
                    new Query(Criteria.where("userId").is(user.getId())),
                    new Update().inc("dailyCommentNums", 1),
                    DailyTaskLumen.class);

        } catch (Exception e) {
            log.error("用户：{}，评论社区评论：{}，失败", user.getId(), commentId, e);
            throw new ServerInternalException("Reply Failed");
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return Boolean.TRUE;
    }


    /**
     * 删除评论的评论
     *
     * @param commentId
     * @param user
     * @return
     */
    public Boolean deleteCommentCommComment(String commentId, User user) {
        RLock lock = redissonClient.getLock("comment:reply:" + commentId);
        try {
            lock.lock();
            Query query = new Query();
            query.addCriteria(Criteria.where("ownerAcc.userId").is(user.getId())
                    .and("id").is(commentId));

            // 执行逻辑删除
            mongoTemplate.updateFirst(query, new Update().set("deleted", Boolean.TRUE), CommComment.class);

            CommComment commComment = getCommCommentById(commentId);
            AccountInfo accountInfo = commComment.getTargetAcc();

            //用户的未读数增减1
            commMessageService.decrementFieldSafely(COMM_USER_NOT_READ_MESSAGE + accountInfo.getUserLoginName(), NOT_READ_COMMENT_NUMS);

            return Boolean.TRUE;
        } catch (Exception e) {
            log.error("用户：{} 对评论：{} 删除报错", user.getId(), commentId, e);
            throw new ServerInternalException("Reduce Comment Failed");
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 查询评论列表
     *
     * @param commFileId
     * @param firstCommentId 查询二级评论的时候需要传入
     * @param user
     * @return
     */
    public CommPageInfo<CommComment> getCommCommentList(String lastCommentId, Integer pageSize, String commFileId, String firstCommentId, User user) {
        List<CommComment> commCommentList = new ArrayList<>();
        try {
            Query query = new Query();
            query.addCriteria(Criteria.where("fileId").is(commFileId));

            if (StringUtil.isNotBlank(firstCommentId)) {
                query.addCriteria(Criteria.where("firstCommentId").in(firstCommentId));
            } else {
                query.addCriteria(Criteria.where("firstCommentId").exists(false));
            }

            // 游标分页：基于上一页最后一条记录的 ID
            if (StringUtil.isNotBlank(lastCommentId)) {
                query.addCriteria(Criteria.where("id").lt(new ObjectId(lastCommentId))); // 仅获取 ID 小于游标的记录
            }

            query.with(Sort.by(Sort.Direction.DESC, "id"));

            // 设置分页
            query.limit(pageSize);
            commCommentList = mongoTemplate.find(query, CommComment.class);

            //处理当前用户是否举报了评论
            if (!CollectionUtils.isEmpty(commCommentList)) {
                markReportedComments(commCommentList, user);
                //todo 是否评论点赞
            }
        } catch (Exception e) {
            log.error("获取社区评论赞列表失败", e);
            throw new ServerInternalException("Failed to retrieve comments");
        }
        return buildPromptPageInfo(pageSize, commCommentList);
    }

    private void markReportedComments(List<CommComment> commCommentList, User user) {
        List<String> commentIdList = commCommentList.stream()
                .map(CommComment::getId)
                .collect(Collectors.toList());

        Query reportQuery = new Query();
        reportQuery.addCriteria(Criteria.where("ownerAcc.userId").is(user.getId()));
        reportQuery.addCriteria(Criteria.where("commentId").in(commentIdList));

        List<CommCommentReport> commCommentReportList = mongoTemplate.find(reportQuery, CommCommentReport.class);

        if (!CollectionUtils.isEmpty(commCommentReportList)) {
            Set<String> reportedCommentIds = commCommentReportList.stream()
                    .map(CommCommentReport::getCommentId)
                    .collect(Collectors.toSet());

            for (CommComment commComment : commCommentList) {
                if (reportedCommentIds.contains(commComment.getId())) {
                    commComment.setReported(Boolean.TRUE);
                }
            }
        }
    }

    // 构建分页结果对象
    private CommPageInfo<CommComment> buildPromptPageInfo(Integer pageSize, List<CommComment> commCommentList) {
        CommPageInfo<CommComment> commPageInfo = new CommPageInfo<>();
        commPageInfo.setResultList(CollectionUtils.isEmpty(commCommentList) ? Collections.emptyList() : commCommentList);
        commPageInfo.setPageSize(pageSize);
        commPageInfo.setLastId(CollectionUtils.isEmpty(commCommentList) ? "" : commCommentList.get(commCommentList.size() - 1).getId());
        return commPageInfo;
    }


    /**
     * 查询社区评论
     *
     * @param commentId
     * @return
     */
    public CommComment getCommCommentById(String commentId) {
        try {
            Query query = new Query();
            query.addCriteria(Criteria.where("id").is(commentId));
            CommComment commComment = mongoTemplate.findOne(query, CommComment.class);
            return commComment;
        } catch (Exception e) {
            log.error("查询社区评论报错，图片id为：{}", commentId);
            return null;
        }
    }

    /**
     * 更新评论的用户名称
     *
     * @param userId
     * @param newUsername
     */
    public void updateUsernameByUserId(Long userId, String newUsername) {
        try {
            // 更新 targetAcc.userName
            Query targetAccQuery = new Query();
            targetAccQuery.addCriteria(Criteria.where("targetAcc.userId").is(userId));
            Update targetAccUpdate = new Update();
            targetAccUpdate.set("targetAcc.userName", newUsername);
            UpdateResult targetResult = mongoTemplate.updateMulti(targetAccQuery, targetAccUpdate, CommComment.class);

            log.info("评论更新 targetAcc userName记录数：{}", targetResult.getModifiedCount());

            // 更新 ownerAcc.userName
            Query ownerAccQuery = new Query();
            ownerAccQuery.addCriteria(Criteria.where("ownerAcc.userId").is(userId));
            Update ownerAccUpdate = new Update();
            ownerAccUpdate.set("ownerAcc.userName", newUsername);
            UpdateResult ownerResult = mongoTemplate.updateMulti(ownerAccQuery, ownerAccUpdate, CommComment.class);

            log.info("评论更新 ownerAcc userName 记录数：{}", ownerResult.getModifiedCount());
        } catch (Exception e) {
            log.error("更新用户：{} 的用户名称：{}报错", userId, newUsername, e);
            throw new ServerInternalException("Failed to update username");
        }
    }

    /**
     * 更新评论的用户头像
     *
     * @param userId
     * @param newUserAvatarUrl
     */
    public void updateUserAvatarUrlByUserId(Long userId, String newUserAvatarUrl) {
        try {
            // 更新 targetAcc.userName
            Query targetAccQuery = new Query();
            targetAccQuery.addCriteria(Criteria.where("targetAcc.userId").is(userId));
            Update targetAccUpdate = new Update();
            targetAccUpdate.set("targetAcc.userAvatarUrl", newUserAvatarUrl);
            UpdateResult targetResult = mongoTemplate.updateMulti(targetAccQuery, targetAccUpdate, CommComment.class);

            log.info("更新 targetAcc userAvatarUr 记录数：{}", targetResult.getModifiedCount());

            // 更新 ownerAcc.userName
            Query ownerAccQuery = new Query();
            ownerAccQuery.addCriteria(Criteria.where("ownerAcc.userId").is(userId));
            Update ownerAccUpdate = new Update();
            ownerAccUpdate.set("ownerAcc.userAvatarUrl", newUserAvatarUrl);
            UpdateResult ownerResult = mongoTemplate.updateMulti(ownerAccQuery, ownerAccUpdate, CommComment.class);

            log.info("更新 ownerAcc userAvatarUr 记录数：{}", ownerResult.getModifiedCount());
        } catch (Exception e) {
            log.error("更新用户：{} 的用户头像：{}报错", userId, newUserAvatarUrl, e);
            throw new ServerInternalException("Error updating user avatar for comments");
        }
    }


    public Boolean readCommComment(String commCommentId, User user) {
        RLock lock = redissonClient.getLock("read:comment:" + user.getId());
        try {
            //修改所有满足条件的
            if (StringUtil.isBlank(commCommentId)) {
                mongoTemplate.updateMulti(
                        new Query(Criteria.where("targetAcc.userId").is(user.getId())
                                .and("read").is(false)),  // read 为 false
                        new Update().set("read", true),
                        CommComment.class
                );

            } else {
                //只修改满足条件的一条
                mongoTemplate.updateFirst(
                        new Query(Criteria.where("targetAcc.userId").is(user.getId())
                                .and("id").is(commCommentId)),
                        new Update().set("read", true),
                        CommComment.class
                );
            }

            return Boolean.TRUE;
        } catch (Exception e) {
            log.error("用户：{} 对评论：{} 已读报错", user.getId(), commCommentId, e);
            throw new ServerInternalException("Read Comment Failed");
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }


    public CommPageInfo<CommComment> getCommentMessage(String lastCommentId, Integer pageSize, User user) {
        List<CommComment> commCommentList = new ArrayList<>();
        try {
            Query query = new Query();
            query.addCriteria(Criteria.where("targetAcc.userId").is(user.getId())
                    .and("deleted").is(Boolean.FALSE));

            if (StringUtil.isNotBlank(lastCommentId)) {
                query.addCriteria(Criteria.where("id").lt(new ObjectId(lastCommentId)));
            }

            query.with(Sort.by(Sort.Direction.DESC, "id"));

            // 设置分页
            query.limit(pageSize);
            commCommentList = mongoTemplate.find(query, CommComment.class);

            //组装图片的地址
            if (!CollectionUtils.isEmpty(commCommentList)) {
                List<String> fileIds = commCommentList.stream().map(CommComment::getFileId).collect(Collectors.toList());
                Map<String, String> fileUrlMap = commImgService.getCommFileUrlListByCommFileIds(fileIds);

                for (CommComment commComment : commCommentList) {
                    commComment.setMiniThumbnailUrl(fileUrlMap.containsKey(commComment.getFileId()) ? fileUrlMap.get(commComment.getFileId()) : "");
                }
            }
        } catch (Exception e) {
            log.error("获取社区评论列表报错", e);
            throw new ServerInternalException("Error retrieving the community image likes list");
        }
        return buildPromptPageInfo(pageSize, commCommentList);
    }
}
