package com.lx.pl.service.tcloud.impl;

import com.lx.pl.config.CosConfig;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.dto.tcloud.AuthParam;
import com.lx.pl.dto.tcloud.AuthVo;
import com.lx.pl.exception.ServerInternalException;
import com.lx.pl.service.CosService;
import com.lx.pl.service.tcloud.ITencentCloudService;
import com.qcloud.cos.model.GeneratePresignedUrlRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.net.URL;

import static com.lx.pl.service.CosService.buildFileNameKey;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class TencentCloudServiceImpl implements ITencentCloudService {

    @Autowired
    private CosService cosService;
    @Resource
    public CosConfig cosConfig;

    @Override
    public AuthVo getUploadAuth(AuthParam param, User user) {
        try {
            if (user == null || user.getId() == null) {
                return new AuthVo(false);
            }
            AuthVo authVo = new AuthVo();
            String fileName = buildFileNameKey(String.valueOf(user.getId()), param.getFileExt(), param.getType());
            authVo.setFileName(fileName);
            if (fileName.startsWith("/")) {
                authVo.setFileName(fileName.substring(1));
            }
            authVo.setResponse(cosService.getTmpCredential(authVo.getFileName()));
            // authVo.setThumbnailName(thumbnailName);
            // authVo.setHighThumbnailName(highThumbnailName);
            setBucketAndRegion(authVo);
            authVo.setSuccess(true);
            return authVo;
        } catch (Exception e) {
            log.error("UploadAuth error", e);
            throw new ServerInternalException("auth error, tyr later!");
        }
    }

    @Override
    public String getPreAuthUrl(AuthParam param, User user) {
        if (user == null || user.getId() == null) {
            return null;
        }
        String fileName = buildFileNameKey(String.valueOf(user.getId()), param.getFileExt(), param.getType());
        URL url = cosService.getBaseCosClient().generatePresignedUrl(cosService.buildGeneratePresignedUrlRequest(fileName));
        return url.toString();
    }

    private void setBucketAndRegion(AuthVo authVo) {
        String prefix = cosConfig.getCosAccelerateDomain();
        authVo.setFullPath(prefix + "/" + authVo.getFileName());
        authVo.setBucket(cosConfig.bucket);
        authVo.setRegion(cosConfig.region);

    }
}
