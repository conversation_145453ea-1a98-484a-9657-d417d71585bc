package com.lx.pl.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lx.pl.db.mysql.gen.entity.GptContacts;

import java.io.IOException;
import java.util.List;

/**
 * 联系我们Service接口
 *
 * <AUTHOR>
 * @date 2024-07-10
 */
public interface IGptContactsService extends IService<GptContacts> {
    /**
     * 查询联系我们
     *
     * @param id 联系我们主键
     * @return 联系我们
     */
    public GptContacts selectGptContactsById(Long id);

    /**
     * 查询联系我们列表
     *
     * @param gptContacts 联系我们
     * @return 联系我们集合
     */
    public List<GptContacts> selectGptContactsList(GptContacts gptContacts);

    /**
     * 新增联系我们
     *
     * @param gptContacts 联系我们
     * @return 结果
     */
    public int insertGptContacts(GptContacts gptContacts);

    /**
     * 修改联系我们
     *
     * @param gptContacts 联系我们
     * @return 结果
     */
    public int updateGptContacts(GptContacts gptContacts);

    /**
     * 批量删除联系我们
     *
     * @param ids 需要删除的联系我们主键集合
     * @return 结果
     */
    public int deleteGptContactsByIds(Long[] ids);

    /**
     * 删除联系我们信息
     *
     * @param id 联系我们主键
     * @return 结果
     */
    public int deleteGptContactsById(Long id);


    /**
     * 业务新增联系我们
     *
     * @param gptContacts 联系我们
     * @return 结果
     */
    public int createGptContacts(GptContacts gptContacts, String platform) throws IOException;
}
