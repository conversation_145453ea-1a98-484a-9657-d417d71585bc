package com.lx.pl.service;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.lx.pl.db.mysql.gen.entity.PromptFile;
import com.lx.pl.db.mysql.gen.entity.PromptRecord;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.db.mysql.gen.mapper.PromptFileMapper;
import com.lx.pl.db.mysql.gen.mapper.PromptRecordMapper;
import com.lx.pl.db.mysql.gen.mapper.UserMapper;
import com.lx.pl.dto.fluxkontext.FluxKontextResponse;
import com.lx.pl.enums.FluxKontextTaskStatus;
import com.lx.pl.service.redis.RedisService;
import com.lx.pl.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.net.URL;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Flux Kontext回调处理服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class FluxKontextCallbackService {

    private static final String FLUX_TASK_PREFIX = "flux_kontext_task:";
    private static final String FLUX_USER_TASK_PREFIX = "flux_kontext_user_tasks:";

    @Autowired
    private PromptRecordMapper promptRecordMapper;

    @Autowired
    private PromptFileMapper promptFileMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private RedisService redisService;

    @Autowired
    private VipService vipService;

    @Autowired
    private ImgUploadCommonService imgUploadCommonService;

    /**
     * 处理任务结果
     */
    public void handleTaskResult(FluxKontextResponse.TaskResultResponse taskResult, String loginName) {
        String requestId = taskResult.getId();
        FluxKontextTaskStatus status = FluxKontextTaskStatus.fromStatus(taskResult.getStatus());

        log.info("Processing Flux Kontext task result for requestId: {}, status: {}", requestId, status.getStatus());

        // 根据状态处理
        switch (status) {
            case READY:
                handleSuccessResult(taskResult, loginName);
                break;
            case ERROR:
                handleFailedResult(taskResult, loginName);
                break;
            default:
                log.warn("Unexpected task status: {}", status.getStatus());
        }
    }

    /**
     * 处理成功结果
     */
    private void handleSuccessResult(FluxKontextResponse.TaskResultResponse taskResult, String loginName) {
        String requestId = taskResult.getId();
        FluxKontextResponse.ResultData resultData = taskResult.getResult();

        if (resultData == null || StringUtil.isBlank(resultData.getSample())) {
            log.warn("Result data is null or empty for requestId: {}", requestId);
            handleFailedResult(taskResult, loginName);
            return;
        }

        try {
            // 更新任务记录
            updatePromptRecord(requestId, loginName, resultData);

            // 下载并保存图像
            String imageUrl = resultData.getSample();
            String savedImageUrl = downloadAndSaveImage(imageUrl, requestId, loginName);

            if (StringUtil.isNotBlank(savedImageUrl)) {
                // 保存图像文件信息
                saveImageFile(requestId, loginName, savedImageUrl, resultData);

                // 更新用户统计
                updateUserStats(loginName, 1);

                // 更新VIP统计
                updateVipStats(requestId, loginName, 1);
            }

            // 清理Redis任务信息
            cleanupTaskFromRedis(requestId, loginName);

            log.info("Successfully processed success result for requestId: {}", requestId);

        } catch (Exception e) {
            log.error("Error processing success result for requestId: " + requestId, e);
            // 如果处理失败，标记为失败状态
            handleFailedResult(taskResult, loginName);
        }
    }

    /**
     * 处理失败结果
     */
    private void handleFailedResult(FluxKontextResponse.TaskResultResponse taskResult, String loginName) {
        String requestId = taskResult.getId();
        String errorMessage = taskResult.getError();

        try {
            // 更新任务状态为失败
            LambdaUpdateWrapper<PromptRecord> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(PromptRecord::getPromptId, requestId)
                    .eq(PromptRecord::getLoginName, loginName)
                    .set(PromptRecord::getDel, true)
                    .set(PromptRecord::getFailureMessage, errorMessage)
                    .set(PromptRecord::getGenEndTime, LocalDateTime.now())
                    .set(PromptRecord::getUpdateTime, LocalDateTime.now());

            promptRecordMapper.update(null, updateWrapper);

            // 清理Redis任务信息
            cleanupTaskFromRedis(requestId, loginName);

            log.info("Processed failed result for requestId: {}, error: {}", requestId, errorMessage);

        } catch (Exception e) {
            log.error("Error processing failed result for requestId: " + requestId, e);
        }
    }

    /**
     * 更新任务记录
     */
    private void updatePromptRecord(String requestId, String loginName, FluxKontextResponse.ResultData resultData) {
        LambdaUpdateWrapper<PromptRecord> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(PromptRecord::getPromptId, requestId)
                .eq(PromptRecord::getLoginName, loginName)
                .set(PromptRecord::getGenEndTime, LocalDateTime.now())
                .set(PromptRecord::getUpdateTime, LocalDateTime.now());

        // 如果有种子值，更新到数据库
        if (resultData.getSeed() != null) {
            updateWrapper.set(PromptRecord::getPromptParams,
                JsonUtils.writeToJsonNode("{\"seed\":" + resultData.getSeed() + "}"));
        }

        promptRecordMapper.update(null, updateWrapper);
    }

    /**
     * 下载并保存图像
     */
    private String downloadAndSaveImage(String imageUrl, String requestId, String loginName) {
        try {
            log.info("Downloading image from URL: {}", imageUrl);

            // 生成文件名
            String fileName = generateFileName(requestId);

            // 下载图像
            URL url = new URL(imageUrl);
            try (InputStream inputStream = url.openStream()) {
                // 使用现有的图像上传服务保存图像
                String savedUrl = imgUploadCommonService.uploadImageFromStream(
                    inputStream, fileName, loginName);

                log.info("Successfully saved image for requestId: {}, savedUrl: {}", requestId, savedUrl);
                return savedUrl;
            }

        } catch (Exception e) {
            log.error("Failed to download and save image for requestId: " + requestId, e);
            return null;
        }
    }

    /**
     * 生成文件名
     */
    private String generateFileName(String requestId) {
        return "flux_kontext_" + requestId + "_" + UUID.randomUUID().toString().substring(0, 8) + ".jpg";
    }

    /**
     * 保存图像文件信息
     */
    private void saveImageFile(String requestId, String loginName, String imageUrl,
                              FluxKontextResponse.ResultData resultData) {
        try {
            PromptFile promptFile = new PromptFile();
            promptFile.setLoginName(loginName);
            promptFile.setPromptId(requestId);
            promptFile.setFileUrl(imageUrl);
            promptFile.setThumbnailUrl(imageUrl); // Flux Kontext返回的是处理好的图片

            // 设置图像尺寸
            if (resultData.getWidth() != null && resultData.getHeight() != null) {
                promptFile.setWidth(resultData.getWidth());
                promptFile.setHeight(resultData.getHeight());
            } else {
                // 默认尺寸
                promptFile.setWidth(1024);
                promptFile.setHeight(1024);
            }

            promptFile.setCreateTime(LocalDateTime.now());
            promptFile.setCreateBy(loginName);
            promptFile.setOriginCreate("flux_kontext");

            promptFileMapper.insert(promptFile);
            log.info("Saved image file for requestId: {}, imageUrl: {}", requestId, imageUrl);

        } catch (Exception e) {
            log.error("Failed to save image file for requestId: " + requestId, e);
        }
    }

    /**
     * 更新用户统计
     */
    private void updateUserStats(String loginName, int imageCount) {
        try {
            LambdaUpdateWrapper<User> userUpdateWrapper = new LambdaUpdateWrapper<>();
            userUpdateWrapper.eq(User::getLoginName, loginName)
                    .setSql("total_img_num = total_img_num + " + imageCount);

            userMapper.update(null, userUpdateWrapper);

        } catch (Exception e) {
            log.error("Failed to update user stats for: " + loginName, e);
        }
    }

    /**
     * 更新VIP统计
     */
    private void updateVipStats(String requestId, String loginName, int imageCount) {
        try {
            vipService.updateMessageByMarkId(requestId, requestId, loginName,
                                           imageCount, imageCount, imageCount);
        } catch (Exception e) {
            log.error("Failed to update VIP stats for requestId: {}, user: {}", requestId, loginName, e);
        }
    }

    /**
     * 清理Redis任务信息
     */
    private void cleanupTaskFromRedis(String requestId, String loginName) {
        try {
            // 删除任务信息
            String taskKey = FLUX_TASK_PREFIX + requestId;
            redisService.delete(taskKey);

            // 从用户任务列表中删除
            String userTaskKey = FLUX_USER_TASK_PREFIX + loginName;
            redisService.deleteFieldFromHash(userTaskKey, requestId);

        } catch (Exception e) {
            log.error("Error cleaning up task from redis for requestId: " + requestId, e);
        }
    }
}
