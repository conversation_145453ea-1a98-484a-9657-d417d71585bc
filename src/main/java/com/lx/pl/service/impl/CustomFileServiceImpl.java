package com.lx.pl.service.impl;

import cn.hutool.core.util.URLUtil;
import com.alibaba.csp.sentinel.util.StringUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.lx.pl.config.CosConfig;
import com.lx.pl.db.mysql.gen.entity.CustomFile;
import com.lx.pl.db.mysql.gen.entity.PromptFile;
import com.lx.pl.db.mysql.gen.entity.PromptRecord;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.db.mysql.gen.mapper.CustomFileMapper;
import com.lx.pl.db.mysql.gen.mapper.PromptFileMapper;
import com.lx.pl.db.mysql.gen.mapper.PromptRecordMapper;
import com.lx.pl.dto.CustomFileParam;
import com.lx.pl.dto.GenGenericPara;
import com.lx.pl.dto.ModelInformation;
import com.lx.pl.dto.Resolution;
import com.lx.pl.enums.OriginCreate;
import com.lx.pl.service.ICustomFileService;
import com.lx.pl.service.ImgUploadCommonService;
import com.lx.pl.service.RedisService;
import com.lx.pl.util.IdUtils;
import com.lx.pl.util.JsonUtils;
import com.lx.pl.util.*;
import com.qcloud.cos.model.ObjectMetadata;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.temporal.ChronoUnit;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static com.lx.pl.constant.LogicConstants.TMP_TOKEN_COUNT;
import static com.lx.pl.service.CosService.buildFileNameKey;
import static com.lx.pl.service.ImgUploadCommonService.getFilePath;
import static com.lx.pl.service.ImgUploadCommonService.isOldBucket;
import static com.lx.pl.service.LoadBalanceService.REALISTIC_MODEL_KEY;

/**
 * 用户上传
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class CustomFileServiceImpl implements ICustomFileService {

    @Autowired
    PromptRecordMapper promptRecordMapper;
    @Autowired
    private CosConfig cosConfig;
    @Autowired
    private CustomFileMapper customFileMapper;
    @Autowired
    private PromptFileMapper promptFileMapper;
    @Autowired
    private ImgUploadCommonService imgUploadCommonService;
    @Autowired
    private RedisService<Object> redisService;
    @Value("${custom.day.size:100}")
    private long customDaySize;

    @Value("${realistic.ModelId}")
    String realisticModelId;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CustomFile saveCustomFile(CustomFileParam param, User user,String platform) {
        if (Objects.isNull(param)) {
            return null;
        }
        // 校验用户是否可以上传
        if (!checkUserCanUploadAndIncrement(user)) {
            return null;
        }
        CustomFile customFile = buildCustomFile(param, user);
        PromptRecord promptRecord = buildPromptRecord(customFile, param,platform);
        PromptFile promptFile = buildPromptFile(customFile,param);
        customFileMapper.insert(customFile);
        promptRecordMapper.insert(promptRecord);
        promptFileMapper.insert(promptFile);
        return customFile;
    }

    private PromptFile buildPromptFile(CustomFile customFile,CustomFileParam param) {
        // 图片信息
        PromptFile promptFile = new PromptFile();
        promptFile.setLoginName(customFile.getLoginName());
        promptFile.setPromptId(customFile.getPromptId());
        promptFile.setFileUrl(customFile.getImgUrl());
        promptFile.setThumbnailUrl(customFile.getThumbnailUrl());
        promptFile.setHighThumbnailUrl(customFile.getHighThumbnailUrl());
        promptFile.setWidth(customFile.getWidth());
        promptFile.setHeight(customFile.getHeight());
        promptFile.setFileName(getFilePath(customFile.getImgUrl()));
        promptFile.setThumbnailName(getFilePath(customFile.getThumbnailUrl()));
        promptFile.setHighThumbnailName(getFilePath(customFile.getHighThumbnailUrl()));
        promptFile.setCreateTime(customFile.getCreateTime());
        promptFile.setCreateBy(customFile.getCreateBy());
        //默认为用户上传
        promptFile.setOriginCreate(StringUtil.isNotBlank(param.getOriginCreate()) ? param.getOriginCreate() : OriginCreate.customUpload.getValue());
        ObjectMetadata metadata = imgUploadCommonService.getMetadata(getFilePath(promptFile.getFileUrl()), isOldBucket(promptFile.getFileUrl()));
        if (metadata != null) {
            promptFile.setSize(metadata.getContentLength());
        }
        return promptFile;
    }

    private PromptRecord buildPromptRecord(CustomFile customFile, CustomFileParam param,String platform) {
        // 数据入库
        PromptRecord promptRecord = new PromptRecord();
        promptRecord.setLoginName(customFile.getLoginName());
        promptRecord.setPromptId(customFile.getPromptId());
        promptRecord.setBatchSize(1);
        promptRecord.setModelId(realisticModelId);
        promptRecord.setCreateBy(customFile.getLoginName());
        promptRecord.setCreateTime(customFile.getCreateTime());
        promptRecord.setAspectRatio(customFile.getWidth() + " * " + customFile.getHeight());
        // 如果图生图有对象，不为空，且图片有值，则生图对象为图生图
        promptRecord.setOriginCreate(StringUtil.isNotBlank(param.getOriginCreate()) ? param.getOriginCreate() : OriginCreate.customUpload.getValue());

        promptRecord.setMarkId(IdUtils.randomUUID());
        promptRecord.setDel(false);
        promptRecord.setPlatform(platform);
        GenGenericPara genGenericPara = new GenGenericPara();
        try {
            //判断上传的图片是否由piclumen生成
            if (param.isBePiclumen()) {
                if (!Objects.isNull(param.getGenGenericPara())) {
                    BeanUtils.copyProperties(param.getGenGenericPara(), genGenericPara);
                }
            } else {
                //不是由piclumen生成的，自定义上传的，使用通用模型的默认值
                String realisticModelDefaultConfig = (String) redisService.get(REALISTIC_MODEL_KEY);
                if (StringUtils.isNotBlank(realisticModelDefaultConfig)) {
                    ModelInformation.DefaultConfig defaultConfig = JsonUtils.fromString(realisticModelDefaultConfig, ModelInformation.DefaultConfig.class);
                    genGenericPara.setSeed(defaultConfig.getSeed());
                    genGenericPara.setSteps(defaultConfig.getSteps());
                    genGenericPara.setCfg(defaultConfig.getCfg());
                    genGenericPara.setDenoise(defaultConfig.getDenoise());
                    genGenericPara.setModel_id(realisticModelId);
                }
            }
            Resolution resolution = new Resolution();
            resolution.setHeight(customFile.getHeight());
            resolution.setWidth(customFile.getWidth());
            resolution.setBatch_size(1);
            genGenericPara.setResolution(resolution);
            promptRecord.setGenInfo(JsonUtils.writeToJsonNode(genGenericPara));
        } catch (JsonProcessingException e) {
            log.error("save upload error", e);
        }
        return promptRecord;
    }

    private CustomFile buildCustomFile(CustomFileParam param, User user) {
        CustomFile customFile = new CustomFile();
        customFile.setUserId(user.getId());
        customFile.setLoginName(user.getLoginName());

        customFile.setImgUrl(param.getFileUrl());
        String thumbnailName = buildFileNameKey(String.valueOf(user.getId()), "webp");
        String highThumbnailName = buildFileNameKey(String.valueOf(user.getId()), "webp");
        imgUploadCommonService.imageProcessToThumbnail(URLUtil.getPath(param.getFileUrl()), thumbnailName, highThumbnailName);
        customFile.setThumbnailUrl(cosConfig.getCosDomain() + thumbnailName);
        customFile.setHighThumbnailUrl(cosConfig.getCosDomain() + highThumbnailName);

        customFile.setHeight(param.getHeight());
        customFile.setWidth(param.getWidth());

        customFile.setCreateTime(LocalDateTime.now());
        customFile.setCreateBy(user.getLoginName());
        customFile.setPromptId(IdUtils.randomUUID());
        return customFile;

    }

    private boolean checkUserCanUploadAndIncrement(User user) {
        String userId = String.valueOf(user.getId());
        boolean hasKey = redisService.hasKey(TMP_TOKEN_COUNT);
        if (hasKey) {
            // 判断计数
            Object dataFromHash = redisService.getDataFromHash(TMP_TOKEN_COUNT, userId);
            if (dataFromHash != null && Long.parseLong(String.valueOf(dataFromHash)) >= customDaySize) {
                return false;
            } else {
                redisService.incrementCountByHashKey(TMP_TOKEN_COUNT, userId, 1);
            }
        } else {
            // 初始化hash 计算过期时间
            synchronized (TMP_TOKEN_COUNT) {
                if (redisService.hasKey(TMP_TOKEN_COUNT)) {
                    // 判断计数
                    Object dataFromHash = redisService.getDataFromHash(TMP_TOKEN_COUNT, userId);
                    if (dataFromHash != null && Long.parseLong(String.valueOf(dataFromHash)) >= customDaySize) {
                        return false;
                    } else {
                        redisService.incrementCountByHashKey(TMP_TOKEN_COUNT, userId, 1);
                    }
                } else {
                    // 获取当前时间
                    LocalDateTime now = LocalDateTime.now(ZoneOffset.ofHours(8));
                    // 计算东八区当天 8 点的时间
                    LocalDateTime todayAt8AM = now.toLocalDate().atTime(8, 0);
                    // 如果当前时间已经超过当天 8 点，则计算明天 8 点的时间
                    if (now.isAfter(todayAt8AM)) {
                        todayAt8AM = todayAt8AM.plusDays(1);
                    }
                    // 计算当前时间到当天/明天 8 点的秒数
                    long until = now.until(todayAt8AM, ChronoUnit.SECONDS);
                    redisService.putDataToHash(TMP_TOKEN_COUNT, userId, 1, until, TimeUnit.SECONDS);
                }
            }
        }
        return true;
    }

    @Override
    public boolean queryCanUpload(User user) {
        Object dataFromHash = redisService.getDataFromHash(TMP_TOKEN_COUNT, String.valueOf(user.getId()));
        return dataFromHash == null || Long.parseLong(String.valueOf(dataFromHash)) < customDaySize;
    }
}
