package com.lx.pl.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lx.pl.db.mysql.gen.entity.StripeUserCustomer;
import com.lx.pl.db.mysql.gen.mapper.StripeCustomerMapper;
import com.lx.pl.service.StripeCustomerService;
import org.springframework.stereotype.Service;

@Service
public class StripeCustomerServiceImpl extends ServiceImpl<StripeCustomerMapper, StripeUserCustomer> implements StripeCustomerService {

    @Override
    public StripeUserCustomer getStripeCustomerByEmail(String email) {
        // 构建 LambdaQueryWrapper 查询条件
        LambdaQueryWrapper<StripeUserCustomer> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StripeUserCustomer::getEmail, email);
        // 使用 getOne 方法
        return this.getOne(queryWrapper);
    }

    @Override
    public StripeUserCustomer getStripeCustomerByUserId(Long userId) {
        // 构建 LambdaQueryWrapper 查询条件
        LambdaQueryWrapper<StripeUserCustomer> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StripeUserCustomer::getUserId, userId);
        // 使用 getOne 方法
        return this.getOne(queryWrapper);
    }


}