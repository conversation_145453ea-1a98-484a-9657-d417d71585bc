package com.lx.pl.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.csp.sentinel.util.StringUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lx.pl.client.SendCloudEmailClient;
import com.lx.pl.db.mysql.gen.entity.GptContacts;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.db.mysql.gen.mapper.GptContactsMapper;
import com.lx.pl.db.mysql.gen.mapper.UserMapper;
import com.lx.pl.exception.ServerInternalException;
import com.lx.pl.service.*;
import com.lx.pl.util.FileUtils;
import com.lx.pl.util.JsonUtils;
import com.lx.pl.util.MyEnvironmentUtils;
import com.sendcloud.sdk.util.ResponseData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.lx.pl.service.ImgUploadCommonService.getFilePath;

/**
 * 联系我们Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-10
 */
@Slf4j
@Service
public class GptContactsServiceImpl extends ServiceImpl<GptContactsMapper, GptContacts> implements IGptContactsService {
    @Resource
    private GptContactsMapper gptContactsMapper;
    @Resource
    private ResourceLoader resourceLoader;
    @Resource
    SendCloudEmailClient sendCloudEmailClient;

    @Value("${sendcloud.api.key}")
    String apiKey;
    @Value("${sendcloud.api.user}")
    String apiUser;
    @Value("${contactUs.opex.loginName}")
    String contactUsEmail;

    @Resource
    private GenService genApiService;

    @Autowired
    ImgUploadCommonService imgUploadCommonService;

    @Resource
    private UserService userService;

    @Resource
    private ImgDownloadCommonService imgDownloadCommonService;

    @Resource
    private UserMapper userMapper;
    @Resource
    private EmailService emailService;

    /**
     * 查询联系我们
     *
     * @param id 联系我们主键
     * @return 联系我们
     */
    @Override
    public GptContacts selectGptContactsById(Long id) {
        return gptContactsMapper.selectGptContactsById(id);
    }

    /**
     * 查询联系我们列表
     *
     * @param gptContacts 联系我们
     * @return 联系我们
     */
    @Override
    public List<GptContacts> selectGptContactsList(GptContacts gptContacts) {
        return gptContactsMapper.selectGptContactsList(gptContacts);
    }

    /**
     * 新增联系我们
     *
     * @param gptContacts 联系我们
     * @return 结果
     */
    @Override
    public int insertGptContacts(GptContacts gptContacts) {
        gptContacts.setCreateTime(LocalDateTime.now());
        return gptContactsMapper.insertGptContacts(gptContacts);
    }

    /**
     * 修改联系我们
     *
     * @param gptContacts 联系我们
     * @return 结果
     */
    @Override
    public int updateGptContacts(GptContacts gptContacts) {
        gptContacts.setUpdateTime(LocalDateTime.now());
        return gptContactsMapper.updateGptContacts(gptContacts);
    }

    /**
     * 批量删除联系我们
     *
     * @param ids 需要删除的联系我们主键
     * @return 结果
     */
    @Override
    public int deleteGptContactsByIds(Long[] ids) {
        return gptContactsMapper.deleteGptContactsByIds(ids);
    }

    /**
     * 删除联系我们信息
     *
     * @param id 联系我们主键
     * @return 结果
     */
    @Override
    public int deleteGptContactsById(Long id) {
        return gptContactsMapper.deleteGptContactsById(id);
    }

    @Override
    public int createGptContacts(GptContacts gptContacts, String platform) throws IOException {
        String from = "<EMAIL>";
        String fromName = "<EMAIL>";
        String to = gptContacts.getEmail();
        String subject = "We've Got Your Message at PicLumen";

        //默认邮件模板
        String templatesPath = "classpath:templates/autoReplyTemplate.html";
        if (MyEnvironmentUtils.isStagingEnvironment() && "android".equals(platform)) {
            templatesPath = "classpath:templatesAndroid/autoReplyTemplate.html";
        }
        if (!Objects.isNull(gptContacts.getReason()) && (gptContacts.getReason() == 7)) {
            //支付邮件模板
            templatesPath = "classpath:templates/payAutoReplyTemplate.html";
            if (MyEnvironmentUtils.isStagingEnvironment() && "android".equals(platform)) {
                templatesPath = "classpath:templatesAndroid/payAutoReplyTemplate.html";
            }
        }

        org.springframework.core.io.Resource resource = resourceLoader.getResource(templatesPath);
        InputStreamReader reader = new InputStreamReader(resource.getInputStream(), StandardCharsets.UTF_8);
        StringBuilder htmlBuilder = new StringBuilder();
        int c;
        while ((c = reader.read()) != -1) {
            htmlBuilder.append((char) c);
        }
        String html = htmlBuilder.toString();

        log.info("发送自动回复邮件请求参数: mail :{} ", to);
        if (MyEnvironmentUtils.isStagingEnvironment() && "android".equals(platform)) {
            subject = "We've Got Your Message at Imglumen";
            emailService.sendAndroidHtmlMessage(to, subject, html);
        } else {
            ResponseData response = sendCloudEmailClient.sendEmail(from, fromName, to, subject, html);

            if (200 == response.getStatusCode() && Boolean.TRUE == response.getResult()) {
                log.info("邮件发送成功，接收邮箱为：{}", to);
            } else {
                throw new ServerInternalException("Failed to send code. Try again later !");
            }
        }


        Boolean isEmail = Boolean.TRUE;

        // 鉴黄检测
        if (CollectionUtil.isNotEmpty(gptContacts.getUrlList())) {
            Boolean isUpdate = Boolean.TRUE;
            List<String> urlList = gptContacts.getUrlList();
            List<String> processedUrls = new ArrayList<>();
            String userId = userService.queryUserIdByName(gptContacts.getLoginName());
            User user = userMapper.selectById(userId);
            isEmail = !user.getContactBlackListFlag();

            if (isEmail) {
                for (String imgUrl : urlList) {
                    try {
                        String newImgUrl = imgUrl.split("[?]")[0];
                        String[] suffix = newImgUrl.split("/");
                        //得到最后一个分隔符后的名字
                        String fileName = suffix[suffix.length - 1];
                        File file = null;
                        file = File.createTempFile("report", fileName);//创建临时文件
                        imgDownloadCommonService.downloadToFileNoAccelerate(getFilePath(imgUrl), file);
                        String ndResult = genApiService.ndPicture(file);

                        // 检测到鉴黄信息则处理马赛克并上传
                        if (StringUtil.isNotBlank(ndResult)) {

                            if (isUpdate) {

                                // 如果鉴黄成功禁止用户发邮件
                                LambdaUpdateWrapper<User> uuw = new LambdaUpdateWrapper<>();
                                uuw.set(User::getContactBlackListFlag, Boolean.TRUE);
                                uuw.eq(User::getId, userId);
                                userMapper.update(null, uuw);
                                isUpdate = Boolean.FALSE;
                            }

                            File mosaicFile = FileUtils.createMosaic(file);
                            String newUrl = imgUploadCommonService.uploadToOss(mosaicFile, userId);
                            processedUrls.add(newUrl);
                        } else {
                            processedUrls.add(imgUrl);
                        }
                    } catch (IOException e) {
                        // 处理异常时添加原始 URL
                        processedUrls.add(imgUrl);
                    }
                }
            }

            gptContacts.setUrlList(processedUrls);
        }

        log.info("该用户是否发送运维邮件:{} ", isEmail);
        /**
         * 邮件通知运维
         */
        if (isEmail) {
            try {
                sendEmail(gptContacts);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        gptContacts.setCreateBy("system");

        // 将 urlList 转换为逗号分隔的字符串并设置到 thumbnailUrls
        if (gptContacts.getUrlList() != null && !gptContacts.getUrlList().isEmpty()) {
            String joinedUrls = String.join(",", gptContacts.getUrlList());
            gptContacts.setThumbnailUrls(joinedUrls);
        }

        return gptContactsMapper.insertGptContacts(gptContacts);
    }

    /**
     * 将收到的用户反馈以邮件形式通知运维
     */
    public void sendEmail(GptContacts gptContacts) throws IOException {
        String from = "<EMAIL>";
        String fromName = "<EMAIL>";
        String to = contactUsEmail;
        String subject = "PicLumen用户反馈信息";

        org.springframework.core.io.Resource resource = resourceLoader.getResource("classpath:templates/userContactUsTemplate.html");
        InputStreamReader reader = new InputStreamReader(resource.getInputStream(), StandardCharsets.UTF_8);
        StringBuilder htmlBuilder = new StringBuilder();
        int c;
        while ((c = reader.read()) != -1) {
            htmlBuilder.append((char) c);
        }
        StringBuilder imagesHtml = new StringBuilder();

        // 获取 urlList，并检查是否为空
        List<String> urlList = gptContacts.getUrlList();
        if (CollectionUtil.isNotEmpty(urlList)) {
            // 遍历 urlList，生成图片标签
            for (String url : urlList) {
                imagesHtml.append("<img src=\"").append(url).append("\" alt=\"用户上传的图片\">");
            }
        } else {
            // 如果没有图片，显示提示
            imagesHtml.append("<p>没有上传图片。</p>");
        }
        String userAgent = StringUtils.isNotBlank(gptContacts.getUserAgent()) ? gptContacts.getUserAgent() : "";
        // 将 URL 列表的 HTML 替换进模板中
        String html = htmlBuilder.toString()
                .replace("${fullName}", gptContacts.getFullName())
                .replace("${email}", gptContacts.getEmail())
                .replace("${message}", gptContacts.getMessage())
                .replace("${userAgent}", userAgent)
                .replace("${urlList}", imagesHtml.toString());  // 替换成生成的图片 HTML

        log.info("发送自动回复邮件请求参数: mail :{} ", to);
        if (!MyEnvironmentUtils.isProdEnvironment()) {
            return; // 非 prod 环境不校验
        }
        ResponseData response = sendCloudEmailClient.sendEmail(from, fromName, to, subject, html);
        log.info("调用发送邮件返回信息: {}", JsonUtils.writeToString(response));

        if (200 == response.getStatusCode() && Boolean.TRUE == response.getResult()) {
            log.info("邮件发送成功，接收邮箱为：{}", to);
        } else {
            log.error("联系我们通知运维邮件失败，失败用户fullName：{}", gptContacts.getFullName());
        }
    }

}
