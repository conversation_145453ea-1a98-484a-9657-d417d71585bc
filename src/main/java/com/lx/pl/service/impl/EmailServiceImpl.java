package com.lx.pl.service.impl;

import com.lx.pl.config.MailProperties;
import com.lx.pl.service.EmailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;
import java.util.Properties;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class EmailServiceImpl implements EmailService {


    @Autowired
    private JavaMailSender javaMailSender;
    @Autowired
    private MailProperties mailProperties;


    @Override
    public void sendAndroidHtmlMessage(String content, String subject) {
        SimpleMailMessage message = new SimpleMailMessage();
        // 邮件发送人
        message.setFrom(mailProperties.getFrom());
        // 邮件接收人（可以使用 String[] 发送给多个用户）
        message.setTo(mailProperties.getToUsers().toArray(String[]::new));
        // 邮件标题
        message.setSubject(subject);
        // 邮件内容
        message.setText(content);
        // 发送邮件
        javaMailSender.send(message);
    }

    @Override
    public void sendAndroidHtmlMessage(String to, String subject, String htmlContent) {
        try {
            MimeMessage message = javaMailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");
            helper.setTo(to);
            helper.setSubject(subject);
            helper.setText(htmlContent, true); // true表示这是HTML
            // 发送邮件
            javaMailSender.send(message);
        } catch (Exception e) {
            log.error("sendAndroidMessage error: {}", e);
        }

    }

}
