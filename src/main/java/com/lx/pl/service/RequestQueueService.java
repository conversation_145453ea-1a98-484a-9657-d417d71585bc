package com.lx.pl.service;

import com.lx.pl.dto.RequestQueueParams;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;

@Service
public class RequestQueueService {
    private static final int CONSUMER_THREAD_POOL_SIZE = 3;
    private final BlockingQueue<Runnable> requestQueue;
    private final ExecutorService executorService;
    private final AtomicBoolean isConsumerStarted = new AtomicBoolean(false);
    private double intervalSeconds;

    public RequestQueueService(RequestQueueParams requestQueueParams) {
        this.requestQueue = new LinkedBlockingQueue<>(requestQueueParams.getQueueSize());
        this.executorService = new ThreadPoolExecutor(
                1, 1, 0L, TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<>(requestQueueParams.getQueueSize()), Executors.defaultThreadFactory(), new ThreadPoolExecutor.AbortPolicy()
        );
        this.intervalSeconds = requestQueueParams.getInterval(); // 赋值
    }

    @Async
    public CompletableFuture<Boolean> addRequest(Runnable requestTask) {
        if (requestQueue.offer(requestTask)) {
            if (isConsumerStarted.compareAndSet(false, true)) {
                startRequestConsumer();
            }
            return CompletableFuture.completedFuture(true);
        }
        return CompletableFuture.completedFuture(false);
    }

    private void startRequestConsumer() {
        executorService.submit(() -> {
            while (true) {
                try {
                    Runnable requestTask = requestQueue.poll(1, TimeUnit.SECONDS);
                    if (requestTask != null) {
                        // 使用 synchronized 确保同一时刻只有一个线程执行任务，避免重复消费
                        synchronized (requestTask) {
                            requestTask.run();
                            Thread.sleep((long) (intervalSeconds * 1000));
                        }
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    e.printStackTrace();
                }
            }
        });
    }
}
