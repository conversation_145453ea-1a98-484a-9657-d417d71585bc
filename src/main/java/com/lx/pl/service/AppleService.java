package com.lx.pl.service;


import java.io.UnsupportedEncodingException;
import java.math.BigInteger;
import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.spec.RSAPublicKeySpec;
import java.util.List;
import java.util.Objects;
//import com.alibaba.fastjson.JSONArray;
//import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.lx.pl.client.AppleApi;
import com.lx.pl.dto.AppleLoginDTO;
import com.lx.pl.dto.BackendAppleKeyResult;
import com.lx.pl.dto.BackendAppleKeysResult;
import com.lx.pl.dto.BackendPromptResult;
import com.lx.pl.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import io.jsonwebtoken.*;
import retrofit2.Response;

import javax.annotation.Resource;


@Service
@Slf4j
public class AppleService {

    private static final Logger LOGGER = LoggerFactory.getLogger(AppleService.class);

    @Value("${apple.auth.url}")
    private String appleAuthUrl;

    @Value("${apple.iss.url}")
    private String appleIssUrl;

    @Autowired
    private AppleApi appleApi;

    public String appleLogin(AppleLoginDTO appleLoginDTO) throws JsonProcessingException {
        String identityToken = appleLoginDTO.getIdentityToken();
        System.out.println("in ");
        // 获取秘钥的返回信息
        String firstDate = null;
        String claim = null;
        try {
            firstDate = new String(Base64.decodeBase64(identityToken.split("\\.")[0]), "UTF-8");
            claim = new String(Base64.decodeBase64(identityToken.split("\\.")[1]), "UTF-8");
        } catch (UnsupportedEncodingException e) {
            LOGGER.error("apple授权码异常，identityToken[%s], %s", identityToken, e);
        }
        // 开发者帐户中获取的 10 个字符的标识符密钥
//        String kid = JSONObject.parseObject(firstDate).get("kid").toString();
//        String aud = JSONObject.parseObject(claim).get("aud").toString();
//        String sub = JSONObject.parseObject(claim).get("sub").toString();
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode firstDateNode = objectMapper.readTree(firstDate);
        String kid = firstDateNode.get("kid").asText();

        JsonNode claimNode = objectMapper.readTree(claim);
        String aud = claimNode.get("aud").asText();
        String sub = claimNode.get("sub").asText();

        PublicKey publicKey = this.getPublicKey(kid);
        if (Objects.isNull(publicKey)) {
            throw new RuntimeException("apple授权登录的数据异常");
        }

        boolean reuslt = this.verifyAppleLoginCode(publicKey, identityToken, aud, sub);

        if (reuslt) {

        }
        return null;
    }

    public boolean verifyAppleLoginCode(PublicKey publicKey, String identityToken, String aud, String sub) {
        boolean result = false;
        JwtParser jwtParser = Jwts.parser().setSigningKey(publicKey);
        jwtParser.requireIssuer(appleIssUrl);
        jwtParser.requireAudience(aud);
        jwtParser.requireSubject(sub);
        try {
            Jws<Claims> claim = jwtParser.parseClaimsJws(identityToken);
            if (claim != null && claim.getBody().containsKey("auth_time")) {
                result = true;
            }
        } catch (ExpiredJwtException e) {
            throw new RuntimeException(String.format("apple登录授权identityToken过期.", e));
        } catch (SignatureException e) {
            throw new RuntimeException(String.format("apple登录授权identityToken非法.", e));
        }
        return result;
    }

    public PublicKey getPublicKey(String kid) {
        try {
            Response<BackendAppleKeysResult> responseBody = appleApi.getModelInformation();
            log.info("调用apple公钥返回返回信息: {}", JsonUtils.writeToString(responseBody.body()));
            // 请求成功
            List<BackendAppleKeyResult> keys = responseBody.body().getKeys();
            // 判断数组是否为空
            if (keys.isEmpty()) {
                return null;
            }
            // 遍历 keysArray
            for (BackendAppleKeyResult backendAppleKeyResult : keys) {
                String keyKid = backendAppleKeyResult.getKid();
                if (kid.equals(keyKid)) {
                    String n = backendAppleKeyResult.getN();
                    String e = backendAppleKeyResult.getE();
                    BigInteger modulus = new BigInteger(1, Base64.decodeBase64(n));
                    BigInteger publicExponent = new BigInteger(1, Base64.decodeBase64(e));
                    RSAPublicKeySpec spec = new RSAPublicKeySpec(modulus, publicExponent);
                    KeyFactory kf = KeyFactory.getInstance("RSA");
                    return kf.generatePublic(spec);
                }
            }
            return null;  // 如果没有找到匹配的 kid，返回 null
        } catch (Exception ex) {
            LOGGER.error("获取PublicKey异常.", ex);
        }
        return null;
    }
}