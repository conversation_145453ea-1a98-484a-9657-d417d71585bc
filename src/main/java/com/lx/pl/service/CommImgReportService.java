package com.lx.pl.service;

import com.lx.pl.db.mysql.community.entity.*;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.db.mysql.gen.repository.CommImgReportRepository;
import com.lx.pl.exception.ServerInternalException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
@Slf4j
public class CommImgReportService {

    @Autowired
    private CommImgReportRepository commImgReportRepository;

    @Autowired
    private CommImgService commImgService;

    @Autowired
    private MongoTemplate mongoTemplate;

    /**
     * 对社区图片进行举报
     *
     * @param commFileId
     * @param user
     * @return
     */
    public Boolean reportCommImg(String commFileId, Integer auditType, String otherContent, User user) {
        try {
            CommFile commFile = commImgService.getCommFileById(commFileId);
            if (!Objects.isNull(commFile)) {
                CommImgReport commImgReport = new CommImgReport();
                commImgReport.setFileId(commFileId);
                //举报发起者用户
                AccountInfo reportAccInfo = AccountInfo.builder()
                        .userId(user.getId())
                        .userName(user.getUserName())
                        .userLoginName(user.getLoginName())
                        .userAvatarUrl(user.getAvatarUrl())
                        .whetherPro(Boolean.FALSE)
                        .build();
                commImgReport.setOwnerAcc(reportAccInfo);
                commImgReport.setAuditType(auditType);
                commImgReport.setOtherContent(otherContent);
                commImgReportRepository.insert(commImgReport);
                return Boolean.TRUE;
            }
        } catch (Exception e) {
            log.error("用户：{} 对图片：{} 举报报错", user.getId(), commFileId, e);
            throw new ServerInternalException("Report Failed");
        }

        return Boolean.FALSE;
    }

    public CommImgReport getCommReportByUserIdAndFileId(Long userId, String fileId) {
        try {
            Query query = new Query();
            query.addCriteria(Criteria.where("ownerAcc.userId").is(userId));
            query.addCriteria(Criteria.where("fileId").is(fileId));

            CommImgReport commImgReport = mongoTemplate.findOne(query, CommImgReport.class);
            return commImgReport;
        } catch (Exception e) {
            log.error("查询社区图片举报报错，图片id为：{}，用户id:{}", fileId, userId, e);
            return null;
        }
    }
}
