package com.lx.pl.service;

import com.lx.pl.db.mysql.community.entity.LumenChangeRecord;
import com.lx.pl.db.mysql.gen.entity.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/6/7
 * @description lumen变化记录Service
 */
@Slf4j
@Service
public class LumenChangeRecordService {
    @Resource
    private MongoTemplate mongoTemplate;

    public void saveOrUpdateLumenChangeRecord(User user, int changeLumen, String changeType, String source, String detail, String batchId) {
        if (StringUtils.isNotBlank(batchId)) {
            LumenChangeRecord one = mongoTemplate.findOne(
                    new Query(Criteria.where("batchId").is(batchId)
                            .and("userId").is(user.getId())),
                    LumenChangeRecord.class
            );
            if (one != null) {
                one.setChangeLumen(one.getChangeLumen() + changeLumen);
                one.setUpdateTime(LocalDateTime.now());
                mongoTemplate.save(one);
                return;
            }
        }
        LumenChangeRecord changeRecord = new LumenChangeRecord();
        changeRecord.setUserId(user.getId());
        changeRecord.setUserLoginName(user.getLoginName());
        changeRecord.setChangeType(changeType);
        changeRecord.setChangeLumen(changeLumen);
        changeRecord.setSource(source);
        changeRecord.setDetail(detail);
        changeRecord.setBatchId(batchId);
        changeRecord.setHappenTime(LocalDateTime.now());
        changeRecord.setCreateTime(LocalDateTime.now());
        changeRecord.setUpdateTime(LocalDateTime.now());
        changeRecord.setCreateBy(detail);
        changeRecord.setUpdateBy(detail);
        mongoTemplate.insert(changeRecord);
    }
}
