package com.lx.pl.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.lx.pl.config.CosConfig;
import com.lx.pl.enums.UploadType;
import com.lx.pl.util.IdUtils;
import com.qcloud.cos.model.DeleteObjectsRequest;
import com.qcloud.cos.model.DeleteObjectsResult;
import com.qcloud.cos.model.ObjectMetadata;
import com.qcloud.cos.model.ciModel.persistence.CIUploadResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import static com.lx.pl.service.CosService.buildFileNameKey;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ImgUploadCommonService {

    @Autowired
    private CosService cosService;
    @Resource
    public CosConfig cosConfig;

    private static final Executor COS_THREAD_POOL = new ThreadPoolExecutor(Runtime.getRuntime()
            .availableProcessors(),
            Runtime.getRuntime()
                    .availableProcessors() * 2, 60, TimeUnit.SECONDS, new ArrayBlockingQueue<>(1000),
            Executors.defaultThreadFactory(), new ThreadPoolExecutor.CallerRunsPolicy());

    /**
     * 桶/日期/userId
     *
     * @param file
     * @param userId 用户ID
     * @return
     */
    public String uploadToOss(File file, String userId) {
        return uploadToOssWithType(file, userId, UploadType.normal.getValue());
    }

    public String uploadToOssWithType(File file, String userId, String type) {
        String name = file.getName();
        String path = buildFileNameKey(userId, name.split("\\.")[1], type);
        cosService.uploadTc(path, file);
        return cosConfig.getCosAccelerateDomain() + path;
    }

    /**
     * 获取文件元数据
     *
     * @param fileKey
     * @param isOld
     * @return
     */
    public ObjectMetadata getMetadata(String fileKey, boolean isOld) {
        return cosService.getMetadata(fileKey, isOld);
    }

    public static String getFilePath(String fullPathUrl) {
        String path = URLUtil.getPath(fullPathUrl);
        if (path.startsWith("/")) {
            return path.substring(1);
        }
        return path;
    }

    public CIUploadResult imageProcessToThumbnail(String key, String thumbnailKey, String highThumbnailKey) {
        return cosService.imageProcess(key, thumbnailKey, highThumbnailKey);
    }

    public CIUploadResult imageProcessToWebp90(String key, String highThumbnailKey, boolean isOld) {
        return cosService.imageProcessToWebp90(key, highThumbnailKey, isOld);
    }

    public CompletableFuture<CIUploadResult> imageProcessToMingUrlAsync(String key, String miniThumbnailKey, boolean isOld) {
        return CompletableFuture.supplyAsync(() -> cosService.imageProcessToMingUrl(key, miniThumbnailKey, isOld), COS_THREAD_POOL);
    }


    /**
     * webp to png
     *
     * @param srcWebpKey /noraml/wwww.webp
     * @return https://images.piclumen.com/noraml/2212.png
     */
    public String webpToPng(String srcWebpKey) {
        String destPngKey = buildFileNameKeyNoUserId("png", UploadType.normal.getValue());
        cosService.webpToPng(srcWebpKey, destPngKey);
        return cosConfig.getCosAccelerateDomain() + destPngKey;
    }

    private static String buildFileNameKeyNoUserId(String fileExt, String type) {
        if (StrUtil.isBlank(type)) {
            type = UploadType.normal.getValue();
        }
        StringBuilder sb = new StringBuilder("/" + type + "/");
        LocalDateTime now = LocalDateTime.now();
        String path = sb.append(DateUtil.format(now, "yyyyMMdd"))
                .append("/")
                .append(DateUtil.format(now, "HH"))
                .append("/")
                .append(IdUtils.randomUUID())
                .append(".")
                .append(fileExt)
                .toString();
        return path;
    }

    /**
     * 判断是否是旧桶
     *
     * @param url
     * @return
     */
    public static boolean isOldBucket(String url) {
        if (StrUtil.isBlank(url)) {
            return false;
        }
        return url.contains("piclumen-1324066212");
    }

    public DeleteObjectsResult deleteObjects(List<DeleteObjectsRequest.KeyVersion> keys) {
        if (CollUtil.isEmpty(keys)) {
            return null;
        }
        return cosService.deleteObjects(keys);
    }

    /**
     * 批量删除
     *
     * @param keys
     * @return
     */
    public DeleteObjectsResult deleteObjectsOldBucket(List<DeleteObjectsRequest.KeyVersion> keys) {
        if (CollUtil.isEmpty(keys)) {
            return null;
        }
        return cosService.deleteObjectsOldBucket(keys);
    }

    public CompletableFuture<Void> deleteObjectsWithUrlListAsync(List<String> urlList) {
        if (CollUtil.isEmpty(urlList)) {
            return null;
        }
        List<List<String>> split = CollUtil.split(urlList, 1000);
        List<CompletableFuture<DeleteObjectsResult>> futureList = new ArrayList<>();
        for (List<String> urls : split) {
            List<DeleteObjectsRequest.KeyVersion> oldKeys = new ArrayList<>();
            List<DeleteObjectsRequest.KeyVersion> newKes = new ArrayList<>();
            for (String url : urls) {
                String key = getFilePath(url);
                if (url.contains(cosConfig.getBucketOld())) {
                    oldKeys.add(new DeleteObjectsRequest.KeyVersion(key));
                } else {
                    newKes.add(new DeleteObjectsRequest.KeyVersion(key));
                }
            }
            if (CollUtil.isNotEmpty(oldKeys)) {
                CompletableFuture<DeleteObjectsResult> result = CompletableFuture.supplyAsync(() -> {
                    log.info("删除cos成功, old keys: {}", oldKeys.stream().map(DeleteObjectsRequest.KeyVersion::getKey).collect(Collectors.joining(",")));
                    return deleteObjectsOldBucket(oldKeys);
                }, COS_THREAD_POOL);
                futureList.add(result);
            }

            if (CollUtil.isNotEmpty(newKes)) {
                CompletableFuture<DeleteObjectsResult> result2 = CompletableFuture.supplyAsync(() -> {
                    DeleteObjectsResult deleteObjectsResult = deleteObjects(newKes);
                    log.info("删除cos成功,new keys: {}", newKes.stream().map(DeleteObjectsRequest.KeyVersion::getKey).collect(Collectors.joining(",")));
                    return deleteObjectsResult;
                }, COS_THREAD_POOL);
                futureList.add(result2);
            }
        }
        return CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0]));
    }
}
