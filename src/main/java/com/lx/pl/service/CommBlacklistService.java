package com.lx.pl.service;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.lx.pl.db.mysql.community.entity.*;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.db.mysql.gen.repository.CommBlacklistRepository;
import com.lx.pl.dto.CommPageInfo;
import com.lx.pl.exception.ServerInternalException;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class CommBlacklistService {

    @Autowired
    private CommBlacklistRepository commBlacklistRepository;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private CommUserService commUserService;

    public Boolean addCommBlacklist(Long userId, User user) {
        RLock lock = redissonClient.getLock("blacklist:user:" + userId);
        try {
            lock.lock();
            CommUser commUser = commUserService.findUserByUserId(userId);

            if (!Objects.isNull(commUser)) {
                CommBlacklist commBlacklist = new CommBlacklist();

                commBlacklist.setCreateTime(LocalDateTime.now());
                //发起关注用户
                AccountInfo ownerAcc = new AccountInfo();
                ownerAcc.setUserId(user.getId());
                ownerAcc.setUserName(user.getUserName());
                ownerAcc.setUserLoginName(user.getLoginName());
                ownerAcc.setUserAvatarUrl(user.getAvatarUrl());
                ownerAcc.setWhetherPro(Boolean.FALSE);
                commBlacklist.setOwnerAcc(ownerAcc);

                //被关注用户
                AccountInfo targetAcc = new AccountInfo();
                BeanUtils.copyProperties(commUser.getAccountInfo(), targetAcc);
                commBlacklist.setTargetAcc(targetAcc);

                commBlacklistRepository.insert(commBlacklist);
            }

            return Boolean.TRUE;
        } catch (Exception e) {
            log.error("用户：{},拉黑目标：{} 失败", user.getId(), userId, e);
            throw new ServerInternalException("Add Blacklist Failed");
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    public Boolean reduceCommBlacklist(Long userId, User user) {
        RLock lock = redissonClient.getLock("blacklist:user:" + userId);
        try {
            lock.lock();
            Query query = new Query();
            query.addCriteria(Criteria.where("targetAcc.userId").is(userId));
            query.addCriteria(Criteria.where("ownerAcc.userId").is(user.getId()));

            // 执行删除操作并判断是否删除了至少一个文档
            return mongoTemplate.remove(query, CommBlacklist.class).getDeletedCount() > 0;
        } catch (Exception e) {
            log.error("用户：{} 对拉黑用户：{} 移除拉黑失败", user.getId(), userId, e);
            throw new ServerInternalException("Reduce Blacklist Failed");
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }


    public CommPageInfo<CommBlacklist> getCommBlacklist(String lastBlacklistId, Integer pageSize, User user) {
        List<CommBlacklist> commBlacklists = new ArrayList<>();
        try {
            Query query = new Query();
            //查询用户拉黑列表
            query.addCriteria(Criteria.where("ownerAcc.userId").is(user.getId()));

            // 游标分页：基于上一页最后一条记录的 ID
            if (StringUtil.isNotBlank(lastBlacklistId)) {
                query.addCriteria(Criteria.where("id").lt(new ObjectId(lastBlacklistId))); // 仅获取 ID 小于游标的记录
            }

            query.with(Sort.by(Sort.Direction.DESC, "id"));

            // 设置分页
            query.limit(pageSize);
            commBlacklists = mongoTemplate.find(query, CommBlacklist.class);
        } catch (Exception e) {
            log.error("用户：{} 获取用户拉黑列表失败", user.getId(), e);
            throw new ServerInternalException("Failed to retrieve the user's following or followers list");
        }
        return buildPromptPageInfo(pageSize, commBlacklists);
    }

    // 构建分页结果对象
    private CommPageInfo<CommBlacklist> buildPromptPageInfo(Integer pageSize, List<CommBlacklist> commBlacklists) {
        CommPageInfo<CommBlacklist> commPageInfo = new CommPageInfo<>();
        commPageInfo.setResultList(CollectionUtils.isEmpty(commBlacklists) ? Collections.emptyList() : commBlacklists);
        commPageInfo.setPageSize(pageSize);
        commPageInfo.setLastId(CollectionUtils.isEmpty(commBlacklists) ? "" : commBlacklists.get(commBlacklists.size() - 1).getId());
        return commPageInfo;
    }


    public CommBlacklist getCommBlacklistByUserIdAndTargetUserId(Long userId, User user) {
        try {
            Query query = new Query();
            query.addCriteria(Criteria.where("targetAcc.userId").is(userId));
            query.addCriteria(Criteria.where("ownerAcc.userId").is(user.getId()));
            CommBlacklist commBlacklist = mongoTemplate.findOne(query, CommBlacklist.class);
            return commBlacklist;
        } catch (Exception e) {
            log.error("查询拉黑信息报错，用户id为：{}，目标id:{}", user.getId(), userId, e);
            return null;
        }
    }
}
