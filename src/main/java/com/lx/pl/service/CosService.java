package com.lx.pl.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.lx.pl.config.CosConfig;
import com.lx.pl.enums.UploadType;
import com.lx.pl.util.DateUtils;
import com.lx.pl.util.IdUtils;
import com.lx.pl.util.StringUtils;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.http.HttpMethodName;
import com.qcloud.cos.model.*;
import com.qcloud.cos.model.ciModel.common.ImageProcessRequest;
import com.qcloud.cos.model.ciModel.persistence.CIUploadResult;
import com.qcloud.cos.model.ciModel.persistence.PicOperations;
import com.qcloud.cos.transfer.Copy;
import com.qcloud.cos.transfer.TransferManager;
import com.tencent.cloud.CosStsClient;
import com.tencent.cloud.Response;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.*;

/**
 * 腾讯云初始化配置
 */
@Service
@Slf4j
public class CosService {

    // 腾讯云全球加速
    @Resource
    public COSClient cosClient;
    // 腾讯云内网全球加速
    @Resource
    public COSClient nwCosClient;
    @Resource(name = "baseCosClient")
    @Getter
    private COSClient baseCosClient;
    @Resource
    public CosConfig cosConfig;

    @Resource(name = "transferManager")
    private TransferManager transferManager;

    private static final Executor COPY_THREAD_POOL = new ThreadPoolExecutor(Runtime.getRuntime()
            .availableProcessors(),
            Runtime.getRuntime()
                    .availableProcessors(), 60, TimeUnit.SECONDS, new ArrayBlockingQueue<>(100),
            Executors.defaultThreadFactory(), new ThreadPoolExecutor.CallerRunsPolicy());


    /**
     * 获取签名图片url
     *
     * @param KEY
     * @return
     */
    public String getSignUrl(String KEY) {
        GeneratePresignedUrlRequest generatePresignedUrlRequest = new GeneratePresignedUrlRequest(cosConfig.bucket, KEY);
        // 设置过期时间，这里设置为永久
        generatePresignedUrlRequest.setExpiration(DateUtils.getPermanentTime());
        // 生成签名URL
        URL signedUrl = cosClient.generatePresignedUrl(generatePresignedUrlRequest);
        return signedUrl.toString();
    }

    /**
     * 上传图片到腾讯云
     *
     * @param
     * @param key
     * @param file
     * @return
     */
    public PutObjectResult uploadTc(String key, File file) {
        if (cosConfig.getEnableInternalAccelerateUpload()) {
            PutObjectRequest putObjectRequest = new PutObjectRequest(cosConfig.bucket, key, file);
            return nwCosClient.putObject(putObjectRequest);
        }
        return uploadTcNoAccelerate(key, file);
    }

    /**
     * 上传图片到腾讯云
     * 直接走内网不走全球加速
     *
     * @param
     * @param key
     * @param file
     * @return
     */
    public PutObjectResult uploadTcNoAccelerate(String key, File file) {
        PutObjectRequest putObjectRequest = new PutObjectRequest(cosConfig.getBucket(), key, file);
        return baseCosClient.putObject(putObjectRequest);
    }

    /**
     * 批量刪除objects
     *
     * @param fileKeys eg: [test/201409/sabcdwed.png] 不能以‘/’ 开头
     * @return
     */
    public DeleteObjectsResult deleteObjects(String... fileKeys) {
        if (ArrayUtil.isEmpty(fileKeys)) {
            return null;
        }
        DeleteObjectsRequest deleteObjectsRequest = new DeleteObjectsRequest(cosConfig.getBucket());
        deleteObjectsRequest.withKeys(fileKeys);
        return baseCosClient.deleteObjects(deleteObjectsRequest);
    }

    /**
     * 获取metaInfo 信息
     */
    public ObjectMetadata getMetadata(String fileKey, boolean isOld) {
        if (isOld) {
            return nwCosClient.getObjectMetadata(cosConfig.getBucketOld(), fileKey);
        }
        return nwCosClient.getObjectMetadata(cosConfig.getBucket(), fileKey);
    }

    /**
     * 指定fileName权限
     *
     * @param fileName
     * @return
     */
    public Response getTmpCredential(String fileName) throws IOException {
        TreeMap<String, Object> config = cosConfig.buildBaseConfig(fileName);
        return CosStsClient.getCredential(config);
    }

    public Response getTmpCredential(String fileName, String thumbnailName, String highThumbnailName) throws IOException {
        TreeMap<String, Object> config = cosConfig.buildBaseConfig(fileName, thumbnailName, highThumbnailName);
        return CosStsClient.getCredential(config);
    }

    public static String buildFileNameKey(String userId, String ext) {
        return buildFileNameKey(userId, ext, UploadType.normal.getValue());
    }


    public LocalDateTime timestampToDatetime(long timestamp) {
        Instant instant = Instant.ofEpochMilli(timestamp);
        return LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
    }

    public static String buildFileNameKey(String userId, String fileExt, String type) {
        if (StrUtil.isBlank(type)) {
            type = UploadType.normal.getValue();
        }
        StringBuilder sb = new StringBuilder("/" + type + "/");
        LocalDateTime now = LocalDateTime.now();
        String path = sb.append(DateUtil.format(now, "yyyyMMdd"))
                .append("/")
                .append(DateUtil.format(now, "HH"))
                .append("/")
                .append(IdUtils.randomUUID())
                .append(".")
                .append(fileExt)
                .toString();
        return path;
    }

    public GeneratePresignedUrlRequest buildGeneratePresignedUrlRequest(String fileNameKey) {
        GeneratePresignedUrlRequest generatePresignedUrlRequest = new GeneratePresignedUrlRequest(cosConfig.getBucket(), fileNameKey);
        generatePresignedUrlRequest.setMethod(HttpMethodName.POST);
        Integer cosTokenDuration = Integer.valueOf(cosConfig.getCosTokenDuration());
        Date expirationTime = new Date(System.currentTimeMillis() + cosTokenDuration * 1000L);
        generatePresignedUrlRequest.setExpiration(expirationTime);
        // generatePresignedUrlRequest.addRequestParameter();
        return null;
    }

    public CIUploadResult imageProcess(String key, String thumbnailKey, String highThumbnailKey) {
        ImageProcessRequest imageProcessRequest = buildImageProcessRequest(key, thumbnailKey, highThumbnailKey, false);
        return baseCosClient.processImage(imageProcessRequest);
    }

    public CIUploadResult imageProcessToWebp90(String key, String highThumbnailKey, boolean isOld) {
        ImageProcessRequest imageProcessRequest = buildImageProcessRequest(key, null, highThumbnailKey, isOld);
        return baseCosClient.processImage(imageProcessRequest);
    }

    public CIUploadResult imageProcessToMingUrl(String key, String miniThumbnailKey, boolean isOld) {
        ImageProcessRequest imageReq;
        if (isOld) {
            imageReq = new ImageProcessRequest(cosConfig.getBucketOld(), key);
        } else {
            imageReq = new ImageProcessRequest(cosConfig.getBucket(), key);
        }

        PicOperations picOperations = new PicOperations();
        picOperations.setIsPicInfo(1);
        List<PicOperations.Rule> ruleList = new LinkedList<>();
        if (miniThumbnailKey != null) {
            PicOperations.Rule rule1 = new PicOperations.Rule();
            rule1.setBucket(cosConfig.getBucket());
            rule1.setFileId(miniThumbnailKey);
            rule1.setRule(cosConfig.getMiniRule());
            ruleList.add(rule1);
        }
        picOperations.setRules(ruleList);
        imageReq.setPicOperations(picOperations);
        return baseCosClient.processImage(imageReq);
    }

    private ImageProcessRequest buildImageProcessRequest(String key, String thumbnailKey, String highThumbnailKey, boolean isOld) {
        ImageProcessRequest imageReq;
        if (isOld) {
            imageReq = new ImageProcessRequest(cosConfig.getBucketOld(), key);
        } else {
            imageReq = new ImageProcessRequest(cosConfig.getBucket(), key);

        }
        PicOperations picOperations = new PicOperations();
        picOperations.setIsPicInfo(1);
        List<PicOperations.Rule> ruleList = new LinkedList<>();
        if (thumbnailKey != null) {
            PicOperations.Rule rule1 = new PicOperations.Rule();
            rule1.setBucket(cosConfig.getBucket());
            rule1.setFileId(thumbnailKey);
            rule1.setRule(cosConfig.getThumbnailRule());
            ruleList.add(rule1);
        }
        if (highThumbnailKey != null) {
            PicOperations.Rule rule2 = new PicOperations.Rule();
            rule2.setBucket(cosConfig.getBucket());
            rule2.setFileId(highThumbnailKey);
            rule2.setRule(cosConfig.getHighThumbnailRule());
            ruleList.add(rule2);
        }
        picOperations.setRules(ruleList);
        imageReq.setPicOperations(picOperations);
        return imageReq;
    }


    /**
     * webp转png
     *
     * @param srcWebpKey
     * @param destPngKey
     * @return
     */
    public String webpToPng(String srcWebpKey, String destPngKey) {
        ImageProcessRequest imageReq = new ImageProcessRequest(cosConfig.getBucket(), srcWebpKey);
        PicOperations picOperations = new PicOperations();
        picOperations.setIsPicInfo(1);
        List<PicOperations.Rule> ruleList = new LinkedList<>();
        PicOperations.Rule rule1 = new PicOperations.Rule();
        rule1.setBucket(cosConfig.getBucket());
        rule1.setFileId(destPngKey);
        rule1.setRule(cosConfig.getWebpToPngRule());
        ruleList.add(rule1);
        picOperations.setRules(ruleList);
        imageReq.setPicOperations(picOperations);
        CIUploadResult ciUploadResult = baseCosClient.processImage(imageReq);
        return destPngKey;
    }

    public DeleteObjectsResult deleteObjects(List<DeleteObjectsRequest.KeyVersion> keys) {
        if (CollUtil.isEmpty(keys)) {
            return null;
        }
        DeleteObjectsRequest deleteObjectsRequest = new DeleteObjectsRequest(cosConfig.getBucket());
        deleteObjectsRequest.withKeys(keys);
        return baseCosClient.deleteObjects(deleteObjectsRequest);
    }

    /**
     * 批量删除
     *
     * @param keys
     * @return
     */
    public DeleteObjectsResult deleteObjectsOldBucket(List<DeleteObjectsRequest.KeyVersion> keys) {
        if (CollUtil.isEmpty(keys)) {
            return null;
        }
        DeleteObjectsRequest deleteObjectsRequest = new DeleteObjectsRequest(cosConfig.getBucketOld());
        deleteObjectsRequest.withKeys(keys);
        return baseCosClient.deleteObjects(deleteObjectsRequest);
    }

    /**
     * 组装key
     * @param prefix
     * @param fileExt
     * @return
     */
    public String buildFileKey(String prefix, String fileExt) {
        StringBuilder sb = new StringBuilder(prefix + "/");
        LocalDateTime now = LocalDateTime.now();
        String path = sb.append(DateUtil.format(now, "yyyyMMdd"))
                .append("/")
                .append(DateUtil.format(now, "HH"))
                .append("/")
                .append(IdUtils.randomUUID())
                .append(".")
                .append(fileExt)
                .toString();
        return path;
    }

    /**
     * 腾讯云复制图片
     * @param fileUrl 原始图片url
     * @param fileKey 目标图片地址
     * @return
     */
    public CompletableFuture<Void> copyImgAsync(String fileUrl, String fileKey) {

        Map<String, String> copyMap = new HashMap<>();
        Map<String, String> copyMapOld = new HashMap<>();

        if (StringUtils.isNotBlank(fileUrl)) {
            fileUrl = fileUrl.startsWith("/") ? fileUrl.substring(1) : fileUrl;
            addToMap(fileUrl, fileKey, copyMap, copyMapOld);
        }

        CompletableFuture<Void> voidFuture = copyObject(copyMap, copyMapOld);
        return voidFuture;
    }

    /**
     * 兼容旧的桶
     * @param fileUrl
     * @param fileKey
     * @param copyMap
     * @param copyMapOld
     */
    private static void addToMap(String fileUrl, String fileKey, Map<String, String> copyMap, Map<String, String> copyMapOld) {
        if (org.apache.commons.lang3.StringUtils.isBlank(fileUrl)) {
            return;
        }
        if (fileUrl.contains("piclumen-1324066212")) {
            copyMapOld.put(URLUtil.getPath(fileUrl), fileKey);
        } else {
            copyMap.put(URLUtil.getPath(fileUrl), fileKey);
        }
    }

    /**
     * 复制图片
     */
    public CompletableFuture<Void> copyObject(Map<String, String> sourceDestMap, Map<String, String> sourceDestMapOld) {
        List<CompletableFuture<Void>> futures = CollUtil.newArrayList();
        if (!sourceDestMap.isEmpty()) {
            log.info("start copyObject:{}", sourceDestMap);
            sourceDestMap.forEach((sourceKey, targetKey) -> {
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    // 复制文件
                    Copy copy = transferManager.copy(cosConfig.getBucket(), sourceKey, cosConfig.getBucket(), targetKey);
                    try {
                        CopyResult copyResult = copy.waitForCopyResult();
                        log.info("copyObject new result:{}", copyResult.getDestinationKey());
                    } catch (Exception e) {
                        log.error("copyObject new error:", e);
                        throw new RuntimeException(e);
                    }
                }, COPY_THREAD_POOL);
                futures.add(future);
            });
        }

        if (!sourceDestMapOld.isEmpty()) {
            log.info("start old copyObject:{}", sourceDestMapOld);
            sourceDestMapOld.forEach((sourceKey, targetKey) -> {
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    // 复制文件
                    log.info("start old copyObject:{}", sourceKey);
                    Copy copy = transferManager.copy(cosConfig.getBucket(), sourceKey, cosConfig.getBucket(), targetKey);
                    try {
                        CopyResult copyResult = copy.waitForCopyResult();
                        log.info("copyObject old result:{}", copyResult.getDestinationKey());
                    } catch (Exception e) {
                        log.error("copyObject old error:", e);
                        throw new RuntimeException(e);
                    }
                    log.info("end old copyObject:{}", sourceKey);
                }, COPY_THREAD_POOL);
                futures.add(future);
            });
        }

        try {
            log.info("copyObject end");
            return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
        } catch (Exception e) {
            log.error("copyObject error:", e);
            throw new RuntimeException(e);
        }
    }


    /**
     * 腾讯云同步复制图片
     * @param fileUrl 原始图片url
     * @param fileKey 目标图片地址
     * @return
     */
    public void copyImgSync(String fileUrl, String fileKey) {

        Map<String, String> copyMap = new HashMap<>();
        Map<String, String> copyMapOld = new HashMap<>();

        if (StringUtils.isNotBlank(fileUrl)) {
            fileUrl = fileUrl.startsWith("/") ? fileUrl.substring(1) : fileUrl;
            addToMap(fileUrl, fileKey, copyMap, copyMapOld);
        }
        //同步复制图片
        syncCopyObject(copyMap, copyMapOld);
    }


    public void syncCopyObject(Map<String, String> sourceDestMap, Map<String, String> sourceDestMapOld) {
        if (!sourceDestMap.isEmpty()) {
            log.info("start copyObject:{}", sourceDestMap);
            sourceDestMap.forEach((sourceKey, targetKey) -> {
                try {
                    // 复制文件
                    Copy copy = transferManager.copy(cosConfig.getBucket(), sourceKey, cosConfig.getBucket(), targetKey);
                    CopyResult copyResult = copy.waitForCopyResult();
                    log.info("copyObject new result:{}", copyResult.getDestinationKey());
                } catch (Exception e) {
                    log.error("copyObject new error:", e);
                    throw new RuntimeException(e);
                }
            });
        }

        if (!sourceDestMapOld.isEmpty()) {
            log.info("start old copyObject:{}", sourceDestMapOld);
            sourceDestMapOld.forEach((sourceKey, targetKey) -> {
                try {
                    log.info("start old copyObject:{}", sourceKey);
                    // 复制文件
                    Copy copy = transferManager.copy(cosConfig.getBucket(), sourceKey, cosConfig.getBucket(), targetKey);
                    CopyResult copyResult = copy.waitForCopyResult();
                    log.info("copyObject old result:{}", copyResult.getDestinationKey());
                    log.info("end old copyObject:{}", sourceKey);
                } catch (Exception e) {
                    log.error("copyObject old error:", e);
                    throw new RuntimeException(e);
                }
            });
        }

        log.info("copyObject end");
    }

}
