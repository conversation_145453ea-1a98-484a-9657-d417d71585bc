package com.lx.pl.service;

import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.lx.pl.db.mysql.gen.entity.EventLog;
import com.lx.pl.db.mysql.gen.mapper.EventLogMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
public class EventLogService {

    private static final DateTimeFormatter yearMonthFormatter = DateTimeFormatter.ofPattern("yyyy_M");

    private static final String logicTable = "gpt_event_log_";

    @Autowired
    EventLogMapper eventLogMapper;

    public int addEvent(EventLog eventLog) {
        return eventLogMapper.insert(eventLog);
    }

    public void createNextMonthTable() {
        LocalDate nextMonth = LocalDate.now().plusMonths(1);
        String nextMonthTable = logicTable + nextMonth.format(yearMonthFormatter);

        eventLogMapper.createTable(nextMonthTable); // 调用 Mapper 方法创建新表
        log.info("创建了日志表表：" + nextMonthTable);
    }
}
