package com.lx.pl.service;

import com.lx.pl.db.mysql.community.entity.CommFile;
import com.lx.pl.dto.ScoreFlushParams;
import com.lx.pl.mq.message.CommonMqMessage;
import com.lx.pl.mq.message.RMqMessage;
import com.lx.pl.mq.producer.NormalMessageProducer;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.redis.core.DefaultTypedTuple;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.data.util.CloseableIterator;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;

import static com.lx.pl.service.ScoreSortedQueue.MAX_SIZE;

@Service
@Slf4j
public class FileScoreService {

    private static final Logger logger = LoggerFactory.getLogger(FileScoreService.class);
    public static final String FILE_SCORE_KEY = "file_score";
    private static final int LIKE_WEIGHT = 4;
    private static final int REMIX_WEIGHT = 3;
    private static final int COMMENT_WEIGHT = 1;
    private static final int SHARE_WEIGHT = 2;
    private static final double DECAY_COEFFICIENT = 0.2;
    private static final int DIVERSITY_BONUS = 0;

    @Autowired
    private RedisService<String> redisService;

    @Autowired
    private MongoTemplate mongoTemplate;
    @Resource
    private NormalMessageProducer<ScoreFlushParams> normalMessageProducer;
    @Value("${rocketmq.piclumen.flush.tag:tag_piclumen_flush_test}")
    private String tag;
    @Value("${rocketmq.piclumen.flush.topic:tp_piclumen_flush_test}")
    private String topic;
    @Value("${rocketmq.piclumen.flush.group:gid_piclumen_flush_test}")
    private String group;
    @Resource
    private RedissonClient redissonClient;

    public void initData() {
        long t1 = System.currentTimeMillis();
        ScoreSortedQueue sortedQueue = new ScoreSortedQueue();
        try {
            Aggregation aggregation = Aggregation.newAggregation(
                    Aggregation.project("fileLikeNums", "fileCommentNums", "createTime", "remixNums", "shareNums")
            );

            AtomicInteger i = new AtomicInteger();
            List<Pair<Query, Update>> updates = new ArrayList<>(10000);
            redisService.delete(FILE_SCORE_KEY);
            try (CloseableIterator<CommFile> stream = mongoTemplate.aggregateStream(aggregation, "files", CommFile.class)) {
                stream.forEachRemaining(file -> {
                    i.getAndIncrement();
                    String mongoId = file.getId();
                    long timestamp = file.getCreateTime().toInstant(ZoneOffset.UTC).getEpochSecond();
                    int likes = getValidValue(file.getFileLikeNums());
                    int remixes = getValidValue(file.getRemixNums());
                    int comments = getValidValue(file.getFileCommentNums());
                    int shares = getValidValue(file.getShareNums());

                    int trendingScore = calculateInteractionScore(likes, remixes, comments, shares);
                    String value = mongoId + "_" + timestamp + "_" + trendingScore;
                    double trendingScoreNew = (trendingScore * timeDecayFunction(timestamp)) + DIVERSITY_BONUS;
                    sortedQueue.addScore(value, trendingScoreNew);

                    Query query = new Query(Criteria.where("id").is(mongoId));
                    Update update = new Update()
                            .set("trendingScoreTime", trendingScoreNew)
                            .set("trendingScore", trendingScore);
                    Pair<Query, Update> pair = Pair.of(query, update);
                    updates.add(pair);
                    if (updates.size() == 5000) {
                        mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, CommFile.class).updateMulti(updates).execute();
                        logger.info("更新 mongo 完成 {} 条数据", i);
                        updates.clear();
                    }
                });
            }

            if (!updates.isEmpty()) {
                mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, CommFile.class).updateMulti(updates).execute();
                logger.info("更新 mongo all end  {} 条数据", i);
                updates.clear();
            }
            if (!sortedQueue.isEmpty()) {
                redisService.zadd(FILE_SCORE_KEY, sortedQueue);
                logger.info("已处理数据大小: {}", redisService.zcard(FILE_SCORE_KEY));
            }
            logger.info("更新 Redis 完成，耗时: {} 秒", (System.currentTimeMillis() - t1) / 1000.0);
            logger.info("理数据大小after: {}", redisService.zcard(FILE_SCORE_KEY));
        } catch (Exception e) {
            logger.error("更新 Redis 失败: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            sortedQueue.clear();
        }
    }

    private int getValidValue(Integer value) {
        if (value == null) {
            return 0;
        }
        return value;
    }

    public int calculateInteractionScore(Integer likes, Integer remixes, Integer comments, Integer shares) {
        if (likes == null) {
            likes = 0;
        }
        if (remixes == null) {
            remixes = 0;
        }
        if (comments == null) {
            comments = 0;
        }
        if (shares == null) {
            shares = 0;
        }
        return (likes * LIKE_WEIGHT +
                remixes * REMIX_WEIGHT +
                comments * COMMENT_WEIGHT +
                shares * SHARE_WEIGHT);
    }

    private double timeDecayFunction(long timestamp) {
        try {
            long currentTime = Instant.now().toEpochMilli() / 1000;
            double timeDiff = (currentTime - timestamp) / 1000.0 / (24 * 3600); // 时间差（天）
            return Math.exp(-DECAY_COEFFICIENT * timeDiff); // 指数衰减公式
        } catch (Exception e) {
            logger.error("时间格式转换失败: {}", e.getMessage());
            return 1.0; // 默认返回 1.0，表示无衰减
        }
    }

    private double calculateTrendingScore(long interactionScore, long timestamp) {
        return (interactionScore * timeDecayFunction(timestamp)) + DIVERSITY_BONUS;
    }


    /**
     * 更新单个文件的 Trending Score
     *
     * @param file
     */
    public void updateSingleFileTrendingScore(CommFile file, int srcScore) {
        String mongoId = file.getId();
        long timestamp = file.getCreateTime().toInstant(ZoneOffset.UTC).getEpochSecond();
        int likes = getValidValue(file.getFileLikeNums());
        int remixes = getValidValue(file.getRemixNums());
        int comments = getValidValue(file.getFileCommentNums());
        int shares = getValidValue(file.getShareNums());
        int interactionScore = calculateInteractionScore(likes, remixes, comments, shares);
        file.setTrendingScore(interactionScore);
        String valueNew = mongoId + "_" + timestamp + "_" + interactionScore;
        double trendingScore = calculateTrendingScore(interactionScore, timestamp);
        file.setTrendingScoreTime(trendingScore);
        logger.info("更新单个文件的 Trending Score new: {}", valueNew);
//        redisService.zadd(FILE_SCORE_KEY, trendingScore, valueNew);
        boolean notEqOld = !valueNew.equals(mongoId + "_" + timestamp + "_" + srcScore);
        redisService.addWithLimit(FILE_SCORE_KEY, trendingScore, valueNew, MAX_SIZE, notEqOld ? (mongoId + "_" + timestamp + "_" + srcScore) : "0");
//
        if (notEqOld) {
            logger.info("更新单个文件的 Trending Score old: {}", mongoId + "_" + timestamp + "_" + srcScore);
        }
//        this.maintainZsetSize(FILE_SCORE_KEY, MAX_SIZE);
    }

    /**
     * 定时任务更新redis的文件分数
     */
    public void updateFileScore() {
        try {
            int offset = 0;
            long t1 = System.currentTimeMillis();
            Set<ZSetOperations.TypedTuple<String>> tuples = new HashSet<>();
            Set<ZSetOperations.TypedTuple<String>> batch = redisService.zrevrangeWithScores(FILE_SCORE_KEY, offset, -1);
            for (ZSetOperations.TypedTuple<String> tuple : batch) {
                String value = tuple.getValue();
                if (value == null) {
                    logger.warn("无效的 value 格式: {}", value);
                    continue;
                }
                String[] parts = value.split("_");
                if (parts.length != 3) {
                    logger.warn("无效的 value 格式: {}", value);
                    continue;
                }

                String mongoId = parts[0];
                long timestamp = Long.parseLong(parts[1]);
                double oldTrendingScore = Double.parseDouble(parts[2]);
                double newTrendingScore = (oldTrendingScore * timeDecayFunction(timestamp)) + DIVERSITY_BONUS;

                // 构建新的 value
                String newValue = mongoId + "_" + timestamp + "_" + (int) oldTrendingScore;

                tuples.add(new DefaultTypedTuple<>(newValue, newTrendingScore));
                // 更新 Redis
            }
            redisService.stringRedisTemplate.delete(FILE_SCORE_KEY);
            redisService.zadd(FILE_SCORE_KEY, tuples);
            logger.info("更新 Redis 完成，耗时: {} 秒", (System.currentTimeMillis() - t1) / 1000.0);
        } catch (Exception e) {
            logger.error("更新 Redis 失败: {}", e.getMessage());
            throw new RuntimeException(e);
        }
    }

    /**
     * 维护 ZSET 的大小
     *
     * @param ZSET_KEY
     * @param maxSize
     */
    public void maintainZsetSize(String ZSET_KEY, int maxSize) {
        long currentSize = redisService.zcard(ZSET_KEY);
        if (currentSize > maxSize) {
            redisService.zremrangeByRank(ZSET_KEY, 0, 0);
        }
    }

    public void sendFlushMessage(String commFileId, Integer srcScore) {
        if (srcScore == null) {
            srcScore = 0;
        }
        ScoreFlushParams scoreFlushParams = new ScoreFlushParams(commFileId, srcScore);
        CommonMqMessage<ScoreFlushParams> mqMessage = new RMqMessage<>(topic, tag, commFileId);
        mqMessage.setMessage(scoreFlushParams);
        normalMessageProducer.syncSend(mqMessage);
    }


    public void flushScore(ScoreFlushParams callBackParam) {
        String commFileId = callBackParam.getCommFileId();
        if (callBackParam.getInteractionScore() == null) {
            log.info("flushScore: {} {}", commFileId, "interactionScore is null");
            return;
        }
        RLock lock = redissonClient.getLock("file:flush:" + commFileId);
        try {
            lock.lock();
            CommFile commFile = getCommFileById(commFileId);
            if (commFile != null) {
                this.updateSingleFileTrendingScore(commFile, callBackParam.getInteractionScore());
                mongoTemplate.updateFirst(
                        new Query(Criteria.where("id").is(commFileId)),
                        new Update().set("trendingScoreTime", commFile.getTrendingScoreTime())
                                .set("trendingScore", commFile.getTrendingScore()),
                        CommFile.class
                );
            }
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

    }


    /**
     * 查询社区图片
     *
     * @param commFileId
     * @return
     */
    private CommFile getCommFileById(String commFileId) {
        try {
            Query query = new Query();
            query.addCriteria(Criteria.where("id").is(commFileId));
            CommFile commFile = mongoTemplate.findOne(query, CommFile.class);
            return commFile;
        } catch (Exception e) {
            log.error("查询社区图片报错，图片id为：{}", commFileId, e);
            return null;
        }
    }
}
