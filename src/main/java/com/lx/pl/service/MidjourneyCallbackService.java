package com.lx.pl.service;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.lx.pl.db.mysql.gen.entity.PromptFile;
import com.lx.pl.db.mysql.gen.entity.PromptRecord;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.db.mysql.gen.mapper.PromptFileMapper;
import com.lx.pl.db.mysql.gen.mapper.PromptRecordMapper;
import com.lx.pl.db.mysql.gen.mapper.UserMapper;
import com.lx.pl.dto.midjourney.MidjourneyResponse;
import com.lx.pl.enums.MidjourneyTaskStatus;
import com.lx.pl.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.concurrent.TimeUnit;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Midjourney回调处理服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class MidjourneyCallbackService {

    @Autowired
    private RedisService redisService;

    @Autowired
    private PromptRecordMapper promptRecordMapper;

    @Autowired
    private PromptFileMapper promptFileMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private WebSocketServer webSocketServer;

    @Autowired
    private VipService vipService;

    @Autowired
    private MidjourneyService midjourneyService;

    private static final String MIDJOURNEY_TASK_PREFIX = "midjourney:task:";
    private static final String MIDJOURNEY_USER_TASK_PREFIX = "midjourney:user:";
    private static final String MIDJOURNEY_USER_CONCURRENT_PREFIX = "midjourney:concurrent:";
    private static final String USER_TODAY_CREATE_IMG_NUMS = "user_today_create_img_nums";
    private static final String USER_TASK_TIMESTAMP = "USER_TASK_TIMESTAMP";
    private static final String MJ_CALLBACK_IDEMPOTENT_PREFIX = "mj:callback:idempotent:";

    /**
     * 处理Midjourney回调
     */
    public void handleCallback(MidjourneyResponse.TaskStatusResponse callbackData) {
        String jobId = callbackData.getJobId();
        String status = callbackData.getStatus();

        log.info("Processing Midjourney callback for jobId: {}, status: {}", jobId, status);

        // 幂等性检查 - 防止重复处理同一个回调
        String idempotentKey = MJ_CALLBACK_IDEMPOTENT_PREFIX + jobId + "_" + status;
        if (redisService.hasKey(idempotentKey)) {
            log.debug("Callback already processed for jobId: {}, status: {}", jobId, status);
            return;
        }

        // 设置幂等性标记，有效期10分钟
        redisService.stringSet(idempotentKey, "1", 10, TimeUnit.MINUTES);

        // 获取任务信息
        String taskKey = MIDJOURNEY_TASK_PREFIX + jobId;
        String loginName = (String) redisService.getDataFromHash(taskKey, "loginName");

        if (StringUtil.isBlank(loginName)) {
            // 尝试从jobId -> markId -> loginName的映射中获取
            String markId = redisService.stringGet(jobId);
            if (StringUtil.isNotBlank(markId)) {
                loginName = redisService.stringGet(markId);
            }

            if (StringUtil.isBlank(loginName)) {
                log.warn("Task not found in redis for jobId: {}", jobId);
                return;
            }
        }

        MidjourneyTaskStatus taskStatus = MidjourneyTaskStatus.fromStatus(status);

        // 根据状态处理，遵循startTaskStatusPolling的模式
        switch (taskStatus) {
            case SUCCESS:
                midjourneyService.handleTaskSuccess(jobId, loginName, callbackData);
                break;
            case FAILED:
                midjourneyService.handleTaskFailure(jobId, loginName, callbackData);
                break;
            case PENDING_QUEUE:
                // 更新Redis状态为排队中，遵循startTaskStatusPolling的模式
                updateMjTaskStatusInRedis(jobId, 1);
                log.debug("Task in queue - jobId: {}, status: {}", jobId, status);
                break;
            case ON_QUEUE:
                // 更新Redis状态为执行中，遵循startTaskStatusPolling的模式
                updateMjTaskStatusInRedis(jobId, 0);
                log.debug("Task running - jobId: {}, status: {}", jobId, status);
                break;
            default:
                log.warn("Unknown task status: {}", status);
        }
    }

    /**
     * 更新MJ任务在Redis中的状态（遵循startTaskStatusPolling的模式）
     */
    private void updateMjTaskStatusInRedis(String jobId, Integer index) {
        try {
            // 获取markId
            String markId = redisService.stringGet(jobId);
            if (StringUtil.isBlank(markId)) {
                log.warn("Cannot find markId for jobId: {}", jobId);
                return;
            }

            // 获取loginName
            String loginName = redisService.stringGet(markId);
            if (StringUtil.isBlank(loginName)) {
                log.warn("Cannot find loginName for markId: {}", markId);
                return;
            }

            // 更新用户hash中的任务状态
            redisService.putDataToHash(loginName, markId, index);

            log.debug("Updated MJ task status in Redis - jobId: {}, markId: {}, index: {}", jobId, markId, index);

        } catch (Exception e) {
            log.error("Update MJ task status in Redis error", e);
        }
    }

    /**
     * 更新任务记录
     */
    private void updatePromptRecord(String jobId, String loginName, MidjourneyResponse.TaskData taskData) {
        LambdaUpdateWrapper<PromptRecord> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(PromptRecord::getPromptId, jobId)
                .eq(PromptRecord::getLoginName, loginName)
                .set(PromptRecord::getGenEndTime, LocalDateTime.now())
                .set(PromptRecord::getUpdateTime, LocalDateTime.now());

        promptRecordMapper.update(null, updateWrapper);
    }

    /**
     * 处理图像数据
     */
    private void processImages(String jobId, String loginName, MidjourneyResponse.TaskData taskData) {
        List<String> images = taskData.getImages();

        // 更新用户当日生图数量
        Integer userTodayCreateImgNums = (Integer) redisService.getDataFromHash(USER_TODAY_CREATE_IMG_NUMS, loginName);
        userTodayCreateImgNums = userTodayCreateImgNums != null ? userTodayCreateImgNums : 0;
        redisService.putDataToHash(USER_TODAY_CREATE_IMG_NUMS, loginName, userTodayCreateImgNums + images.size());

        // 保存图像文件信息
        for (String imageUrl : images) {
            PromptFile promptFile = new PromptFile();
            promptFile.setLoginName(loginName);
            promptFile.setPromptId(jobId);
            promptFile.setFileUrl(imageUrl);
            promptFile.setThumbnailUrl(imageUrl); // Midjourney返回的就是处理好的图片
            promptFile.setWidth(taskData.getWidth());
            promptFile.setHeight(taskData.getHeight());
            promptFile.setCreateTime(LocalDateTime.now());
            promptFile.setCreateBy(loginName);

            promptFileMapper.insert(promptFile);
        }

        // 更新用户总图片数量
        LambdaUpdateWrapper<User> userUpdateWrapper = new LambdaUpdateWrapper<>();
        userUpdateWrapper.eq(User::getLoginName, loginName)
                .setSql("total_img_num = total_img_num + " + images.size());

        userMapper.update(null, userUpdateWrapper);

        // 更新VIP相关统计
        try {
            vipService.updateMessageByMarkId(jobId, jobId, loginName, images.size(), images.size(), images.size());
        } catch (Exception e) {
//            loadBalanceService.dealFailureTask(mark_id, result.getPrompt_id(), userLoginName, "Task Failed");
            log.error("生图回调信息成功，但解析数据失败，失败报错信息", e);
        }
    }

    /**
     * 发送WebSocket通知
     */
    private void sendWebSocketNotification(String loginName, MidjourneyResponse.TaskStatusResponse callbackData) {
        try {
            String message = JsonUtils.writeToString(callbackData);
            webSocketServer.sendToOne(loginName, message);
        } catch (Exception e) {
            log.error("Error sending websocket notification to user: " + loginName, e);
        }
    }

}
