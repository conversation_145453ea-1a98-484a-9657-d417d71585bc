package com.lx.pl.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lx.pl.db.mysql.gen.entity.ToolRmbgResult;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.dto.ToolRmbgParam;
import com.lx.pl.dto.mq.RmbgVo;
import com.lx.pl.enums.LogicErrorCode;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Stripe 用户客户信息服务接口
 */
public interface ToolRmbgResultService extends IService<ToolRmbgResult> {

    @Transactional(rollbackFor = Exception.class)
    ToolRmbgResult saveRmbgFile(ToolRmbgParam param, User user, String platform);

    void dealRmbgReadyStatus(RmbgVo readyServerVo);

    List<ToolRmbgResult> getRmbgHistory(User user);

    List<ToolRmbgResult> getRmbgResultStatus(List<Long> ids);

    void deleteBatchByIds(User user, List<Long> ids);

    LogicErrorCode passVerify(User user, String batchId);
}
