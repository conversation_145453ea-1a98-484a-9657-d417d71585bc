package com.lx.pl.service;

import static com.lx.pl.util.CommonUtils.getMD5;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lx.pl.client.TranslationApi;
import com.lx.pl.db.mysql.gen.entity.OpsInfo;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.db.mysql.gen.mapper.OpsInfoMapper;
import com.lx.pl.dto.ModelInformation;
import com.lx.pl.dto.SuspensionNotice;
import com.lx.pl.dto.TranslationResult;
import com.lx.pl.dto.UserInfo;
import com.lx.pl.dto.baidu.BaiduTrans;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import com.lx.pl.util.JsonUtils;
import com.lx.pl.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.apis.producer.SendReceipt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import retrofit2.Response;

import javax.annotation.Resource;

@Slf4j
@Service
public class CommonService {

 //   private final static  String USERID_FEATURED_PERMISSIONS = "userIdFeaturedPermissions";

    @Resource
    TranslationApi translationApi;


    public static final String BAIDU_TRANSLATE_API = "https://fanyi-api.baidu.com/api/trans/vip/translate";

    public static final String SUSPENSION_Time =  "suspension-time"; // 停服时间

    public static final String SUSPENSION_Time_START =  "suspension-start-time"; // 停服开始时间

    public static final String SUSPENSION_Time_END =  "suspension-end-time"; // 停服结束时间

    @Autowired
    RestTemplate restTemplate;

    @Value("${api.version}")
    String apiVersion;

    @Value("${baidu.translation.appId}")
    private String appId;

    @Value("${baidu.translation.key}")
    private String apiKey;

    @Autowired
    OpsInfoMapper opsInfoMapper;

    @Autowired
    RedisService redisService;

    // @Value("${rocketmq.piclumen.topic}")
    // private String topic;
    // @Value("${rocketmq.piclumen.business-one.tag:test-tag}")
    // private String tag;
    // @Resource
    // private NormalMessageProducer<UserInfo> messageProducer;
    public String version() {
        return apiVersion;
    }

    public String translatePrompt(String text) {
        String result = translate(text, "zh", "en");
        if (result != null) {
            return result;
        }
        return text;
    }

    public String translate(String text, String source, String des) {

        Map<String, String> params = new HashMap<String, String>();
        params.put("q", text);
        params.put("from", source);
        params.put("to", des);
        params.put("appid", appId);
        // 随机数
        String salt = String.valueOf(System.currentTimeMillis());
        params.put("salt", salt);
        // 签名
        String src = appId + text + salt + apiKey; // 加密前的原文
        String md5 = getMD5(src);
        md5 = md5.toLowerCase();
        params.put("sign", md5);
        try {
            String s = get(params);
            log.info("baidu translate: {} -> {}", text, s);
            return s;
        } catch (UnsupportedEncodingException e) {
            return null;
        }
    }

    public String get(Map<String, String> params2)
            throws UnsupportedEncodingException {

        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(params, headers);
        String s = params2.get("q");
        String salt = params2.get("salt");
        String appid = params2.get("appid");
        String to = params2.get("to");
        String from = params2.get("from");
        String sign = params2.get("sign");

        params.add("salt", salt);
        params.add("appid", appid);
        params.add("to", to);
        params.add("from", from);
        params.add("q", s);
        params.add("sign", sign);

        ResponseEntity<String> response = restTemplate.exchange(BAIDU_TRANSLATE_API, HttpMethod.POST,
                requestEntity,
                String.class);

        int statusCode = response.getStatusCodeValue();
        if (statusCode != 200) {
            System.out.println("Http错误码：" + statusCode);
        }

        String resStr = response.getBody();

        ObjectMapper objectMapper = new ObjectMapper();
        try {
            BaiduTrans baiduTrans = objectMapper.readValue(resStr, BaiduTrans.class);
            return baiduTrans.getTrans_result().get(0).getDst();
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 翻译成英文或者增强提示词
     *
     * @param prompt
     * @return
     * @throws JsonProcessingException
     */
    public String translationToEnglishOrEnhance(String prompt, String mode) throws JsonProcessingException, UnsupportedEncodingException {
        String englishPrompt = "";
        if (StringUtil.isNotBlank(prompt)) {

            String today = LocalDate.now().toString();
            // 统计每天的翻译次数
            redisService.incrementFieldInHash("translation_day_nums", today, 1);

            log.debug("翻译接口请求参数: {}", JsonUtils.writeToString(prompt));
            Response<TranslationResult> response = translationApi.translationPrompt(mode, prompt, 0.6F, String.valueOf(Boolean.TRUE), 0.9F);
            log.info("调用翻译接口后端返回信息: {}", JsonUtils.writeToString(response.body()));

            // 获取响应数据
            TranslationResult translationResult = response.body();
            /**
             *  获取调用结果集，调用翻译模型接口 的code 为 0 ，且 result 结果集有值，才算成功
             */
            if (0 == translationResult.getCode() && !Objects.isNull(translationResult.getResult())) {
                TranslationResult.Result result = translationResult.getResult();
                englishPrompt = result.getText();
            }
        }
        return englishPrompt;
    }

    public SuspensionNotice getSuspensionTime(){
        SuspensionNotice suspensionNotice = new SuspensionNotice();
        String startTime =  (String) redisService.getDataFromHash(SUSPENSION_Time, SUSPENSION_Time_START);
        String endTime =  (String) redisService.getDataFromHash(SUSPENSION_Time, SUSPENSION_Time_END);
        suspensionNotice.setStartTime(StringUtils.isNotBlank(startTime) ? startTime : "946684800"); // 如果为空返回 2000       年 1月1日 早上8点
        suspensionNotice.setEndTime(StringUtils.isNotBlank(endTime) ? endTime : "4070912400");// 如果为空返回 2099年 1月1日 早上9点
        return suspensionNotice;
    }
     //   public void testSend() {
    //     UserInfo user = new UserInfo();
    //     user.setUserName("test");
    //     messageProducer.syncSend(user, tag, "123465");
    //   }
    //
    // public void testAsyncSend() {
    //   UserInfo user = new UserInfo();
    //   user.setUserName("test");
    //   CompletableFuture<SendReceipt> future0 = new CompletableFuture<>();
    //   future0.whenCompleteAsync((sendReceipt, throwable) -> {
    //     if (null != throwable) {
    //       log.error("Failed to send message", throwable);
    //       return;
    //     }
    //     log.info("Send message successfully, messageId={}", sendReceipt.getMessageId());
    //   }, MQ_THREAD_POOL);
    //   messageProducer.asyncSend(user, tag, "123465", null);
    // }

//    public Boolean judegeTheFeaturedPermissions(User user){
//        try {
//            Set<Long> userIdFeaturedSet = new HashSet<>();
//            //先查询reids,找不到再查mysql
//            String useridFeatured = (String) redisService.get(USERID_FEATURED_PERMISSIONS);
//            if (StringUtil.isNotBlank(useridFeatured)) {
//                userIdFeaturedSet = Arrays.stream(useridFeatured.split(","))
//                        .map(Long::valueOf) // 转换为 Long 类型
//                        .collect(Collectors.toSet());
//            }else {
//                LambdaQueryWrapper<OpsInfo> lwq = new LambdaQueryWrapper();
//                lwq.eq(OpsInfo::getFeatured, Boolean.TRUE);
//                lwq.isNull(OpsInfo::getUserId);
//                List<OpsInfo> opsInfoList = opsInfoMapper.selectList(lwq);
//                if (!CollectionUtils.isEmpty(opsInfoList)) {
//                    userIdFeaturedSet = opsInfoList.stream()
//                            .map(OpsInfo::getId) // 提取 OpsInfo 对象的 id 属性
//                            .collect(Collectors.toSet());
//                }
//            }
//
//            return userIdFeaturedSet.contains(user.getId());
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//
//        return Boolean.FALSE;
//    }
}
