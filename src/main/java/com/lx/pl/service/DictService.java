package com.lx.pl.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lx.pl.db.mysql.gen.entity.Dict;
import com.lx.pl.db.mysql.gen.mapper.DictMapper;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class DictService {

    @Autowired
    DictMapper dictMapper;

    public int createDict(Dict neDict) {
        return dictMapper.insert(neDict);
    }

    public Dict getDictById(long id) {
        return dictMapper.selectById(id);
    }

    public List<Dict> getDictByKey(String key) {
        QueryWrapper<Dict> wrapper = new QueryWrapper();
        wrapper.eq("config_key", key);
        List<Dict> values = dictMapper.selectList(wrapper);
        return values;
    }

    public List<Dict> listDicts(int pageNum, int pageSize) {
        IPage<Dict> DictIPage = dictMapper.selectPage(new Page<>(pageNum, pageSize), null);
        return DictIPage.getRecords();
    }

    public int updateById(Dict dict) {
        return dictMapper.updateById(dict);
    }

    public int updateByKey(Dict dict) {
        QueryWrapper<Dict> wrapper = new QueryWrapper<>();
        wrapper.eq("config_key", dict.getConfigKey());
        List<Dict> neDicts = dictMapper.selectList(wrapper);
        if (neDicts == null || neDicts.size() == 0) {
            return dictMapper.insert(dict);
        } else if (neDicts.size() > 1) {
            throw new RuntimeException("本条key对应多条数据");
        } else {
            Dict d = neDicts.get(0);
            dict.setId(d.getId());
            return dictMapper.updateById(dict);
        }
    }

    public int deleteById(long id) {
        return dictMapper.deleteById(id);
    }
}
