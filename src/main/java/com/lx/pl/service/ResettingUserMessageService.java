package com.lx.pl.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.db.mysql.gen.mapper.UserMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;

import static com.lx.pl.service.GenService.IP_TODAY_RELAX_CREATE_IMG_NUMS;
import static com.lx.pl.service.GenService.USER_TODAY_CREATE_IMG_NUMS;
import static com.lx.pl.service.ImgService.USER_PUBLIC_IMG_NUMS;
import static com.lx.pl.service.VipService.*;

@Service
public class ResettingUserMessageService {

    private static final Logger logger = LoggerFactory.getLogger("schedule-task");

    @Autowired
    private RedisService redisService;

    @Autowired
    UserMapper userMapper;

    public void resettingUserCreateImgNumTask() {
        logger.info("开始执行零时区零点的定时操作");
        try {
            //删除不要的昨日生图数量
            redisService.unlink(USER_TODAY_CREATE_IMG_NUMS);
            redisService.unlink(USER_TODAY_RELAX_CREATE_IMG_NUMS);
            redisService.unlink(IP_TODAY_RELAX_CREATE_IMG_NUMS);
            logger.info("结束执行零时区零点的定时操作");
        } catch (Exception e) {
            logger.error("执行零时区零点的定时操作失败:", e);
        }
    }

    public void resettingUserPublicImgNum() {
        logger.info("开始执行零时区重置今天公开图片数");
        try {
            //删除不要的昨日公开图片数据
            redisService.unlink(USER_PUBLIC_IMG_NUMS);
            logger.info("结束执行零时区重置今天公开图片数");
        } catch (Exception e) {
            logger.error("执行零时区重置今天公开图片数失败:", e);
        }
    }

    public void resettingUserLumens() {
        logger.info("开始执行零时区重置用用户有效的点数和使用的点数");
        try {
            //删除用户购买的点数
            redisService.unlink(USER_RECHARGE_TOTAL_LUMENS);
            redisService.unlink(USER_RECHARGE_USE_LUMENS);
            //删除用户vip的点数
            redisService.unlink(USER_VIP_TOTAL_LUMENS);
            redisService.unlink(USER_VIP_USE_LUMENS);
            //删除用户赠送点数
            redisService.unlink(USER_GIFT_TOTAL_LUMENS);
            redisService.unlink(USER_GIFT_USE_LUMENS);
            logger.info("结束执行零时区重置用用户有效的点数和使用的点数");
        } catch (Exception e) {
            logger.error("执行零时区重置用用户有效的点数和使用的点数失败:", e);
        }
    }
}
