package com.lx.pl.service;

import com.lx.pl.constant.LogicConstants;
import com.lx.pl.util.IpUtils;
import com.maxmind.geoip2.DatabaseReader;
import com.maxmind.geoip2.model.CountryResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.net.InetAddress;
import java.util.Optional;

@Service
@Slf4j
public class GeoIpCityService {
    @Resource
    private DatabaseReader databaseReaderCity;

    public Optional<String> getCity(String ip) {
        try {
            // 内网不查询
            if (IpUtils.internalIp(ip)) {
                return Optional.of(LogicConstants.INNER_IP);
            }
            InetAddress ipAddress = InetAddress.getByName(ip);
            CountryResponse response = databaseReaderCity.country(ipAddress);
            return Optional.ofNullable(response.getCountry().getName());
        } catch (Exception e) {
            log.error("getCity error", e);
            return Optional.of(LogicConstants.UNKNOWN_IP);
        }
    }
}
