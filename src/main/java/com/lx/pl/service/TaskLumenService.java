package com.lx.pl.service;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.lx.pl.db.mysql.community.entity.AccountInfo;
import com.lx.pl.db.mysql.community.entity.CommUser;
import com.lx.pl.db.mysql.community.entity.DailyTaskLumen;
import com.lx.pl.db.mysql.community.entity.TaskLumen;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.db.mysql.gen.repository.CommImgReportRepository;
import com.lx.pl.db.mysql.gen.repository.DailyTaskLumenRepository;
import com.lx.pl.db.mysql.gen.repository.TaskLumenRepository;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.Collections;
import java.util.HashSet;
import java.util.Objects;

@Service
@Slf4j
public class TaskLumenService {

    @Autowired
    private TaskLumenRepository taskLumenRepository;

    @Autowired
    private DailyTaskLumenRepository dailyTaskLumenRepository;

    @Autowired
    private MongoTemplate mongoTemplate;

    public TaskLumen getTaskLumenByUserId(User user) {
        Query query = new Query();
        query.addCriteria(Criteria.where("userId").is(user.getId()));
        TaskLumen taskLumen = mongoTemplate.findOne(query, TaskLumen.class);

        //如果没有则新建
        if (Objects.isNull(taskLumen)) {
            taskLumen = new TaskLumen();
            taskLumen.setUserId(user.getId());
            taskLumen.setUserLoginName(user.getLoginName());
            taskLumen.setCreationNums(0);
            taskLumen.setLikeNums(0);
            taskLumen.setFollowNums(0);
            taskLumen.setReceiveLikeNums(0);
            taskLumen.setReceiveFollowNums(0);
            taskLumen.setSubscribeYoutube(0);
            taskLumen.setFollowIG(0);
            taskLumen.setFollowX(0);
            taskLumen.setJoinDiscord(0);
            taskLumen.setWriteReview(0);
            taskLumen.setWriteReviewProducthunt(0);
            taskLumen.setFinishTaskIds(new HashSet<>());
            taskLumen.setRewardTaskIds(new HashSet<>());
            addTaskLumen(taskLumen);
        }

        if (taskLumen.getWriteReviewProducthunt() == null) {
            taskLumen.setWriteReviewProducthunt(0);
        }

        return taskLumen;
    }

    public Boolean addTaskLumen(TaskLumen taskLumen) {
        taskLumenRepository.insert(taskLumen);
        return Boolean.TRUE;
    }

    public void saveTaskLumen(TaskLumen taskLumen) {
        taskLumenRepository.save(taskLumen);
    }

    public DailyTaskLumen getDailyTaskLumenByUserId(User user) {
        Query query = new Query();
        query.addCriteria(Criteria.where("userId").is(user.getId()));
        DailyTaskLumen dailyTaskLumen = mongoTemplate.findOne(query, DailyTaskLumen.class);

        // 当天开始时间戳（秒）
        Long nowTimeStartOfDaySecond = LocalDate.now(ZoneOffset.UTC).atStartOfDay(ZoneOffset.UTC).toEpochSecond();

        //如果没有则新建,有则判断是否过期,过期则重置
        if (Objects.isNull(dailyTaskLumen)) {
            dailyTaskLumen = new DailyTaskLumen();
            dailyTaskLumen.setUserId(user.getId());
            dailyTaskLumen.setUserLoginName(user.getLoginName());
            dailyTaskLumen.setSignInNums(1);
            dailyTaskLumen.setDailyLikeNums(0);
            dailyTaskLumen.setDailyShareNums(0);
            dailyTaskLumen.setDailyCommentNums(0);
            dailyTaskLumen.setDailyWatchADNums(0);
            dailyTaskLumen.setDailyLumensTime(nowTimeStartOfDaySecond);
            dailyTaskLumen.setFinishTaskIds(Collections.singleton("1"));
            dailyTaskLumen.setRewardTaskIds(new HashSet<>());
            addDailyTaskLumen(dailyTaskLumen);
        } else if (dailyTaskLumen.getDailyLumensTime() == null
                || !dailyTaskLumen.getDailyLumensTime().equals(nowTimeStartOfDaySecond)) {
            dailyTaskLumen.setSignInNums(1);
            dailyTaskLumen.setDailyLikeNums(0);
            dailyTaskLumen.setDailyShareNums(0);
            dailyTaskLumen.setDailyCommentNums(0);
            dailyTaskLumen.setDailyWatchADNums(0);
            dailyTaskLumen.setDailyLumensTime(nowTimeStartOfDaySecond);
            dailyTaskLumen.setFinishTaskIds(Collections.singleton("1"));
            dailyTaskLumen.setRewardTaskIds(new HashSet<>());
            dailyTaskLumenRepository.save(dailyTaskLumen);
        }

        if (dailyTaskLumen.getDailyWatchADNums() == null) {
            dailyTaskLumen.setDailyWatchADNums(0);
        }

        return dailyTaskLumen;
    }

    public Boolean addDailyTaskLumen(DailyTaskLumen dailyTaskLumen) {
        dailyTaskLumenRepository.insert(dailyTaskLumen);
        return Boolean.TRUE;
    }

    public void saveDailyTaskLumen(DailyTaskLumen dailyTaskLumen) {
        dailyTaskLumenRepository.save(dailyTaskLumen);
    }
}
