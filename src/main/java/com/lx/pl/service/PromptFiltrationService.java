package com.lx.pl.service;

import com.lx.pl.constant.LogicConstants;
import com.lx.pl.util.DateUtils;
import com.lx.pl.util.PromptFiltrationUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @program: core
 * @desciption:
 * @date 2024/12/14 14:30
 */
@Service
public class PromptFiltrationService {

    @Resource
    RedisService redisService;

    public Boolean filterChildSex(String prompt) {
        Boolean b1 = filterAdultBadWords(prompt);
        if (b1) {
            return true;
        }

        Boolean b = PromptFiltrationUtil.filterChildSex(prompt);
        if (b) {
            redisService.incrementHashValue(LogicConstants.CHILD_PRON_COUNT, LocalDate.now().toString(), 1);
        }
        return b;
    }

    public Boolean filterAdultBadWords(String prompt) {
        Boolean b = PromptFiltrationUtil.filterAdultBadWords(prompt);
        if (b) {
            redisService.incrementHashValue(LogicConstants.ADULT_PRON_COUNT, LocalDate.now().toString(), 1);
        }
        return b;
    }

    public String filterAndRemoveBadWords(String prompt) {
        return PromptFiltrationUtil.filterAndRemoveBadWords(prompt);
    }

    public Boolean filterBadWords(String prompt) {
        return PromptFiltrationUtil.filterBadWords(prompt);
    }
}
