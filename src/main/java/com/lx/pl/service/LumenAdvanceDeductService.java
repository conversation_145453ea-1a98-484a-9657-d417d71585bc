package com.lx.pl.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lx.pl.db.mysql.gen.entity.PromptRecord;
import com.lx.pl.db.mysql.gen.mapper.PromptRecordMapper;
import com.lx.pl.dto.GenGenericPara;
import com.lx.pl.dto.GenGenericResp;
import com.lx.pl.dto.Resolution;
import com.lx.pl.enums.OriginCreate;
import com.lx.pl.enums.PublicType;
import com.lx.pl.util.DoubleMathUtils;
import com.lx.pl.util.JsonUtils;
import com.lx.pl.util.LogicUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

import static com.lx.pl.service.VipService.USER_NOT_FINISH_TASK_LUMENS;

/**
 * <AUTHOR>
 * @date 2025/6/5
 * @description lumen预扣处理Service
 */
@Slf4j
@Service
public class LumenAdvanceDeductService {
    @Resource
    private RedisService redisService;
    @Resource
    private PromptRecordMapper promptRecordMapper;
    @Value("${fluxdev.modelId}")
    private String fluxdevModelId;

    public List<GenGenericResp> notFinishTask(String loginName) {
        List<GenGenericResp> respList = new ArrayList<>();
        try {
            int lumenCount = 0;
            //重置notFinishTask点数
            redisService.deleteFieldFromHash(USER_NOT_FINISH_TASK_LUMENS, loginName);
            //获取用户未完成的任务信息
            List<String> userNotFinishTaskList = redisService.getAllKeysFromHash(loginName);
            //如果当前用户没有未完成任务，则直接返回
            if (CollectionUtils.isEmpty(userNotFinishTaskList)) {
                return respList;
            }

            /**
             * 对taskId数据进行相关解析
             */
            HashMap<String, Integer> notFinishTaskMap = new HashMap<>();
            List<String> markIds = new ArrayList<>();
            for (String userNotFinishTask : userNotFinishTaskList) {
                //截取用户的promptId
                Integer notFinishTaskValue = (Integer) redisService.getDataFromHash(loginName, userNotFinishTask);
                markIds.add(userNotFinishTask);
                //为空的值不存入redis
                if (!Objects.isNull(notFinishTaskValue)) {
                    notFinishTaskMap.put(userNotFinishTask, notFinishTaskValue);
                }
            }

            /**
             * 根据taskId去数据库查询相关的数据
             */
            LambdaQueryWrapper<PromptRecord> qw = new LambdaQueryWrapper<PromptRecord>();
            qw.in(PromptRecord::getMarkId, markIds);
            qw.eq(PromptRecord::getLoginName, loginName);
            qw.eq(PromptRecord::getDel, Boolean.FALSE);
            List<PromptRecord> promptRecordList = promptRecordMapper.selectList(qw);

            /**
             * 对结果集进行标准化处理，返回给前端
             */
            if (!CollectionUtils.isEmpty(promptRecordList)) {
                for (PromptRecord promptRecord : promptRecordList) {
                    GenGenericResp resp = new GenGenericResp();
                    GenGenericPara genGenericPara = JsonUtils.fromJsonNode(promptRecord.getGenInfo(), GenGenericPara.class);
                    //生图或者去背景的批量生图数改为1
                    if (!OriginCreate.create.getValue().equals(promptRecord.getOriginCreate()) &&
                            !OriginCreate.picCreate.getValue().equals(promptRecord.getOriginCreate())) {
                        Resolution resolution = new Resolution();
                        BeanUtils.copyProperties(genGenericPara.getResolution(), resolution);
                        resolution.setBatch_size(1);
                        genGenericPara.setResolution(resolution);
                    }
                    BeanUtils.copyProperties(genGenericPara, resp);
                    resp.setPromptId(promptRecord.getPromptId());
                    resp.setTaskId(promptRecord.getTaskId());
                    resp.setNumber(promptRecord.getTaskNumber());
                    resp.setMarkId(promptRecord.getMarkId());
                    resp.setIndex(notFinishTaskMap.containsKey(promptRecord.getMarkId()) ? notFinishTaskMap.get(promptRecord.getMarkId()) : -1);
                    resp.setCreateTimestamp(promptRecord.getCreateTime());
                    resp.setOriginCreate(promptRecord.getOriginCreate());
                    resp.setIsPublic(PublicType.undisclosed.getValue());
                    resp.setFastHour(promptRecord.getFastHour());
                    resp.setFeatureName(promptRecord.getFeatureName());
                    respList.add(resp);

                    //判断fastHour的lumen点数,且任务在当前一个小时之内
                    if (promptRecord.getFastHour() && (promptRecord.getCreateTime().isAfter(LocalDateTime.now().minusHours(1)))) {
                        int taskCostLumen;
                        int batchSizeNum = genGenericPara.getResolution().getBatch_size();
                        //去背景和扩图为1点
                        if (Objects.equals(OriginCreate.removeBackground.getValue(), promptRecord.getOriginCreate())) {
                            taskCostLumen = batchSizeNum;
                        } else if (Objects.equals(OriginCreate.hiresFix.getValue(), promptRecord.getOriginCreate())) {
                            //超分按像素计算
                            double scale = promptRecord.getPromptParams().get("scale_by").asDouble();
                            taskCostLumen = LogicUtil.calculateCostLumenByPixel((int) DoubleMathUtils.mul(genGenericPara.getResolution().getWidth(), scale),
                                    (int) DoubleMathUtils.mul(genGenericPara.getResolution().getHeight(), scale));
                            if (Objects.equals(fluxdevModelId, genGenericPara.getModel_id())) {
                                taskCostLumen *= 12;
                            }
                        } else {
                            //其余按张数计算
                            if (fluxdevModelId.equals(genGenericPara.getModel_id())) {
                                taskCostLumen = 12 * batchSizeNum;
                            } else if (!Objects.isNull(genGenericPara.getHighPixels()) && genGenericPara.getHighPixels()) {
                                taskCostLumen = 10 * batchSizeNum;
                            } else {
                                taskCostLumen = batchSizeNum;
                            }
                        }
                        lumenCount += taskCostLumen;
                        resp.setCostLumen(taskCostLumen);
                    }
                }

                //notFinishTask 更新redis中未完成的任务点数
                redisService.incrementFieldInHash(USER_NOT_FINISH_TASK_LUMENS, loginName, lumenCount);
            }
        } catch (Exception e) {
            log.error("获取用户未完成的任务及更新lumen预扣点数异常, loginName: {}", loginName, e);
        }

        return respList;
    }
}
