package com.lx.pl.service.message;

import com.lx.pl.config.PushNotifyProperties;
import com.lx.pl.dto.message.SendNotificationCountDTO;
import com.lx.pl.dto.message.SendNotificationDTO;
import com.turo.pushy.apns.ApnsClient;
import com.turo.pushy.apns.DeliveryPriority;
import com.turo.pushy.apns.PushNotificationResponse;
import com.turo.pushy.apns.PushType;
import com.turo.pushy.apns.util.ApnsPayloadBuilder;
import com.turo.pushy.apns.util.SimpleApnsPushNotification;
import com.turo.pushy.apns.util.TokenUtil;
import io.netty.util.concurrent.Future;
import io.netty.util.concurrent.GenericFutureListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.stream.Collectors;


@Slf4j
@Component
public class ApnsService implements ISendNotification {
    @Resource
    private ApnsClient apnsClient;

    @Resource
    private PushNotifyProperties pushNotifyProperties;


    /**
     * 发送通知
     *
     * @param sendNotificationDTO sendNotificationDTO
     */
    public void sendNotification(SendNotificationDTO sendNotificationDTO, SendNotificationCountDTO sendNotificationCountDTO) {

        sendNotificationDTO.getTargetTokenList().stream().collect(Collectors.groupingBy(s -> sendNotificationDTO.getTargetTokenList().indexOf(s) / 50)).values().forEach(batch -> {

            ApnsPayloadBuilder payloadBuilder = new ApnsPayloadBuilder();
            payloadBuilder.setAlertTitle(sendNotificationDTO.getTitle());
            payloadBuilder.setAlertBody(sendNotificationDTO.getBody());
            payloadBuilder.setSound("default");


            String payload = payloadBuilder.buildWithDefaultMaximumLength();
            DeliveryPriority priority = DeliveryPriority.IMMEDIATE;
            PushType pushType = PushType.ALERT;
            Date invalidationTime = new Date(System.currentTimeMillis() + 1000 * 60 * 60 * 24);


            for (String deviceToken : batch) {
                if (StringUtils.isBlank(deviceToken)) {
                    continue;
                }
                String token = TokenUtil.sanitizeTokenString(deviceToken);
                SimpleApnsPushNotification pushNotification = new SimpleApnsPushNotification(token, pushNotifyProperties.getApnsAppBundledId(), payload, invalidationTime, priority, pushType);
                Future<PushNotificationResponse<SimpleApnsPushNotification>> sendNotificationFuture =
                        apnsClient.sendNotification(pushNotification);


                sendNotificationFuture.addListener(new GenericFutureListener<>() {
                    @Override
                    public void operationComplete(Future<? super PushNotificationResponse<SimpleApnsPushNotification>> future) throws Exception {
                        if (future.isSuccess()) {
                            PushNotificationResponse<SimpleApnsPushNotification> response = (PushNotificationResponse<SimpleApnsPushNotification>) future.getNow();
                            if (!response.isAccepted()) {
                                log.error("apns send reject, deviceToken: {}, response: {}", deviceToken, response);
                            }
                        } else {
                            log.error("apns send error, deviceToken: {}", deviceToken);
                        }
                    }
                });
            }
        });
    }

}
