package com.lx.pl.service.message;


import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiRobotSendRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.concurrent.CompletableFuture;


@Service
@Slf4j
public class DingTalkAlert {

    @Value("${spring.profiles.active:default}")
    private String env;


    @Resource
    private ThreadPoolTaskExecutor dingTalkAlertExecutor;


    public static final String PUSH_ALARM_TOKEN = "dfc4765466c009012eb929448aadbd9bec1b14c892a0ce2125df97bd94418a18";
    public static final String COMMON_BUSINESS_ALARM_TOKEN = "4d12156fe8b4438e8d862a8b688813c471e710010bea0da5c2ba4cebb9d15502";

    //告警必须包含此前缀才能发送
    private static final String PUSH_ALARM_PREFIX = "PUSH推送告警";
    private static final String COMMON_BUSINESS_ALARM_PREFIX = "Piclumen业务告警";


    /**
     * 默认异步发送， 机器人每分钟最多发送20条消息 超过20条 限流10分钟
     *
     * @param alarmMessage 告警消息
     */
    public void sendPushAlarm(String alarmMessage) {
        executeSendAlarm(PUSH_ALARM_TOKEN, PUSH_ALARM_PREFIX, alarmMessage);
    }

    /**
     * 默认异步发送， 机器人每分钟最多发送20条消息 超过20条 限流10分钟
     *
     * @param alarmMessage 告警消息
     */
    public void sendCommonBusinessAlarm(String alarmMessage) {
        executeSendAlarm(COMMON_BUSINESS_ALARM_TOKEN, COMMON_BUSINESS_ALARM_PREFIX, alarmMessage);
    }

    private void executeSendAlarm(String token, String prefix, String alarmMessage) {
        CompletableFuture.runAsync(() -> {
            try {
                //sign字段和timestamp字段必须拼接到请求URL上，否则会出现 310000 的错误信息 -> 我就没设置过加密的方式
                DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/robot/send");
                OapiRobotSendRequest req = new OapiRobotSendRequest();
                //定义文本内容
                OapiRobotSendRequest.Text text = new OapiRobotSendRequest.Text();
                text.setContent(prefix + "[" + env + "]: " + alarmMessage);
                //设置消息类型
                req.setMsgtype("text");
                req.setText(text);
                client.execute(req, token);
            } catch (Exception e) {
                log.error("DingTalk send common business alarm error", e);
            }
        }, dingTalkAlertExecutor);
    }
}
