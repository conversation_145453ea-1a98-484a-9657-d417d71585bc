package com.lx.pl.service.message;

import cn.hutool.core.util.NumberUtil;
import com.lx.pl.constant.LogicConstants;
import com.lx.pl.db.mysql.message.entity.PushMessage;
import com.lx.pl.db.mysql.message.mapper.PushMessageMapper;
import com.lx.pl.service.RedisService;
import com.lx.pl.vo.PushMessageReportReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class PushNotifyService {
    @Resource
    private PushMessageMapper pushMessageMapper;
    @Resource
    private RedisService redisService;

    /**
     * 处理消息打开上报
     */
    public void pushOpenReport(PushMessageReportReq req) {
        Long messageId = null;
        if (NumberUtil.isNumber(req.getMessageId())) {
            messageId = Long.valueOf(req.getMessageId());
        }
        if (messageId == null) {
            log.warn("PUSH打开消息上报失败, 未知的messageId: {}", messageId);
            return;
        }
        PushMessage pushMessage = pushMessageMapper.selectById(messageId);
        if (pushMessage == null) {
            log.warn("PUSH打开消息上报失败, 未知的messageId: {}", messageId);
            return;
        }
        String uvKey = LogicConstants.PUSH_OPEN_KEY_PREFIX + messageId;
        boolean newKey = redisService.hasKey(uvKey);
        // 增加PUSH消息打开数
        redisService.increment(uvKey);
        if (newKey) {
            // 新UV key设置过期时间
            redisService.expire(uvKey, LogicConstants.PUSH_OPEN_EXPIRED_TIME, TimeUnit.HOURS);
        }
    }
}
