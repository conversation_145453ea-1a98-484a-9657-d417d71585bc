//package com.lx.pl.service.message;
//
//import com.fasterxml.jackson.databind.JsonNode;
//import com.google.auth.oauth2.GoogleCredentials;
//import com.google.gson.JsonObject;
//import com.lx.pl.config.PushNotifyProperties;
//import com.lx.pl.dto.message.SendNotificationCountDTO;
//import com.lx.pl.dto.message.SendNotificationDTO;
//import com.lx.pl.enums.PushTypeEnum;
//import com.lx.pl.util.JsonUtils;
//import lombok.extern.slf4j.Slf4j;
//import okhttp3.*;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.core.io.ClassPathResource;
//import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.PostConstruct;
//import javax.annotation.Resource;
//import java.io.IOException;
//import java.util.Arrays;
//import java.util.Collections;
//import java.util.List;
//import java.util.Objects;
//import java.util.concurrent.CompletableFuture;
//import java.util.concurrent.TimeUnit;
//import java.util.stream.Collectors;
//
//@Component
//@Slf4j
//public class FcmAndroidService implements ISendNotification {
//
//    private final OkHttpClient fcmClient = new OkHttpClient.Builder()
//            .protocols(Arrays.asList(Protocol.HTTP_2, Protocol.HTTP_1_1))
//            .connectionPool(new ConnectionPool(32, 5, TimeUnit.MINUTES))
//            .connectTimeout(30, TimeUnit.SECONDS)
//            .build();
//
//    @Resource
//    private PushNotifyProperties pushNotifyProperties;
//    @Resource
//    private ThreadPoolTaskExecutor sendNotificationTask;
//
//    private String fcmAndroidAccessToken;
//    private GoogleCredentials googleCredentials;
//    private static final long TOKEN_REFRESH_INTERVAL = 5 * 60 * 1000; // 5分钟刷新一次
//    private long lastTokenRefreshTime;
//
//    @PostConstruct
//    public void init() {
//        if (pushNotifyProperties == null) {
//            throw new IllegalStateException("PushNotifyProperties is not configured");
//        }
//
//        if (StringUtils.isBlank(pushNotifyProperties.getFcmAndroidFilePath())) {
//            throw new IllegalStateException("FCM Android file path is not configured");
//        }
//
//        if (StringUtils.isBlank(pushNotifyProperties.getFcmMessagingScope())) {
//            throw new IllegalStateException("FCM messaging scope is not configured");
//        }
//
//        org.springframework.core.io.Resource resource = new ClassPathResource(pushNotifyProperties.getFcmAndroidFilePath());
//        if (!resource.exists()) {
//            throw new IllegalStateException("FCM Android credentials file not found: " + pushNotifyProperties.getFcmAndroidFilePath());
//        }
//
//        try (var inputStream = resource.getInputStream()) {
//            googleCredentials = GoogleCredentials
//                    .fromStream(inputStream)
//                    .createScoped(Collections.singleton(pushNotifyProperties.getFcmMessagingScope()));
//
//            refreshToken();
//            log.info("FCM Android service initialized successfully");
//        } catch (IOException e) {
//            log.error("Failed to initialize FCM Android service", e);
//            throw new IllegalStateException("Failed to initialize FCM Android service: " + e.getMessage(), e);
//        }
//    }
//
//    private synchronized void refreshToken() {
//        try {
//            googleCredentials.refreshIfExpired();
//            fcmAndroidAccessToken = googleCredentials.getAccessToken().getTokenValue();
//            lastTokenRefreshTime = System.currentTimeMillis();
//
//            if (StringUtils.isEmpty(fcmAndroidAccessToken)) {
//                throw new IllegalStateException("Failed to obtain FCM Android access token");
//            }
//
//            log.debug("FCM Android access token refreshed successfully");
//        } catch (IOException e) {
//            log.error("Failed to refresh FCM Android access token", e);
//            throw new IllegalStateException("Failed to refresh FCM Android access token: " + e.getMessage(), e);
//        }
//    }
//
//    private synchronized String getAccessToken() {
//        // 如果距离上次刷新超过29分钟，则刷新token
//        if (System.currentTimeMillis() - lastTokenRefreshTime > TOKEN_REFRESH_INTERVAL) {
//            refreshToken();
//        }
//        return fcmAndroidAccessToken;
//    }
//
//    /**
//     * 发送推送通知
//     *
//     * @param sendNotificationDTO      推送通知数据传输对象
//     * @param sendNotificationCountDTO 推送通知计数数据传输对象
//     */
//    @Override
//    public void sendNotification(SendNotificationDTO sendNotificationDTO, SendNotificationCountDTO sendNotificationCountDTO) {
//        // 安卓没有视频相关功能 所以如果消息深度链接对应的是视频 安卓直接不发消息
//        if (Objects.nonNull(sendNotificationDTO.getSchemeEnum())
//                && Objects.equals(sendNotificationDTO.getSchemeEnum().getType(), "video")) {
//            return;
//        }
//
//
//        // 批量处理推送请求
//        List<Request> requests = sendNotificationDTO.getTargetTokenList().stream()
//                .filter(StringUtils::isNotEmpty)
//                .map(token -> {
//                    JsonObject message = buildMessage(token, sendNotificationDTO);
//                    return new Request.Builder()
//                            .url(pushNotifyProperties.getFcmAndroidEndPoint())
//                            .headers(Headers.of("Authorization", "Bearer " + getAccessToken()))
//                            .post(RequestBody.create(
//                                    message.toString(),
//                                    MediaType.get("application/json; charset=utf-8")
//                            ))
//                            .build();
//                })
//                .collect(Collectors.toList());
//
//        // 使用HTTP/2多路复用并发发送请求
//        CompletableFuture.allOf(
//                requests.stream()
//                        .map(request -> CompletableFuture.runAsync(() -> {
//                            try (Response response = fcmClient.newCall(request).execute()) {
//                                if (response.body() != null) {
//                                    JsonNode resp = JsonUtils.writeStringToJsonNode(response.body().string());
//                                    handleResponse(resp, sendNotificationCountDTO);
//                                }
//                            } catch (IOException e) {
//                                log.error("FCM push failed", e);
//                                sendNotificationCountDTO.incrementFailureCount();
//                            }
//                        }, sendNotificationTask))
//                        .toArray(CompletableFuture[]::new)
//        ).join();
//    }
//
//
//    // https://firebase.google.cn/docs/reference/fcm/rest/v1/projects.messages?hl=zh-cn 消息组装
//    private static JsonObject buildMessage(String token, SendNotificationDTO sendNotificationDTO) {
//        JsonObject message = new JsonObject();
//        message.addProperty("token", token);
//
//        // 通知配置
//        JsonObject notification = new JsonObject();
//        notification.addProperty("title", sendNotificationDTO.getTitle());
//        if (sendNotificationDTO.getPushType() == PushTypeEnum.TEXT) {
//            notification.addProperty("body", sendNotificationDTO.getBody());
//        }
//        if (sendNotificationDTO.getPushType() == PushTypeEnum.IMAGE) {
//            notification.addProperty("body", sendNotificationDTO.getBody());
//            notification.addProperty("image", sendNotificationDTO.getImageUrl());
//        }
//        message.add("notification", notification);
//        // 数据字段 - 只在值存在时添加
//        JsonObject data = new JsonObject();
//        if (sendNotificationDTO.getSchemeEnum() != null) {
//            data.addProperty("deep_link", sendNotificationDTO.getSchemeEnum().getAndroidScheme());
//        }
//        if (sendNotificationDTO.getMessageId() != null) {
//            data.addProperty("notify_id", String.valueOf(sendNotificationDTO.getMessageId()));
//        }
//        // 只在data有属性时才添加到message中
//        if (!data.isEmpty()) {
//            message.add("data", data);
//        }
//
//        // Android 配置
//        JsonObject android = new JsonObject();
//        android.addProperty("priority", "high");
//
//
//        // Android 通知配置
//        JsonObject notifyAndroid = new JsonObject();
//        notifyAndroid.addProperty("channel_id", "default_fcm_channel");
//        notifyAndroid.addProperty("click_action", "com.yile.ai.NOTIFY_ACTION_ACTIVITY");
//        android.add("notification", notifyAndroid);
//
//        message.add("android", android);
//
//        // 包装消息
//        JsonObject requestBody = new JsonObject();
//        requestBody.add("message", message);
//        return requestBody;
//    }
//
//    private void handleResponse(JsonNode resp, SendNotificationCountDTO sendNotificationCountDTO) {
//        log.debug("FCM Android push response:: {}", resp);
//        // FCM成功响应会包含一个name字段（消息ID）
//        if (resp.has("name")) {
//            sendNotificationCountDTO.incrementSuccessCount();
//        } else {
//            // 记录错误信息
//            if (resp.has("error")) {
//                String errorCode = resp.path("error").path("code").asText();
//                String errorMessage = resp.path("error").path("message").asText();
//
//                // 检查是否是限流错误
//                if ("429".equals(errorCode)
//                        || "RESOURCE_EXHAUSTED".equals(errorCode)
//                        || (errorMessage != null && errorMessage.contains("Quota exceeded"))) {
//                    sendNotificationCountDTO.incrementLimitCount();
//                } else {
//                    sendNotificationCountDTO.incrementFailureCount();
//                    log.error("FCM Android push failed, code: {}, message: {}", errorCode, errorMessage);
//                }
//            } else {
//                sendNotificationCountDTO.incrementFailureCount();
//                log.error("FCM Android push failed with unexpected response: {}", resp);
//            }
//        }
//    }
//}
