package com.lx.pl.service.message;


import com.lx.pl.enums.ClientType;
import com.lx.pl.enums.NotificationTokenTypeEnum;
import com.lx.pl.exception.ServerInternalException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

@Service
public class SendNotificationFactory {

    @Resource
    private ApnsService apnsService;

    @Resource
    private FcmIOSService fcmIOSService;

//    @Resource
//    private FcmAndroidService fcmAndroidService;


    /**
     * 根据平台类型和通知类型获取对应的发送通知实现
     *
     * @param clientType          平台类型
     * @param notificationTokenTypeEnum 通知类型
     * @return 发送通知实现
     */
    public ISendNotification findSendNotification(ClientType clientType, NotificationTokenTypeEnum notificationTokenTypeEnum) {
        if (Objects.equals(clientType, ClientType.ios)
                && Objects.equals(notificationTokenTypeEnum, NotificationTokenTypeEnum.DEVICE_TOKEN)) {
            return apnsService;
        }
        if (Objects.equals(clientType, ClientType.ios)) {
            return fcmIOSService;
        }
//        if (Objects.equals(clientType, ClientType.android)) {
//            return fcmAndroidService;
//        }
        return null;
    }
}
