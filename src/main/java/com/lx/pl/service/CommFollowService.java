package com.lx.pl.service;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.lx.pl.db.mysql.community.dto.CommFollowWithStats;
import com.lx.pl.db.mysql.community.entity.*;
import com.lx.pl.db.mysql.community.entity.AccountInfo;
import com.lx.pl.db.mysql.community.entity.CommFollow;
import com.lx.pl.db.mysql.community.entity.CommUser;
import com.lx.pl.db.mysql.community.entity.TaskLumen;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.db.mysql.gen.repository.CommFollowRepository;
import com.lx.pl.db.mysql.gen.repository.CommUserRepository;
import com.lx.pl.dto.CommPageInfo;
import com.lx.pl.exception.ServerInternalException;
import com.mongodb.client.result.UpdateResult;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CommFollowService {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Lazy
    @Autowired
    private CommUserService commUserService;

    @Autowired
    private CommFollowRepository commFollowRepository;

    @Autowired
    private CommUserRepository commUserRepository;

    @Autowired
    private RedissonClient redissonClient;

    public Boolean addCommFollow(Long userId, User user) {
        boolean result = false;
        // 添加空值检查
        if (Objects.isNull(userId) || Objects.isNull(user)) {
            return result;
        }

        // 防止用户关注自己
        if (userId.equals(user.getId())) {
            return result;
        }

        RLock lock = redissonClient.getLock("follow:user:" + userId);
        try {
            lock.lock();
            CommUser commUser = commUserService.findUserByUserId(userId);

            if (!Objects.isNull(commUser)) {
                // 先查询关注关系是否存在
                Query query = new Query();
                query.addCriteria(Criteria.where("targetAcc.userId").is(userId));
                query.addCriteria(Criteria.where("ownerAcc.userId").is(user.getId()));
                if (!CollectionUtils.isEmpty(mongoTemplate.find(query, CommFollow.class))) {
                    log.warn("用户【{}】已关注【{}】, 请勿重复关注", user.getId(), userId);
                    return result;
                }

                CommFollow commFollow = new CommFollow();

                commFollow.setCreateTime(LocalDateTime.now());
                //发起关注用户
                AccountInfo followerInfo = new AccountInfo();
                followerInfo.setUserId(user.getId());
                followerInfo.setUserName(user.getUserName());
                followerInfo.setUserLoginName(user.getLoginName());
                followerInfo.setUserAvatarUrl(user.getAvatarUrl());
                followerInfo.setWhetherPro(Boolean.FALSE);
                commFollow.setOwnerAcc(followerInfo);
                commFollow.setOwnerIntroduction(user.getIntroduction());

                //被关注用户
                AccountInfo followeeInfo = new AccountInfo();
                BeanUtils.copyProperties(commUser.getAccountInfo(), followeeInfo);
                commFollow.setTargetAcc(followeeInfo);
                commFollow.setTargetIntroduction(commUser.getIntroduction());

                commFollowRepository.insert(commFollow);

                //用户的关注数据+1
                mongoTemplate.updateFirst(
                        new Query(Criteria.where("accountInfo.userId").is(user.getId())),
                        new Update().inc("followNums", 1),
                        CommUser.class
                );

                //被关注用户的被关注数据+1
                mongoTemplate.updateFirst(
                        new Query(Criteria.where("accountInfo.userId").is(userId)),
                        new Update().inc("fansNums", 1),
                        CommUser.class
                );

                //关注用户lumen任务数据+1
                mongoTemplate.updateFirst(
                        new Query(Criteria.where("userId").is(user.getId())),
                        new Update().inc("followNums", 1),
                        TaskLumen.class
                );

                //被关注用户lumen任务数据+1
                mongoTemplate.updateFirst(
                        new Query(Criteria.where("userId").is(followeeInfo.getUserId())),
                        new Update().inc("receiveFollowNums", 1),
                        TaskLumen.class
                );
            }

            result = true;
        } catch (Exception e) {
            log.error("用户：{},发起关注目标：{} 失败", user.getId(), userId, e);
            throw new ServerInternalException("follow Failed");
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return result;
    }

    public Boolean reduceCommFollow(Long userId, User user) {
        boolean result = false;
        // 添加空值检查
        if (Objects.isNull(userId) || Objects.isNull(user)) {
            return result;
        }

        // 防止用户取消关注自己
        if (userId.equals(user.getId())) {
            return result;
        }

        RLock lock = redissonClient.getLock("follow:user:" + userId);
        try {
            lock.lock();
            Query query = new Query();
            query.addCriteria(Criteria.where("targetAcc.userId").is(userId));
            query.addCriteria(Criteria.where("ownerAcc.userId").is(user.getId()));

            // 先执行删除操作并判断是否删除了至少一个文档
            boolean removeSuccess = mongoTemplate.remove(query, CommFollow.class).getDeletedCount() > 0;
            if (!removeSuccess) {
                log.warn("用户【{}】已取消关注【{}】, 请勿重复取消", user.getId(), userId);
                return result;
            }

            //用户的关注数据 - 1
            mongoTemplate.updateFirst(
                    new Query(Criteria.where("accountInfo.userId").is(user.getId())),
                    new Update().inc("followNums", -1),
                    CommUser.class
            );

            //被关注用户的被关注数据 - 1
            mongoTemplate.updateFirst(
                    new Query(Criteria.where("accountInfo.userId").is(userId)),
                    new Update().inc("fansNums", -1),
                    CommUser.class
            );

            //关注用户lumen任务数据+1
            mongoTemplate.updateFirst(
                    new Query(Criteria.where("userId").is(user.getId())),
                    new Update().inc("followNums", -1),
                    TaskLumen.class
            );

            //被关注用户lumen任务数据+1
            mongoTemplate.updateFirst(
                    new Query(Criteria.where("userId").is(userId)),
                    new Update().inc("receiveFollowNums", -1),
                    TaskLumen.class
            );
            result = true;
        } catch (Exception e) {
            log.error("用户：{} 对关注用户：{} 取消关注失败", user.getId(), userId, e);
            throw new ServerInternalException("Reduce Like Failed");
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return result;
    }

    public CommPageInfo<CommFollow> getCommFollowList(String lastFollowId, Integer pageSize, String selectType, User user) {
        List<CommFollow> commFollowList = new ArrayList<>();
        try {
            Query query = new Query();
            //查询关注列表
            if ("follow".equals(selectType)) {
                query.addCriteria(Criteria.where("ownerAcc.userId").is(user.getId()));
            }

            //查询粉丝列表
            if ("fans".equals(selectType)) {
                query.addCriteria(Criteria.where("targetAcc.userId").is(user.getId()));
            }

            // 游标分页：基于上一页最后一条记录的 ID
            if (StringUtil.isNotBlank(lastFollowId)) {
                query.addCriteria(Criteria.where("id").lt(new ObjectId(lastFollowId))); // 仅获取 ID 小于游标的记录
            }

            query.with(Sort.by(Sort.Direction.DESC, "id"));

            // 设置分页
            query.limit(pageSize);
            commFollowList = mongoTemplate.find(query, CommFollow.class);

            //如果是查询粉丝列表，需要增加followed的值
            if ("fans".equals(selectType)) {
                handleUserFollowed(commFollowList, user);
            }
        } catch (Exception e) {
            log.error("用户：{} 获取用户关注或者粉丝列表失败", user.getId(), e);
            throw new ServerInternalException("Failed to retrieve the user's following or followers list");
        }
        return buildPromptPageInfo(pageSize, commFollowList);
    }

    public CommPageInfo<CommFollowWithStats> getCommFollowListWithStats(String lastFollowId, Integer pageSize, String selectType, User user) {
        List<CommFollowWithStats> resultList = new ArrayList<>();
        try {
            List<AggregationOperation> operations = new ArrayList<>();

            // 1. 基础匹配条件
            if ("follow".equals(selectType)) {
                operations.add(Aggregation.match(Criteria.where("ownerAcc.userId").is(user.getId())));
            } else if ("fans".equals(selectType)) {
                operations.add(Aggregation.match(Criteria.where("targetAcc.userId").is(user.getId())));
            }

            // 2. 游标分页
            if (StringUtil.isNotBlank(lastFollowId)) {
                operations.add(Aggregation.match(Criteria.where("_id").lt(new ObjectId(lastFollowId))));
            }

            // 3. 排序
            operations.add(Aggregation.sort(Sort.Direction.DESC, "_id"));

            // 4. 关联用户信息
            LookupOperation lookupUser = Aggregation.lookup(
                    "users",
                    "follow".equals(selectType) ? "targetAcc.userId" : "ownerAcc.userId",
                    "accountInfo.userId",
                    "userInfo"
            );
            operations.add(lookupUser);

            // 5. 展开用户信息数组
            operations.add(Aggregation.unwind("userInfo", true));

            // 6. 分页限制
            operations.add(Aggregation.limit(pageSize));

            // 7. 最终字段投影 + 动态 introduction 映射
            ProjectionOperation projectStage = Aggregation.project()
                    .and("_id").as("id")
                    .and("createTime").as("createTime")
                    .and("userInfo.likeNums").as("likeNums")
                    .and("userInfo.followNums").as("followNums")
                    .and("userInfo.fansNums").as("fansNums")
                    .and("userInfo.publicImgNums").as("publicImgNums")
                    .and("userInfo.accountInfo").as("userAccountInfo");

            if ("fans".equals(selectType)) {
                projectStage = projectStage
                        .and("ownerIntroduction").as("introduction");
            } else if ("follow".equals(selectType)) {
                projectStage = projectStage
                        .and("targetIntroduction").as("introduction");
            }

            operations.add(projectStage);

            Aggregation aggregation = Aggregation.newAggregation(operations);
            resultList = mongoTemplate.aggregate(aggregation, "follows", CommFollowWithStats.class).getMappedResults();

            //如果是查询粉丝列表，需要增加followed的值
            if ("fans".equals(selectType)) {
                handleUserFollowedWithStats(resultList, user);
            }

        } catch (Exception e) {
            log.error("用户：{} 获取用户关注/粉丝列表(含统计信息)失败", user.getId(), e);
            throw new ServerInternalException("Failed to retrieve the user's following/followers list with statistics");
        }
        return buildPageInfo(pageSize, resultList);
    }

    private void handleUserFollowed(List<CommFollow> commFollowList, User user) {
        if (CollectionUtils.isEmpty(commFollowList)) return;

        // 提取粉丝用户ID（关注者的ownerAcc.userId）
        List<Long> fanUserIds = commFollowList.stream()
                .map(follow -> follow.getOwnerAcc().getUserId())
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        // 查询当前用户是否已关注这些粉丝用户
        Query followQuery = new Query(Criteria.where("ownerAcc.userId").is(user.getId())
                .and("targetAcc.userId").in(fanUserIds));
        List<CommFollow> existingFollows = mongoTemplate.find(followQuery, CommFollow.class);

        // 构建已关注的用户ID集合
        Set<Long> followedIds = existingFollows.stream()
                .map(follow -> follow.getTargetAcc().getUserId())
                .collect(Collectors.toSet());

        // 设置粉丝的 followed 状态
        commFollowList.forEach(follow ->
                follow.setFollowed(followedIds.contains(follow.getOwnerAcc().getUserId()))
        );
    }

    private void handleUserFollowedWithStats(List<CommFollowWithStats> commFollowList, User user) {
        if (CollectionUtils.isEmpty(commFollowList)) return;

        // 提取粉丝用户ID（关注者的ownerAcc.userId）
        List<Long> fanUserIds = commFollowList.stream()
                .map(follow -> follow.getUserAccountInfo().getUserId())
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        // 查询当前用户是否已关注这些粉丝用户
        Query followQuery = new Query(Criteria.where("ownerAcc.userId").is(user.getId())
                .and("targetAcc.userId").in(fanUserIds));
        List<CommFollow> existingFollows = mongoTemplate.find(followQuery, CommFollow.class);

        // 构建已关注的用户ID集合
        Set<Long> followedIds = existingFollows.stream()
                .map(follow -> follow.getTargetAcc().getUserId())
                .collect(Collectors.toSet());

        // 设置粉丝的 followed 状态
        commFollowList.forEach(follow ->
                follow.setFollowed(followedIds.contains(follow.getUserAccountInfo().getUserId()))
        );
    }

    // 构建分页结果对象
    private CommPageInfo<CommFollow> buildPromptPageInfo(Integer pageSize, List<CommFollow> commFollowList) {
        CommPageInfo<CommFollow> commPageInfo = new CommPageInfo<>();
        commPageInfo.setResultList(CollectionUtils.isEmpty(commFollowList) ? Collections.emptyList() : commFollowList);
        commPageInfo.setPageSize(pageSize);
        commPageInfo.setLastId(CollectionUtils.isEmpty(commFollowList) ? "" : commFollowList.get(commFollowList.size() - 1).getId());
        return commPageInfo;
    }

    // 构建分页结果对象
    private CommPageInfo<CommFollowWithStats> buildPageInfo(Integer pageSize, List<CommFollowWithStats> commFollowList) {
        CommPageInfo<CommFollowWithStats> commPageInfo = new CommPageInfo<>();
        commPageInfo.setResultList(CollectionUtils.isEmpty(commFollowList) ? Collections.emptyList() : commFollowList);
        commPageInfo.setPageSize(pageSize);
        commPageInfo.setLastId(CollectionUtils.isEmpty(commFollowList) ? "" : commFollowList.get(commFollowList.size() - 1).getId());
        return commPageInfo;
    }


    public Boolean judgeTargetUserFollow(Long userId, User user) {
        try {
            Query query = new Query();
            query.addCriteria(Criteria.where("ownerAcc.userId").is(user.getId()));
            query.addCriteria(Criteria.where("targetAcc.userId").is(userId));
            CommFollow commFollow = mongoTemplate.findOne(query, CommFollow.class);

            return !Objects.isNull(commFollow) ? Boolean.TRUE : Boolean.FALSE;
        } catch (Exception e) {
            log.error("用户：{} 对关注用户：{} 查询关注信息失败", user.getId(), userId, e);
            throw new ServerInternalException("Failed to check if the current user is being followed");
        }
    }


    /**
     * 根据 userId 查询关注用户的id集合
     *
     * @param userId 用户 ID
     */
    public List<Long> findFollowUserIdsByUserId(Long userId) {
        //查询用户的关注的id
        Query query = new Query();
        query.addCriteria(Criteria.where("ownerAcc.userId").is(userId));
        List<CommFollow> commFollowList = mongoTemplate.find(query, CommFollow.class);

        List<Long> followerUserIdList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(commFollowList)) {
            followerUserIdList = commFollowList.stream()
                    .map(commFollow -> commFollow.getTargetAcc().getUserId())  // 访问 TargetAcc 的 userId
                    .collect(Collectors.toList());
        }
        return followerUserIdList;
    }

    /**
     * 更新关注者的用户名称
     *
     * @param userId
     * @param newUsername
     */
    public void updateUsernameByUserId(Long userId, String newUsername) {
        try {
            // 更新 targetAcc.userName
            Query targetAccQuery = new Query();
            targetAccQuery.addCriteria(Criteria.where("targetAcc.userId").is(userId));
            Update targetAccUpdate = new Update();
            targetAccUpdate.set("targetAcc.userName", newUsername);
            UpdateResult targetResult = mongoTemplate.updateMulti(targetAccQuery, targetAccUpdate, CommFollow.class);

            log.info("关注更新 targetAcc userName记录数：{}", targetResult.getModifiedCount());

            // 更新 ownerAcc.userName
            Query ownerAccQuery = new Query();
            ownerAccQuery.addCriteria(Criteria.where("ownerAcc.userId").is(userId));
            Update ownerAccUpdate = new Update();
            ownerAccUpdate.set("ownerAcc.userName", newUsername);
            UpdateResult ownerResult = mongoTemplate.updateMulti(ownerAccQuery, ownerAccUpdate, CommFollow.class);

            log.info("关注更新 ownerAcc userName 记录数：{}", ownerResult.getModifiedCount());
        } catch (Exception e) {
            log.error("更新用户：{} 的用户名称：{}报错", userId, newUsername, e);
            throw new ServerInternalException("Failed to update username");
        }
    }

    /**
     * 更新关注者的用户头像
     *
     * @param userId
     * @param newUserAvatarUrl
     */
    public void updateUserAvatarUrlByUserId(Long userId, String newUserAvatarUrl) {
        try {
            // 更新 targetAcc.userName
            Query targetAccQuery = new Query();
            targetAccQuery.addCriteria(Criteria.where("targetAcc.userId").is(userId));
            Update targetAccUpdate = new Update();
            targetAccUpdate.set("targetAcc.userAvatarUrl", newUserAvatarUrl);
            UpdateResult targetResult = mongoTemplate.updateMulti(targetAccQuery, targetAccUpdate, CommFollow.class);

            log.info("关注更新 targetAcc userAvatarUr 记录数：{}", targetResult.getModifiedCount());

            // 更新 ownerAcc.userName
            Query ownerAccQuery = new Query();
            ownerAccQuery.addCriteria(Criteria.where("ownerAcc.userId").is(userId));
            Update ownerAccUpdate = new Update();
            ownerAccUpdate.set("ownerAcc.userAvatarUrl", newUserAvatarUrl);
            UpdateResult ownerResult = mongoTemplate.updateMulti(ownerAccQuery, ownerAccUpdate, CommFollow.class);

            log.info("关注更新 ownerAcc userAvatarUr 记录数：{}", ownerResult.getModifiedCount());
        } catch (Exception e) {
            log.error("更新用户：{} 的用户头像：{}报错", userId, newUserAvatarUrl, e);
            throw new ServerInternalException("Failed to update the follower's user avatar");
        }
    }

    /**
     * 更新用户的自介绍
     *
     * @param userId
     * @param introduction
     */
    public void updateUserIntroductionByUserId(Long userId, String introduction) {
        try {
            // 更新 targetAcc.userName
            Query targetAccQuery = new Query();
            targetAccQuery.addCriteria(Criteria.where("targetAcc.userId").is(userId));
            Update targetAccUpdate = new Update();
            targetAccUpdate.set("targetIntroduction", introduction);
            UpdateResult targetResult = mongoTemplate.updateMulti(targetAccQuery, targetAccUpdate, CommFollow.class);

            log.info("关注更新 targetAcc targetIntroduction 记录数：{}", targetResult.getModifiedCount());

            // 更新 ownerAcc.userName
            Query ownerAccQuery = new Query();
            ownerAccQuery.addCriteria(Criteria.where("ownerAcc.userId").is(userId));
            Update ownerAccUpdate = new Update();
            ownerAccUpdate.set("ownerIntroduction", introduction);
            UpdateResult ownerResult = mongoTemplate.updateMulti(ownerAccQuery, ownerAccUpdate, CommFollow.class);

            log.info("关注更新 ownerAcc ownerIntroduction 记录数：{}", ownerResult.getModifiedCount());
        } catch (Exception e) {
            log.error("更新用户：{} 的用户自我介绍：{}报错", userId, introduction, e);
            throw new ServerInternalException("Failed to update the follower's user avatar");
        }
    }
}
