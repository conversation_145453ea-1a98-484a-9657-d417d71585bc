package com.lx.pl.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.URLUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.lx.pl.Handler.ImgControlFactory;
import com.lx.pl.client.ImgControlApi;
import com.lx.pl.config.CosConfig;
import com.lx.pl.db.mysql.gen.entity.ImgControl;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.db.mysql.gen.mapper.ImgControlMapper;
import com.lx.pl.dto.BackendPromptResult;
import com.lx.pl.dto.ImgControlRequest;
import com.lx.pl.dto.ImgControlSaveRequest;
import com.lx.pl.enums.UploadType;
import com.lx.pl.util.IdUtils;
import com.lx.pl.util.JsonUtils;
import com.lx.pl.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import okhttp3.ResponseBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import retrofit2.Call;
import retrofit2.Response;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ImgControlService {

    @Autowired
    private ImgControlApi imgControlApi;

    @Autowired
    private CosConfig cosConfig;

    @Autowired
    ImgUploadCommonService imgUploadCommonService;

    @Autowired
    ImgControlMapper imgControlMapper;

    @Autowired
    CosService cosService;

    public String extractImg(String imgUrl, String controlType, User user) throws IOException {

        ImgControlRequest request = new ImgControlRequest();
        request.setImage_url(imgUrl);
        request.setTask_type(controlType);

        log.info("图片内容提取请求参数: {}", JsonUtils.writeToString(request));
        Call<ResponseBody> call = imgControlApi.extractImgContent(request);
        Response<ResponseBody> response = call.execute();

        String signedUrl = "";
        File tempFile = null;
        if (response.isSuccessful() && response.body() != null) {
            ResponseBody body = response.body();
            try {
                String responseStr = response.body().string();
                JsonNode rootNode = JsonUtils.writeStringToJsonNode(responseStr);

                int code = rootNode.path("code").asInt();
                String message = rootNode.path("message").asText();
                JsonNode dataNode = rootNode.path("data");

                if (code == 3000 && dataNode != null && !dataNode.isNull()) {
                    String base64Data = dataNode.asText();
                    if (StringUtils.isNotBlank(base64Data)) {
                        byte[] imageBytes = Base64.getDecoder().decode(base64Data);

                        tempFile = File.createTempFile("image_content_" + System.currentTimeMillis(), ".jpg");
                        try (FileOutputStream fos = new FileOutputStream(tempFile)) {
                            fos.write(imageBytes);
                            log.info("图片内容已写入临时文件: {}", tempFile.getAbsolutePath());
                        }

                        signedUrl = imgUploadCommonService.uploadToOssWithType(
                                tempFile, String.valueOf(user.getId()), UploadType.temp.getValue());
                    } else {
                        log.warn("响应成功，但图片 base64 数据为空");
                    }
                } else {
                    log.warn("业务处理失败，code: {}, message: {}", code, message);
                }
            } catch (IOException e) {
                throw new RuntimeException("图片处理失败", e);
            } finally {
                //对于临时文件处理完后，进行删除
                if (tempFile != null && tempFile.delete()) {
                    log.info("删除临时文件成功file：{}", tempFile.getAbsolutePath());
                }
            }
        } else {
            log.warn("未收到有效的图片数据，HTTP 状态码: {}", response.code());
        }

        return signedUrl;
    }


    public int getImgControlNums(String controlType, User user) {
        //查询功能公共的数目
        LambdaQueryWrapper<ImgControl> publicQuery = new LambdaQueryWrapper<>();
        publicQuery.eq(ImgControl::getLoginName, "system");
        publicQuery.eq(ImgControl::getImgType, "public");
        publicQuery.eq(ImgControl::getControlType, controlType);
        Long publicNums = imgControlMapper.selectCount(publicQuery);

        //查询功能公共用户删除的数目
        LambdaQueryWrapper<ImgControl> userDelPublicQuery = new LambdaQueryWrapper<>();
        userDelPublicQuery.eq(ImgControl::getLoginName, user.getLoginName());
        userDelPublicQuery.eq(ImgControl::getImgType, "public");
        userDelPublicQuery.eq(ImgControl::getControlType, controlType);
        userDelPublicQuery.eq(ImgControl::getDel, Boolean.TRUE);
        Long userDelPublicNums = imgControlMapper.selectCount(userDelPublicQuery);

        //查询用户新增的数目
        LambdaQueryWrapper<ImgControl> privateQuery = new LambdaQueryWrapper<>();
        privateQuery.eq(ImgControl::getLoginName, user.getLoginName());
        privateQuery.eq(ImgControl::getImgType, "private");
        privateQuery.eq(ImgControl::getControlType, controlType);
        privateQuery.eq(ImgControl::getDel, Boolean.FALSE);
        Long privateNums = imgControlMapper.selectCount(privateQuery);

        int result = publicNums.intValue() - userDelPublicNums.intValue() + privateNums.intValue();
        //避免出现负数
        result = result < 0 ? 0 : result;
        return result;
    }

    public Boolean saveImgControl(ImgControlSaveRequest imgControlSaveRequest, User user) {
        try {
            //桶上复制图片
            String fileKey = cosService.buildFileKey("imgControl", "jpg");
            cosService.copyImgSync(imgControlSaveRequest.getImgUrl(), fileKey);
            String imgUrl = cosConfig.getCosAccelerateDomain() + "/" + fileKey;

            ImgControl imgControl = new ImgControl();
            imgControl.setUserId(user.getId());
            imgControl.setLoginName(user.getLoginName());
            imgControl.setImgUrl(imgUrl);
            imgControl.setControlType(imgControlSaveRequest.getControlType());
            imgControl.setImgType("private");
            imgControl.setHeight(imgControlSaveRequest.getHeight());
            imgControl.setWidth(imgControlSaveRequest.getWidth());
            imgControl.setRealWidth(imgControlSaveRequest.getRealWidth());
            imgControl.setRealHeight(imgControlSaveRequest.getRealHeight());
            imgControl.setDel(Boolean.FALSE);
            imgControlMapper.insert(imgControl);
        } catch (Exception e) {
            log.error("用户：{}保存图片失败，失败原因：", user.getLoginName(), e);
            return Boolean.FALSE;
        }

        return Boolean.TRUE;
    }

    public ImgControl getImgControl(Long id, User user) {
        return imgControlMapper.selectById(id);
    }

    public Boolean deleteImgControl(ImgControl imgControl, User user) {
        try {
            if ("private".equals(imgControl.getImgType())) {
                //私有的图片，则直接删除
                imgControlMapper.deleteById(imgControl.getId());
            } else if ("public".equals(imgControl.getImgType())) {
                //公共的图片，则新增一条删除记录
                imgControl.setId(null);
                imgControl.setUserId(user.getId());
                imgControl.setLoginName(user.getLoginName());
                imgControl.setDel(Boolean.TRUE);
                imgControlMapper.insert(imgControl);
            } else {
                return Boolean.FALSE;
            }
        } catch (Exception e) {
            log.error("用户：{}删除图片失败，失败原因：", user.getLoginName(), e);
            return Boolean.FALSE;
        }

        return Boolean.TRUE;
    }

    public List<ImgControl> selectImgControlList(String controlType, User user) {
        List<ImgControl> imgControlList = new ArrayList<>();

        //查询功能公共的数目
        LambdaQueryWrapper<ImgControl> publicQuery = new LambdaQueryWrapper<>();
        publicQuery.eq(ImgControl::getLoginName, "system");
        publicQuery.eq(ImgControl::getImgType, "public");
        publicQuery.eq(ImgControl::getControlType, controlType);
        publicQuery.orderByAsc(ImgControl::getCreateTime);
        publicQuery.orderByAsc(ImgControl::getId);
        List<ImgControl> publicList = imgControlMapper.selectList(publicQuery);

        //添加公共数据
        if (!CollectionUtils.isEmpty(publicList)) {
            imgControlList.addAll(publicList);
        }

        //查询功能公共用户删除的数目
        LambdaQueryWrapper<ImgControl> userDelPublicQuery = new LambdaQueryWrapper<>();
        userDelPublicQuery.eq(ImgControl::getLoginName, user.getLoginName());
        userDelPublicQuery.eq(ImgControl::getImgType, "public");
        userDelPublicQuery.eq(ImgControl::getControlType, controlType);
        userDelPublicQuery.eq(ImgControl::getDel, Boolean.TRUE);
        List<ImgControl> userDelPublicList = imgControlMapper.selectList(userDelPublicQuery);

        //移除用户已经删除的功能数据
        if (!CollectionUtils.isEmpty(userDelPublicList)) {
            Set<String> imgUrlSet = userDelPublicList.stream()
                    .map(ImgControl::getImgUrl)
                    .collect(Collectors.toSet());

            imgControlList.removeIf(img -> imgUrlSet.contains(img.getImgUrl()));
        }

        //查询用户新增的数目
        LambdaQueryWrapper<ImgControl> privateQuery = new LambdaQueryWrapper<>();
        privateQuery.eq(ImgControl::getLoginName, user.getLoginName());
        privateQuery.eq(ImgControl::getImgType, "private");
        privateQuery.eq(ImgControl::getControlType, controlType);
        privateQuery.eq(ImgControl::getDel, Boolean.FALSE);
        privateQuery.orderByAsc(ImgControl::getCreateTime);
        privateQuery.orderByAsc(ImgControl::getId);
        List<ImgControl> privateList = imgControlMapper.selectList(privateQuery);

        //添加用户的个人数据
        if (!CollectionUtils.isEmpty(privateList)) {
            imgControlList.addAll(privateList);
        }

        return imgControlList;
    }
}
