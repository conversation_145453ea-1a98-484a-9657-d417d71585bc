package com.lx.pl.service;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lx.pl.constant.LogicConstants;
import com.lx.pl.db.mysql.gen.entity.VersionControlAndroid;
import com.lx.pl.db.mysql.gen.entity.VersionControlIos;
import com.lx.pl.db.mysql.gen.mapper.VersionControlAndroidMapper;
import com.lx.pl.db.mysql.gen.mapper.VersionControlIosMapper;
import com.lx.pl.exception.ServerInternalException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
@Slf4j
public class VersionControlService {

    @Autowired
    private VersionControlIosMapper versionControlIosMapper;

    @Autowired
    private VersionControlAndroidMapper versionControlAndroidService;

    @Autowired
    RedisService redisService;

    public String getIosVersion() {
        // 获取redis中存储的版本号
        String s = (String) redisService.get(LogicConstants.IOS_VERSION);

        // redis中没有，则从数据库中获取
        if (StringUtil.isBlank(s)) {
            VersionControlIos versionControlIos = versionControlIosMapper.selectOne(new LambdaQueryWrapper<VersionControlIos>()
                    .eq(VersionControlIos::getIsCurrent, 1));

            if (!Objects.isNull(versionControlIos) && !StringUtil.isBlank(versionControlIos.getVersion())) {
                // 更新redis中的版本号
                redisService.set(LogicConstants.IOS_VERSION, versionControlIos.getVersion());
                return versionControlIos.getVersion();
            }

            throw new ServerInternalException("can't get version number");
        }
        return s;
    }

    public String getAndroidVersion() {
        // 获取redis中存储的版本号
        String s = (String) redisService.get(LogicConstants.ANDROID_VERSION);

        // redis中没有，则从数据库中获取
        if (StringUtil.isBlank(s)) {
            VersionControlAndroid versionControlAndroid = versionControlAndroidService.selectOne(new LambdaQueryWrapper<VersionControlAndroid>()
                    .eq(VersionControlAndroid::getIsCurrent, 1));

            if (!Objects.isNull(versionControlAndroid) && !StringUtil.isBlank(versionControlAndroid.getVersion())) {
                // 更新redis中的版本号
                redisService.set(LogicConstants.ANDROID_VERSION, versionControlAndroid.getVersion());
                return versionControlAndroid.getVersion();
            }

            throw new ServerInternalException("can't get version number");
        }
        return s;
    }
}
