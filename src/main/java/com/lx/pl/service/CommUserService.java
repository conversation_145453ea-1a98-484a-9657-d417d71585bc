package com.lx.pl.service;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lx.pl.db.mysql.community.entity.*;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.db.mysql.gen.mapper.UserMapper;
import com.lx.pl.db.mysql.gen.repository.CommUserRepository;
import com.lx.pl.exception.ServerInternalException;
import com.lx.pl.pay.common.domain.SubscriptionCurrent;
import com.mongodb.client.result.UpdateResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


@Service
@Slf4j
public class CommUserService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private CommUserRepository commUserRepository;

    @Autowired
    private CommCommentService commCommentService;

    @Autowired
    private CommFollowService commFollowService;

    @Lazy
    @Autowired
    private CommImgService commImgService;

    @Autowired
    private CommLikeService commLikeService;

    public Boolean addCommUser(User user) {
        try {
            CommUser commUser = new CommUser();
            AccountInfo accountInfo = new AccountInfo();
            accountInfo.setUserId(user.getId());
            accountInfo.setUserLoginName(user.getLoginName());
            accountInfo.setUserName(user.getUserName());
            accountInfo.setUserAvatarUrl(user.getAvatarUrl());
            accountInfo.setWhetherPro(Boolean.FALSE);
            commUser.setAccountInfo(accountInfo);
            commUser.setIntroduction(StringUtil.isNotBlank(user.getIntroduction()) ? user.getIntroduction() : "");
            commUser.setCreateTime(user.getCreateTime());

            commUser.setLikeNums(0);
            commUser.setFollowNums(0);
            commUser.setFansNums(0);
            commUser.setPublicImgNums(0);

            commUserRepository.insert(commUser);

            return Boolean.TRUE;
        } catch (Exception e) {
            log.error("新增用户：{}到社区报错", user.getId(), e);
            throw new ServerInternalException("Add Failed");
        }
    }

    /**
     * 判断用户的userName和头像是否有变更，有则刷新社区信息
     *
     * @param userId
     * @param userName
     * @param userAvatarUrl
     */
    @Transactional
    public void updateUserMessage(Long userId, String introduction, String userName, String userAvatarUrl) {
        CommUser commUser = findUserByUserId(userId);
        if (Objects.isNull(commUser)) {
            return;
        }

        //如果用户自我介绍发生变更
        if ((StringUtil.isNotBlank(introduction) && !introduction.equals(commUser.getIntroduction()))) {
            commFollowService.updateUserIntroductionByUserId(userId, introduction);

            mongoTemplate.updateFirst(
                    new Query(Criteria.where("accountInfo.userId").is(userId)),
                    new Update().set("introduction", introduction),
                    CommUser.class
            );
        }

        //如果用户名称变更
        if (StringUtil.isNotBlank(userName) && !userName.equals(commUser.getAccountInfo().getUserName())) {
            commCommentService.updateUsernameByUserId(userId, userName);
            commFollowService.updateUsernameByUserId(userId, userName);
            commImgService.updateUsernameByUserId(userId, userName);
            commLikeService.updateUsernameByUserId(userId, userName);

            mongoTemplate.updateFirst(
                    new Query(Criteria.where("accountInfo.userId").is(userId)),
                    new Update().set("accountInfo.userName", userName),
                    CommUser.class
            );
        }

        //如果用户头像变更
        if (StringUtil.isNotBlank(userAvatarUrl) && !userAvatarUrl.equals(commUser.getAccountInfo()
                .getUserAvatarUrl())) {
            commCommentService.updateUserAvatarUrlByUserId(userId, userAvatarUrl);
            commFollowService.updateUserAvatarUrlByUserId(userId, userAvatarUrl);
            commImgService.updateUserAvatarUrlByUserId(userId, userAvatarUrl);
            commLikeService.updateUserAvatarUrlByUserId(userId, userAvatarUrl);

            mongoTemplate.updateFirst(
                    new Query(Criteria.where("accountInfo.userId").is(userId)),
                    new Update().set("accountInfo.userAvatarUrl", userAvatarUrl),
                    CommUser.class
            );
        }
    }

    /**
     * 根据 userId 查询用户详细信息
     *
     * @param userId 用户 ID
     * @return CommUser 用户详情
     */
    public CommUser findUserByUserId(Long userId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("accountInfo.userId").is(userId));
        return mongoTemplate.findOne(query, CommUser.class);
    }

    /**
     * 根据 userId 查询用户详细信息
     *
     * @param userId 用户 ID
     * @return CommUser 用户详情
     */
    public CommUser findUserByUserIdAndFollow(Long userId, User user) {
        CommUser commUser = findUserByUserId(userId);
        if (!Objects.isNull(commUser)) {
            commUser.setFollowed(commFollowService.judgeTargetUserFollow(userId, user));
        }
        return commUser;
    }

    public Boolean addAllCommUser() {
        Long lastId = null; // 游标初始化为 null
        Integer pageSize = 1000;

        while (true) {
            // 构造游标查询条件
            LambdaQueryWrapper<User> query = new LambdaQueryWrapper<>();
            query.orderByAsc(User::getId); // 按 ID 升序
            if (lastId != null) {
                query.gt(User::getId, lastId); // 仅查询大于游标的数据
            }

            // 分页查询数据
            List<User> userList = userMapper.selectList(query.last("LIMIT " + pageSize));

            if (CollectionUtils.isEmpty(userList)) {
                break; // 无数据时退出循环
            }

            List<CommUser> commUserList = new ArrayList<>();
            for (User user : userList) {
                try {
                    CommUser commUser = new CommUser();
                    AccountInfo accountInfo = new AccountInfo();
                    accountInfo.setUserId(user.getId());
                    accountInfo.setUserLoginName(user.getLoginName());
                    accountInfo.setUserName(user.getUserName());
                    accountInfo.setUserAvatarUrl(user.getAvatarUrl());
                    accountInfo.setWhetherPro(Boolean.FALSE);
                    commUser.setAccountInfo(accountInfo);
                    commUser.setIntroduction(StringUtil.isNotBlank(user.getIntroduction()) ? user.getIntroduction() : "");
                    commUser.setCreateTime(!Objects.isNull(user.getCreateTime()) ? user.getCreateTime() : LocalDateTime.now());

                    commUser.setLikeNums(0);
                    commUser.setFollowNums(0);
                    commUser.setFansNums(0);
                    commUser.setPublicImgNums(0);
                    commUserList.add(commUser);
                } catch (Exception e) {
                    log.error("用户：{}，插入社区报错", user.getId(), e);
                }
            }

            commUserRepository.saveAll(commUserList);

            // 更新游标为当前批次最后一条数据的 ID
            lastId = userList.get(userList.size() - 1).getId();
        }

        return Boolean.TRUE;
    }

    @Transactional
    public void updateUserVip(SubscriptionCurrent current, Long userId) {
        CommUser commUser = findUserByUserId(userId);
        if (Objects.isNull(commUser)) {
            return;
        }
//        Query targetAccQuery = new Query();
//        targetAccQuery.addCriteria(Criteria.where("targetAcc.userId").is(userId));
//        Update targetAccUpdate = new Update();
//        if (vipBeginTime != null) {
//            targetAccUpdate.set("targetAcc.vipBeginTime", vipBeginTime);
//        }
//        if (vipEndTime != null) {
//            targetAccUpdate.set("targetAcc.vipEndTime", vipEndTime);
//        }
//        if (vipType != null) {
//            targetAccUpdate.set("targetAcc.vipType", vipType);
//        }
//        if (priceInterval != null) {
//            targetAccUpdate.set("targetAcc.priceInterval", priceInterval);
//        }
//
//        // 更新 ownerAcc.userName
//        Query ownerAccQuery = new Query();
//        ownerAccQuery.addCriteria(Criteria.where("ownerAcc.userId").is(userId));
//        Update ownerAccUpdate = new Update();
//        if (vipBeginTime != null) {
//            ownerAccUpdate.set("ownerAcc.vipBeginTime", vipBeginTime);
//        }
//        if (vipEndTime != null) {
//            ownerAccUpdate.set("ownerAcc.vipEndTime", vipEndTime);
//        }
//        if (vipType != null) {
//            ownerAccUpdate.set("ownerAcc.vipType", vipType);
//        }
//        if (priceInterval != null) {
//            ownerAccUpdate.set("ownerAcc.priceInterval", priceInterval);
//        }
//
//        UpdateResult targetResult = mongoTemplate.updateMulti(targetAccQuery, targetAccUpdate, CommFollow.class);
//        log.info("关注更新 targetAcc updateUserVip 记录数：{}", targetResult.getModifiedCount());
//        UpdateResult ownerResult = mongoTemplate.updateMulti(ownerAccQuery, ownerAccUpdate, CommFollow.class);
//        log.info("关注更新 ownerAcc updateUserVip 记录数：{}", ownerResult.getModifiedCount());
//
//        targetResult = mongoTemplate.updateMulti(targetAccQuery, targetAccUpdate, CommComment.class);
//        log.info("评论更新 targetAcc updateUserVip 记录数：{}", targetResult.getModifiedCount());
//        ownerResult = mongoTemplate.updateMulti(ownerAccQuery, ownerAccUpdate, CommComment.class);
//        log.info("评论更新 ownerAcc updateUserVip 记录数：{}", ownerResult.getModifiedCount());
//
//        targetResult = mongoTemplate.updateMulti(targetAccQuery, targetAccUpdate, CommFile.class);
//        log.info("文件更新 targetAcc updateUserVip 记录数：{}", targetResult.getModifiedCount());
//        ownerResult = mongoTemplate.updateMulti(ownerAccQuery, ownerAccUpdate, CommFile.class);
//        log.info("文件更新 ownerAcc updateUserVip 记录数：{}", ownerResult.getModifiedCount());
//
//        targetResult = mongoTemplate.updateMulti(targetAccQuery, targetAccUpdate, CommLike.class);
//        log.info("点赞更新 targetAcc updateUserVip 记录数：{}", targetResult.getModifiedCount());
//        ownerResult = mongoTemplate.updateMulti(ownerAccQuery, ownerAccUpdate, CommLike.class);
//        log.info("点赞更新 ownerAcc updateUserVip 记录数：{}", ownerResult.getModifiedCount());

        Update update = new Update();

        if (current.getVipBeginTime() != null) {
            update.set("accountInfo.vipBeginTime", current.getVipBeginTime());
        }
        if (current.getVipEndTime() != null) {
            update.set("accountInfo.vipEndTime", current.getVipEndTime());
        }
        if (current.getPlanLevel() != null) {
            update.set("accountInfo.planLevel", current.getPlanLevel());
        }
        if (current.getPriceInterval() != null) {
            update.set("accountInfo.priceInterval", current.getPriceInterval());
        }
        UpdateResult updateResult = mongoTemplate.updateFirst(
                new Query(Criteria.where("accountInfo.userId").is(userId)),
                update,
                CommUser.class
        );
        log.info("用户更新 updateUserVip 记录数：{}", updateResult.getModifiedCount());
    }
}
