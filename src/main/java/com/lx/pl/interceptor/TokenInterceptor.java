package com.lx.pl.interceptor;


import com.github.lianjiatech.retrofit.spring.boot.interceptor.BasePathMatchInterceptor;
import okhttp3.HttpUrl;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * @Description: 注解式拦截器
 * 看登录状态是否过期,如过期,重启设置
 */
@Component
public class TokenInterceptor extends BasePathMatchInterceptor {

    private volatile long expired;
    private volatile String token;
    private long tokenExpiredTime = 300000;

    @Value("${apikey}")
    String apikey;

    @Override
    protected Response doIntercept(Chain chain) throws IOException {
        long now = System.currentTimeMillis();
        Request request = chain.request();

        HttpUrl url = request.url();
        if (token == null || expired < now + tokenExpiredTime) {
            synchronized (this) {
                if (token == null || expired < now + tokenExpiredTime) {
                    refreshToken();
                }
            }
        }
        request = request.newBuilder()
                .url(url)
                .addHeader("token", token)
                .addHeader("Content-Type", "application/json;charset=UTF-8")
                .addHeader("Authorization", "Bearer " + apikey)
                .addHeader("Accept-Encoding", "gzip, deflate")
                .addHeader("Connection", "keep-alive")
                .addHeader("Accept", "*/*")
                .build();
        return chain.proceed(request);
    }

    /**
     * 更新Token参数
     */
    private void refreshToken() {
        this.token = "newToken";
        this.expired = System.currentTimeMillis() + (tokenExpiredTime * 2);
    }
}
