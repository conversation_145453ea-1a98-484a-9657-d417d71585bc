package com.lx.pl.interceptor;

import com.lx.pl.annotation.Authorization;
import com.lx.pl.annotation.NoToken;
import com.lx.pl.manager.TokenManager;

import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.util.Objects;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

/**
 * 自定义拦截器，判断此次请求是否有权限
 */
@Component
public class AuthorizationInterceptor extends HandlerInterceptorAdapter {

    public static final String CURRENT_USER_ID = "CURRENT_USER_ID";
    private static final String AUTHORIZATION = "authorization";

    @Autowired
    private TokenManager manager;

    @Value("${callback_apikey}")
    String callbackApikey;

    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {
        //如果不是映射到方法直接通过
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }

        HandlerMethod handlerMethod = (HandlerMethod) handler;
        Method method = handlerMethod.getMethod();

        //如果是notoken注解方法，则免登录，进行api-key进行登录
        boolean isAnnotationNoToken = !Objects.isNull(handlerMethod.getMethod().getDeclaredAnnotation(NoToken.class));
        if (isAnnotationNoToken) {
            String clientApiKey = request.getHeader("Authorization"); //  key通过HTTP头传递
            if (StringUtils.isNotBlank(clientApiKey) && clientApiKey.equals("Bearer " + callbackApikey)) { // 检查API key是否有效
                return true;
            } else {
                return false;
            }
        }

        //从header中得到token
        String token = request.getHeader(AUTHORIZATION);
        if (manager.checkToken(token)) {
            //如果token验证成功，权限检查通过，将token对应的用户id存在request中，便于之后注入
            request.setAttribute(CURRENT_USER_ID, manager.getUserId(token));
            return true;
        }

        //如果验证token失败，并且方法注明了Authorization，返回401错误
        if (method.getAnnotation(Authorization.class) != null) {
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            return false;
        }
        return true;
    }
}
