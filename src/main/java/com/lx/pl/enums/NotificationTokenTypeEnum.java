package com.lx.pl.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
public enum NotificationTokenTypeEnum {


    FCM_TOKEN("fcm_token"),
    DEVICE_TOKEN("device_token"),
    UNKNOWN("unknown");

    private final String code;

    NotificationTokenTypeEnum(String code) {
        this.code = code;
    }

    /**
     * 获取通知令牌类型
     *
     * @param fcmToken    FCM 令牌
     * @param deviceToken 设备令牌
     * @return 通知令牌类型
     */
    public static NotificationTokenTypeEnum getNotificationTokenTypeEnum(String fcmToken, String deviceToken) {
        // 优先使用 fcmToken
        if (StringUtils.isNotBlank(fcmToken)) {
            return FCM_TOKEN;
        } else if (StringUtils.isNotBlank(deviceToken)) {
            return DEVICE_TOKEN;
        } else {
            return UNKNOWN;
        }
    }


}
