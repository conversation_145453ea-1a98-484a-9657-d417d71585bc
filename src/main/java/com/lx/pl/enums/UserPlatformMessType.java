package com.lx.pl.enums;

import com.lx.pl.util.StringUtils;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public enum UserPlatformMessType {


    featured(1, "精选", Collections.emptyList()),
    official(2, "普通私信", Collections.emptyList()),
    upgrade(3, "会员升级通知", Arrays.asList(ClientType.web.getValue())),
    winning(4, "活动获奖通知", Arrays.asList(ClientType.web.getValue())),
    MEMBER_ABOUT_TO_EXPIRE(5, "会员即将到期", Arrays.asList(ClientType.web.getValue())),
    MEMBER_EXPIRED(6, "会员已到期", Arrays.asList(ClientType.web.getValue())),
    TRIAL_ABOUT_TO_EXPIRED(7, "试订阅即将到期并扣费", Arrays.asList(ClientType.web.getValue())),
    FAILED_TO_DEDUCT(8, "会员扣费失败通知", Arrays.asList(ClientType.web.getValue()));



    private Integer value;

    private String label;

    private  List<String> platformInfo;


    //如果枚举值中还有数据则必须要创建一个构造函数
    UserPlatformMessType(Integer value, String label, List<String> platformInfo) {
        this.value = value;
        this.label = label;
        this.platformInfo = platformInfo;
    }

    public static boolean allPlatform(UserPlatformMessType type) {
        List<String> messagePlatformInfo = Optional.ofNullable(type.getPlatformInfo()).orElse(Collections.emptyList());

        return CollectionUtils.isEmpty(messagePlatformInfo) || new HashSet<>(messagePlatformInfo).containsAll(ClientType.allValues);
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public List<String> getPlatformInfo() {
        return platformInfo;
    }

    public void setPlatformInfo(List<String> platformInfo) {
        this.platformInfo = platformInfo;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }


}
