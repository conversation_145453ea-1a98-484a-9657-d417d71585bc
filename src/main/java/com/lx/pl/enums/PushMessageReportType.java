package com.lx.pl.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum PushMessageReportType {

    /**
     * 推送消息打开上报
     */
    PUSH_OPEN("push_open", "推送消息打开上报");

    private final String code;
    private final String desc;

    public static PushMessageReportType getByCode(String code) {
        if (code != null) {
            for (PushMessageReportType value : values()) {
                if (value.getCode().equals(code)) {
                    return value;
                }
            }
        }
        return null;
    }
}