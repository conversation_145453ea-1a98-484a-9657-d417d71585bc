package com.lx.pl.enums;

public enum SensitiveMessage {

    NSFW("NSFW", "涉黄");

    private String value;

    private String label;

    //如果枚举值中还有数据则必须要创建一个构造函数
    SensitiveMessage(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }
}
