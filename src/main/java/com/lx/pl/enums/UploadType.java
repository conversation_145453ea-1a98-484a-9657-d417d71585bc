package com.lx.pl.enums;

public enum UploadType {

    normal("normal", "常规上传"),
    suggest("suggest", "用户反馈"),
    album("album", "用户相册"),
    community("community", "社区"),

    temp("temp", "零时文件"),
    BATCH_RMBG("batch_rmbg", "批量去背景"),
    ;
    private String value;

    private String label;

    // 如果枚举值中还有数据则必须要创建一个构造函数
    UploadType(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }


}
