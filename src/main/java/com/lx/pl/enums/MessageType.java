package com.lx.pl.enums;

public enum MessageType {

    LIKE("like", "点赞消息"),
    COMMENT("comment", "评论消息"),
    SYS_UPDATE("sysUpdate", "系统消息"),
    PLATFORM_MESSAGE("platformMessage", "平台消息"),

    PLATFORM_ACTIVITY("platformActivity","平台活动");

    private String value;

    private String label;

    //如果枚举值中还有数据则必须要创建一个构造函数
    MessageType(String value, String label) {
        this.value = value;
        this.label = label;
    }


    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public static MessageType fromValue(String value) {
        for (MessageType type : MessageType.values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown message type: " + value);
    }
}
