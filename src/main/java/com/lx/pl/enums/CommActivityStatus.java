package com.lx.pl.enums;

public enum CommActivityStatus {

    conduct(1, "进行中"),
    awards(2, "评奖中"),
    end(3, "已结束");

    private Integer value;

    private String label;

    //如果枚举值中还有数据则必须要创建一个构造函数
    CommActivityStatus(Integer value, String label) {
        this.value = value;
        this.label = label;
    }


    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }
}
