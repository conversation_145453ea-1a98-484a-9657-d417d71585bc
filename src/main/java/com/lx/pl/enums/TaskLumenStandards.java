package com.lx.pl.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

@Getter
@AllArgsConstructor
public enum TaskLumenStandards {

    // 每日任务
    FreeLumens("1", "Free Lumens", 1, 10, "用户每日登录可获得Lumen", TaskGroup.DAILY.getId(), 1),
    LIKE_20_CREATIONS("2", "Like 20 creations", 20, 4, "每日点赞20次", TaskGroup.DAILY.getId(), 2),
    LeaveAComment("3", "Leave a Comment", 1, 1, "每日首次评论", TaskGroup.DAILY.getId(), 3),
    ShareYorCreation("4", "Share yor creation", 1, 1, "每日首次分享到社区", TaskGroup.DAILY.getId(), 4),
    WATCH_1_AD("18", "Watch 1 ad", 1, 2, "用户每日第1次观看激励广告", TaskGroup.DAILY.getId(), 5),
    WATCH_2_AD("19", "Watch 2 ad", 2, 2, "用户每日第2次观看激励广告", TaskGroup.DAILY.getId(), 5),
    WATCH_3_AD("20", "Watch 3 ad", 3, 2, "用户每日第3次观看激励广告", TaskGroup.DAILY.getId(), 5),
    WATCH_4_AD("21", "Watch 4 ad", 4, 2, "用户每日第4次观看激励广告", TaskGroup.DAILY.getId(), 5),
    WATCH_5_AD("22", "Watch 5 ad", 5, 2, "用户每日第5次观看激励广告", TaskGroup.DAILY.getId(), 5),

    // 成就任务
    FirstImageCreation("5", "First Image Creation", 1, 2, "新用户首次生图", TaskGroup.ACHIEVEMENT.getId(), 3),
    FollowUsOnIG("6", "Follow Us on IG", 1, 2, "关注IG官号", TaskGroup.ACHIEVEMENT.getId(), 4),
    FollowUsOnX("7", "Follow Us on X", 1, 2, "关注X官号", TaskGroup.ACHIEVEMENT.getId(), 5),

    RECEIVE_5_LIKES("8", "Get 5 Likes", 5, 1, "获得5个点赞", TaskGroup.ACHIEVEMENT.getId(), 1),
    RECEIVE_20_LIKES("9", "Get 20 Likes", 20, 2, "获得20个点赞", TaskGroup.ACHIEVEMENT.getId(), 1),
    RECEIVE_50_LIKES("10", "Get 50 Likes", 50, 5, "获得50个点赞", TaskGroup.ACHIEVEMENT.getId(), 1),
    RECEIVE_100_LIKES("11", "Get 100 Likes", 100, 10, "获得100个点赞", TaskGroup.ACHIEVEMENT.getId(), 1),

    RECEIVE_5_FOLLOWERS("12", "Get 5 Followers", 5, 2, "获得5个关注", TaskGroup.ACHIEVEMENT.getId(), 2),
    RECEIVE_30_FOLLOWERS("13", "Get 30 Followers", 30, 3, "获得30个关注", TaskGroup.ACHIEVEMENT.getId(), 2),
    RECEIVE_100_FOLLOWERS("14", "Get 100 Followers", 100, 10, "获得100个关注", TaskGroup.ACHIEVEMENT.getId(), 2),

    SUBSCRIBE_YOUTUBE("15", "Subscribe our channel", 1, 2, "订阅PicLumen的YouTube频道", TaskGroup.ACHIEVEMENT.getId(), 6),
    JOIN_DISCORD("16", "Join Discord", 1, 2, "加入Discord", TaskGroup.ACHIEVEMENT.getId(), 7),
    WRITE_REVIEW("17", "Vote Us on", 1, 6, "去Trustpilot写好评", TaskGroup.ACHIEVEMENT.getId(), 8),
    WRITE_REVIEW_PRODUCTHUNT("23", "Vote Us on", 1, 6, "去Producthunt写好评", TaskGroup.ACHIEVEMENT.getId(), 9);

    private final String taskId;
    private final String taskName;
    private final Integer targetCount;  // 任务目标次数
    private final Integer reward;       // 完成后奖励的 lumen 币
    private final String description;   // 任务描述
    private final Integer taskGroupId;  //任务组
    private final Integer order;        //组内任务排序

    //观看广告任务id集合
    public static final Set<String> WATCH_AD_TASK_IDS_SET = new HashSet<>(
            Set.of(WATCH_1_AD.getTaskId(),
                    WATCH_2_AD.getTaskId(),
                    WATCH_3_AD.getTaskId(),
                    WATCH_4_AD.getTaskId(),
                    WATCH_5_AD.getTaskId()));

    public static TaskLumenStandards getByTaskId(String taskId) {
        if (StringUtils.isBlank(taskId)) {
            return null;
        }

        return Arrays.stream(TaskLumenStandards.values()).filter(taskLumenStandards -> Objects.equals(taskLumenStandards.getTaskId(), taskId)).findFirst().orElse(null);
    }
}
