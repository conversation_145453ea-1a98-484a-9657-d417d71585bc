package com.lx.pl.enums;

public enum Img2imgStyle {

    contentRefer("contentRefer", "Content Refer"),
    styleRefer("styleRefer", "Style Refer"),
    characterRefer("characterRefer", "Character Refer");

    private String value;

    private String label;

    //如果枚举值中还有数据则必须要创建一个构造函数
    Img2imgStyle(String value, String label) {
        this.value = value;
        this.label = label;
    }


    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public static String getLabelByValue(String value) {
        for (Img2imgStyle img2imgStyle : Img2imgStyle.values()) {
            if (img2imgStyle.getValue().equals(value)) {
                return img2imgStyle.getLabel();
            }
        }
        throw new IllegalArgumentException("No enum constant with label " + value);
    }
}
