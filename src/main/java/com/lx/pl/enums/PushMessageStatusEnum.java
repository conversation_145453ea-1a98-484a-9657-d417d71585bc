package com.lx.pl.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum PushMessageStatusEnum {

    /**
     * 未推送
     */
    PENDING(0, "未推送"),

    /**
     * 已推送
     */
    PUSHED(1, "已推送"),

    /**
     * 已过期
     */
    EXPIRED(2, "已过期"),

    /**
     * 已暂停
     */
    PAUSED(3, "已暂停"),

    /**
     * DEFAULT
     */
    DEFAULT(-1, "DEFAULT"),

    /**
     * FAILED
     */
    FAILED(-2, "FAILED"),

    /**
     * 正在推送
     */
    PUSHING(5, "正在推送");

    private final Integer code;
    private final String desc;

    public static PushMessageStatusEnum getByCode(Integer code) {
        if (code != null) {
            for (PushMessageStatusEnum value : values()) {
                if (value.getCode().equals(code)) {
                    return value;
                }
            }
        }
        return DEFAULT;
    }
}
