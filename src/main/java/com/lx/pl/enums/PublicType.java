package com.lx.pl.enums;

public enum PublicType {

    undisclosed(0, "未公开"),
    publicity(1, "已公开"),
    review(2, "审核中"),
    reject(3, "已拒绝");

    private Integer value;

    private String label;

    //如果枚举值中还有数据则必须要创建一个构造函数
    PublicType(Integer value, String label) {
        this.value = value;
        this.label = label;
    }


    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }
}
