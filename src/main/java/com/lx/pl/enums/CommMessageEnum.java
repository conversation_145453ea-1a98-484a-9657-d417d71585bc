package com.lx.pl.enums;

public enum CommMessageEnum {

    like("like", "点赞"),
    comment("comment", "评论"),
    sysUpdate("sysUpdate","系统更新"),

    platformMessage("platformMessage", "平台信息");

    private String value;

    private String label;

    //如果枚举值中还有数据则必须要创建一个构造函数
    CommMessageEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }
}
