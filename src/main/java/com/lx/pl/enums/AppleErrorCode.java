package com.lx.pl.enums;

/**
 * @Description: stripe支付处理枚举，描述不返回前端，用于细分错误码，message 描述前端显示错误
 * @Author: senlin_he
 * @Date: 2024/12/27
 */
public enum AppleErrorCode {
    VALIDATE_RECEIPT_ERROR("800", " ", "验证receipt 出错"),
    VERIFY_AND_DECODE_TRANSACTION_ERROR("801", " verify and decode transaction error", "验证 transaction payload 出错"),
    FAILED_TO_UPDATE_PAYAPPLEUSERRELATION("802", "Failed to update PayAppleUserRelation", "绑定PayAppleUserRelation和originalTransactionId出错"),
    PAY_APPLE_USER_RELATION_NOT_FOUND("803", " No PayAppleUserRelation found ", "没有找到apple支付关联账号信息"),
    PROCESS_RENEWAL_TRANSACTION_ERROR("804", " Failed to process renewal transaction ", "处理续订交易失败"),
    SUBSCRIPTION_NOT_FOUND("805", "subscription not found", "没有找到对应的订阅信息SubscriptionCurrent"),
    PRODUCT_PARAM_ERROR("806", "Both originalProduct and currentProduct must not be null", "新产品信息和老产品信息不能同时为null"),
    GET_HISTORY_ERROR("807", "get history error", "获取交易历史失败"),
    PRODUCT_NOT_FOUND("808", "product not found", "没有找到对应的产品信息"),
    REPEAT_VALIDATE_ERROR("809", "repeat validate error", "重复验证"),
    // 消耗产品失败
    CONSUMABLE_VALIDATE_ERROR("810", "consumable validate error", "消耗产品失败"),
    TRANSACTION_RELATION_ERROR("811", "", "未找到事务关联关系"),
    CAN_NOT_RESTORE("812", "You have purchase record in apple, can not support restore", "不能进行restore"),
    ;

    private final String code;
    private final String message;
    private final String description;

    AppleErrorCode(String code, String message, String description) {
        this.code = code;
        this.message = message;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public String getDescription() {
        return description;
    }
}
