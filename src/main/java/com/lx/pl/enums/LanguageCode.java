package com.lx.pl.enums;

import lombok.Data;

public enum LanguageCode {
    SIMPLIFIED_CHINESE("Simplified Chinese", "zh-CN"),
    TRADITIONAL_CHINESE("Traditional Chinese", "zh-TW"),
    FRENCH("French", "fr"),
    SPANISH("Spanish", "es"),
    PORTUGUESE("Portuguese", "pt"),
    GERMAN("German", "de"),
    ITALIAN("Italian", "it"),
    RUSSIAN("Russian", "ru"),
    DUTCH("Dutch", "nl"),
    JAPANESE("Japanese", "ja"),
    KOREAN("Korean", "ko"),
    INDONESIAN("Indonesian", "id"),
    THAI("Thai", "th"),
    VIETNAMESE("Vietnamese", "vi"),
    NORWEGIAN("Norwegian", "no"),
    DANISH("Danish", "da"),
    SWEDISH("Swedish", "sv"),
    ARABIC("Arabic", "ar");


    private String value;

    private String label;

    //如果枚举值中还有数据则必须要创建一个构造函数
    LanguageCode(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public static String getValueByLabel(String label) {
        for (LanguageCode languageCode : LanguageCode.values()) {
            if (languageCode.getLabel().equals(label)) {
                return languageCode.getValue();
            }
        }
        return "";
    }

}
