package com.lx.pl.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 推送类型枚举
 */
@Getter
@RequiredArgsConstructor
public enum PushTypeEnum {
    TEXT(1, "文本"),
    IMAGE(2, "大图");

    private final Integer value;
    private final String desc;

    /**
     * 根据value获取枚举
     */
    public static PushTypeEnum fromValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (PushTypeEnum type : PushTypeEnum.values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 判断值是否有效
     */
    public static boolean isValid(Integer value) {
        return fromValue(value) != null;
    }
}
