package com.lx.pl.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/6/7
 * @description lumen变化类型
 */
@Getter
public enum LumenChangeTypeEnum {
    ADD("add", "增加"),
    DEDUCT("deduct", "减少");

    private final String value;
    private final String description;

    LumenChangeTypeEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }
}
