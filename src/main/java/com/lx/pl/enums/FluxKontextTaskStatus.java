package com.lx.pl.enums;

/**
 * Flux Kontext任务状态枚举
 *
 * <AUTHOR>
 */
public enum FluxKontextTaskStatus {
    
    QUEUED("Queued", "排队中"),
    PROCESSING("Processing", "处理中"),
    READY("Ready", "完成"),
    ERROR("Error", "失败");

    private final String status;
    private final String description;

    FluxKontextTaskStatus(String status, String description) {
        this.status = status;
        this.description = description;
    }

    public String getStatus() {
        return status;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据状态字符串获取枚举
     */
    public static FluxKontextTaskStatus fromStatus(String status) {
        for (FluxKontextTaskStatus taskStatus : values()) {
            if (taskStatus.getStatus().equalsIgnoreCase(status)) {
                return taskStatus;
            }
        }
        return ERROR; // 默认返回错误状态
    }

    /**
     * 是否为最终状态
     */
    public boolean isFinalStatus() {
        return this == READY || this == ERROR;
    }

    /**
     * 是否为成功状态
     */
    public boolean isSuccess() {
        return this == READY;
    }
}
