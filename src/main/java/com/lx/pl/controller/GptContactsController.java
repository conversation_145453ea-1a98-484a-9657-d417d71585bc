package com.lx.pl.controller;

import com.lx.pl.annotation.Authorization;
import com.lx.pl.annotation.CurrentUser;
import com.lx.pl.db.mysql.gen.entity.GptContacts;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.dto.generic.R;
import com.lx.pl.service.GenService;
import com.lx.pl.service.IGptContactsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.List;

/**
 * 联系我们Controller
 *
 * <AUTHOR>
 * @date 2024-07-10
 */
@RestController
@Tag(description = "联系我们控制器", name = "联系我们管理")
@RequestMapping("/api/contacts")
public class GptContactsController {
    @Resource
    private IGptContactsService gptContactsService;

    @Autowired
    GenService genService;

    /**
     * 查询联系我们列表
     */
    @Operation(summary = "查询联系我们列表")
    @GetMapping("/list")
    @Authorization
    public R<List<GptContacts>> list(GptContacts gptContacts) {
        List<GptContacts> list = gptContactsService.selectGptContactsList(gptContacts);
        return R.success(list);
    }

    /**
     * 获取联系我们详细信息
     */
    @Operation(summary = "获取联系我们详细信息")
    @GetMapping(value = "/{id}")
    @Authorization
    public R<GptContacts> getInfo(@PathVariable("id") Long id) {
        return R.success(gptContactsService.selectGptContactsById(id));
    }

    /**
     * 新增联系我们
     */
    @Operation(summary = "新增联系我们")
    @PostMapping
    @Authorization
    public R<Integer> add(@RequestBody GptContacts gptContacts) {
        gptContactsService.insertGptContacts(gptContacts);
        return R.success();
    }

    /**
     * 修改联系我们
     */
    @Operation(summary = "修改联系我们")
    @PutMapping
    @Authorization
    public R<Integer> edit(@RequestBody GptContacts gptContacts) {
        return R.success(gptContactsService.updateGptContacts(gptContacts));
    }

    /**
     * 删除联系我们
     */
    @Operation(summary = "删除联系我们")
    @DeleteMapping("/{ids}")
    @Authorization
    public R<Integer> remove(@PathVariable Long[] ids) {
        return R.success(gptContactsService.deleteGptContactsByIds(ids));
    }

    /**
     * 逻辑新增联系我们
     */
    @Operation(summary = "逻辑新增联系我们")
    @PostMapping("/create")
    public R<Integer> create(@Validated @RequestBody GptContacts gptContacts, HttpServletRequest request) throws IOException {
        gptContacts.setUserAgent(request.getHeader("User-Agent"));
        String platform = genService.getPlatform(request);
        Integer result = gptContactsService.createGptContacts(gptContacts, platform);
        return result > 0 ? R.success() : R.fail(1, "Failed");
    }
}
