package com.lx.pl.controller.pay;

import com.lx.pl.db.mysql.gen.entity.StripeProduct;
import com.lx.pl.service.StripeProductService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/stripe-product")
public class StripeProductController {

    @Autowired
    private StripeProductService stripeProductService;

    /**
     * 查询所有商品信息
     */
    @GetMapping("/list")
    public List<StripeProduct> listAll() {
        return stripeProductService.list();
    }

    /**
     * 根据 ID 查询商品信息
     */
    @GetMapping("/{id}")
    public StripeProduct getById(@PathVariable Long id) {
        return stripeProductService.getById(id);
    }

    /**
     * 创建或更新商品信息
     */
    @PostMapping("/save")
    public boolean saveOrUpdate(@RequestBody StripeProduct stripeProduct) {
        return stripeProductService.saveOrUpdate(stripeProduct);
    }

    /**
     * 根据 ID 删除商品信息
     */
    @DeleteMapping("/{id}")
    public boolean deleteById(@PathVariable Long id) {
        return stripeProductService.removeById(id);
    }
}