package com.lx.pl.controller.pay;

import com.lx.pl.db.mysql.gen.entity.StripeUserCustomer;
import com.lx.pl.service.StripeCustomerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/stripe-customer")
public class StripeCustomerController {

    @Autowired
    private StripeCustomerService stripeCustomerService;

    /**
     * 查询所有客户信息
     */
    @GetMapping("/list")
    public List<StripeUserCustomer> listAll() {
        return stripeCustomerService.list();
    }

    /**
     * 根据 ID 查询客户信息
     */
    @GetMapping("/{id}")
    public StripeUserCustomer getById(@PathVariable Long id) {
        return stripeCustomerService.getById(id);
    }

    /**
     * 创建或更新客户信息
     */
    @PostMapping("/save")
    public boolean saveOrUpdate(@RequestBody StripeUserCustomer stripeUserCustomer) {
        return stripeCustomerService.saveOrUpdate(stripeUserCustomer);
    }

    /**
     * 根据 ID 删除客户信息
     */
    @DeleteMapping("/{id}")
    public boolean deleteById(@PathVariable Long id) {
        return stripeCustomerService.removeById(id);
    }
}