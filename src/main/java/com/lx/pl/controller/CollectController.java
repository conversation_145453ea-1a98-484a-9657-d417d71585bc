package com.lx.pl.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.lx.pl.annotation.Authorization;
import com.lx.pl.annotation.CurrentUser;
import com.lx.pl.db.mysql.gen.entity.*;
import com.lx.pl.dto.*;
import com.lx.pl.dto.generic.R;
import com.lx.pl.service.CollectService;
import com.lx.pl.util.StringUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

@Tag(name = "收藏夹相关接口")
@Slf4j
@RestController
@RequestMapping("/api/collect")
public class CollectController {

    @Autowired
    CollectService collectService;


    @Operation(summary = "用户收藏")
    @PostMapping("/add")
    @Authorization
    public R<CollectUseSize> addCollect(
            @RequestParam("classifyId") @Parameter(description = "收藏夹id") String classifyId,
            @RequestParam("imgName") @Parameter(description = "图片名称") String imgName,
            @RequestParam("promptId") String promptId,
            @CurrentUser @Parameter(hidden = true) User user) {

        if (Objects.isNull(collectService.getUserCollectClassify(classifyId, user))) {
            return R.fail(400, "Favorite does not exist !");
        }
        if (!Objects.isNull(collectService.getUserCollect(promptId, imgName, user.getLoginName(), classifyId))) {
            return R.fail(400, "Do not collect repeatedly !");
        }
        PromptFile promptFile = collectService.getPromptFile(promptId, imgName, user.getLoginName());
        PromptRecord promptRecord = collectService.getPromptRecord(promptId, user.getLoginName());
        if (Objects.isNull(promptFile) || Objects.isNull(promptRecord)) {
            return R.fail(400, "collect fail , the picture had been deleted !");
        }
        CollectUseSize result = collectService.addCollect(classifyId, promptId, imgName, user, promptRecord, promptFile);
        return !Objects.isNull(result) ? R.success(result) : R.fail(1, "Failed");
    }

    @Operation(summary = "用户取消收藏")
    @PostMapping("/reduce")
    @Authorization
    public R<CollectUseSize> reduceCollect(
            @RequestParam("imgName") @Parameter(description = "图片名称") String imgName,
            @RequestParam("promptId") String promptId,
            @RequestParam("classifyId") @Parameter(description = "收藏夹id") String classifyId,
            @CurrentUser @Parameter(hidden = true) User user) {

        if (Objects.isNull(collectService.getUserCollectClassify(classifyId, user))) {
            return R.fail(400, "Favorite does not exist !");
        }
        UserCollect userCollect = collectService.getUserCollect(promptId, imgName, user.getLoginName(), classifyId);
        if (Objects.isNull(userCollect)) {
            return R.fail(400, "Please collect first !");
        }
        CollectUseSize result = collectService.reduceCollect(userCollect, user);
        return !Objects.isNull(result) ? R.success(result) : R.fail(1, "Failed");
    }

    @Operation(summary = "分页查询收藏页图片")
    @GetMapping("/list")
    @Authorization
    public R<PromptPageInfo<UserCollectResult>> getHomeList(
            @RequestParam("pageNum") Integer pageNum,
            @RequestParam("pageSize") Integer pageSize,
            @RequestParam("classifyId") String classifyId,
            @RequestParam(value = "vagueKey", required = false) String vagueKey,
            @RequestParam(value = "collationName") @Parameter(description = "排序规则名称：Ascending : 升序 Descending : 降序")
            String collationName,
            @CurrentUser @Parameter(hidden = true) User user) throws JsonProcessingException {
        return R.success(collectService.getCollectList(pageNum, pageSize, vagueKey, classifyId, collationName, user));
    }

    @Operation(summary = "分页查询收藏页图片(web端新接口)")
    @GetMapping("/history-collect-list")
    @Authorization
    public R<PromptPageInfo<HistoryImgResult>> getHistoryList(
            @RequestParam("pageNum") Integer pageNum,
            @RequestParam("pageSize") Integer pageSize,
            @RequestParam("classifyId") String classifyId,
            @RequestParam(value = "vagueKey", required = false) String vagueKey,
            @RequestParam(value = "collationName") @Parameter(description = "排序规则名称：Ascending : 升序 Descending : 降序")
            String collationName,
            @CurrentUser @Parameter(hidden = true) User user) throws JsonProcessingException {
        return R.success(collectService.getHistoryCollectList(pageNum, pageSize, vagueKey, classifyId, collationName, user));
    }

    @Operation(summary = "新增收藏夹")
    @PostMapping("/add-classify")
    @Authorization
    public R<Boolean> addCollectClassify(@RequestParam("collectName") @Parameter(description = "收藏夹名称") String collectName,
                                         @RequestParam(value = "description", required = false) @Parameter(description = "描述") String description,
                                         @RequestParam(value = "cover", required = false) @Parameter(description = "封面") String cover,
                                         @CurrentUser @Parameter(hidden = true) User user) {
        Long collectClassify = collectService.getCollectClassify(null, null, user);

        if (collectClassify >= 1024) {
            return R.fail(400, "You have reached the maximum number of 1024 collection folders");
        }

        Long num = collectService.getCollectClassify(collectName, null, user);
        if (num >= 1) {
            return R.fail(400, "this category already exists");
        }
        Boolean result = collectService.addCollectClassify(collectName, description, user, cover);
        return result ? R.success(result) : R.fail(1, "Failed");
    }

    @Operation(summary = "重命名收藏夹或修改收藏夹")
    @PostMapping("/rename-classify")
    @Authorization
    public R<Boolean> renameCollectClassify(@RequestParam("id") @Parameter(description = "收藏夹id") String id,
                                            @RequestParam("collectName") @Parameter(description = "收藏夹名称") String collectName,
                                            @RequestParam(value = "description", required = false) @Parameter(description = "描述") String description,
                                            @RequestParam(value = "cover", required = false) @Parameter(description = "封面") String cover,
                                            @CurrentUser @Parameter(hidden = true) User user) {

        Long num = collectService.getCollectClassify(collectName, id, user);
        if (num >= 1) {
            return R.fail(400, "this category already exists");
        }
        Boolean result = collectService.renameCollectClassify(id, collectName, description, user, cover);
        return result ? R.success(result) : R.fail(1, "Failed");
    }

    @Operation(summary = "用户收藏夹列表")
    @GetMapping("/classify-list")
    @Authorization
    public R<List<UserCollectClassify>> getCollectClassifyList(
            @CurrentUser @Parameter(hidden = true) User user) {
        return R.success(collectService.getCollectClassifyList(user));
    }


    @Operation(summary = "用户删除收藏夹")
    @PostMapping("/delete-classify")
    @Authorization
    public R<CollectUseSize> deleteCollectClassifyList(@RequestParam("id") @Parameter(description = "收藏夹id") String id,
                                                       @CurrentUser @Parameter(hidden = true) User user) {
        //校验收藏夹里是否还有图片
        Long userCollectNums = collectService.getUserCollectNums(user.getLoginName(), id);
        if (!Objects.isNull(userCollectNums) && userCollectNums > 0) {
            return R.fail(400, "Please delete all the pictures in the collection folder first !");
        }
        CollectUseSize result = collectService.deleteCollectClassify(id, user);
        return !Objects.isNull(result) ? R.success(result) : R.fail(1, "Failed");
    }

    @Operation(summary = "用户批量收藏")
    @PostMapping("/add-batch")
    @Authorization
    public R<CollectUseSize> addCollect(
            @RequestBody FileCollectBatch collectBatch,
            @CurrentUser @Parameter(hidden = true) User user) {

        if (StringUtils.isBlank(collectBatch.getClassifyId()) || CollectionUtils.isEmpty(collectBatch.getCollectBatchList())) {
            return R.fail(400, "Please choose favorites");
        }

        if (Objects.isNull(collectService.getUserCollectClassify(collectBatch.getClassifyId(), user))) {
            return R.fail(400, "Favorite does not exist !");
        }
        CollectUseSize result = collectService.addCollectBatchSegmentation(collectBatch.getCollectBatchList(), collectBatch.getClassifyId(), user);
        return !Objects.isNull(result) ? R.success(result) : R.fail(1, "Failed");
    }

    @Operation(summary = "用户取消批量收藏")
    @PostMapping("/reduce-batch")
    @Authorization
    public R<CollectUseSize> reduceCollect(
            @RequestBody FileCollectBatch collectBatch,
            @CurrentUser @Parameter(hidden = true) User user) {
        if (StringUtils.isBlank(collectBatch.getClassifyId()) || CollectionUtils.isEmpty(collectBatch.getCollectBatchList())) {
            return R.fail(400, "Please choose favorites");
        }

        if (Objects.isNull(collectService.getUserCollectClassify(collectBatch.getClassifyId(), user))) {
            return R.fail(400, "Favorite does not exist !");
        }
        CollectUseSize result = collectService.reduceCollectBatchSegmentation(collectBatch.getCollectBatchList(), collectBatch.getClassifyId(), user);
        return !Objects.isNull(result) ? R.success(result) : R.fail(1, "Failed");
    }

    @Operation(summary = "用户移到收藏")
    @PostMapping("/move")
    @Authorization
    public R<CollectUseSize> moveCollect(
            @RequestParam("fileName") @Parameter(description = "图片名称") String fileName,
            @RequestParam("promptId") String promptId,
            @RequestParam("classifyId") @Parameter(description = "收藏夹id") String classifyId,
            @RequestParam("oldClassifyId") @Parameter(description = "老收藏夹id") String oldClassifyId,
            @CurrentUser @Parameter(hidden = true) User user) {

        if (Objects.isNull(collectService.getUserCollectClassify(classifyId, user))) {
            return R.fail(400, "Favorite does not exist !");
        }

        UserCollect userCollect = collectService.getUserCollect(promptId, fileName, user.getLoginName(), oldClassifyId);
        if (Objects.isNull(userCollect)) {
            return R.fail(400, "Please collect first !");
        }

        if (!Objects.isNull(collectService.getUserCollect(promptId, fileName, user.getLoginName(), classifyId))) {
            return R.fail(400, "Do not collect repeatedly !");
        }

        CollectUseSize result = collectService.moveCollect(userCollect, classifyId, user);
        return !Objects.isNull(result) ? R.success(result) : R.fail(1, "Failed");
    }

    @Operation(summary = "用户批量移到收藏")
    @PostMapping("/move-batch")
    @Authorization
    public R<CollectUseSize> moveCollectBatch(
            @RequestBody FileCollectBatch collectBatch,
            @CurrentUser @Parameter(hidden = true) User user) {

        if (Objects.isNull(collectService.getUserCollectClassify(collectBatch.getOldClassifyId(), user))) {
            return R.fail(400, "Favorite does not exist !");
        }

        CollectUseSize result = collectService.moveCollectBatchSegmentation(collectBatch.getCollectBatchList(), collectBatch.getClassifyId()
                , collectBatch.getOldClassifyId(), user);
        return !Objects.isNull(result) ? R.success(result) : R.fail(1, "Failed");
    }


    @Operation(summary = "更新用户收藏量")
    @GetMapping("/update-user-collect-num")
    @Authorization
    public Boolean updateUserCollectNum() {
        return collectService.updateUserCollectNum();
    }

    @Operation(summary = "设置封面")
    @PostMapping("/set-cover")
    @Authorization
    public R<Boolean> renameCollectClassify(@RequestParam("id") @Parameter(description = "收藏夹id") String id,
                                            @RequestParam(value = "cover", required = false) @Parameter(description = "封面") String cover,
                                            @CurrentUser @Parameter(hidden = true) User user) {

        Boolean result = collectService.setCollectClassifyCover(id, user, cover);
        return result ? R.success(result) : R.fail(1, "Failed");
    }

    @Operation(summary = "用户批量取消收藏并删除")
    @PostMapping("/reduce-delete-batch")
    @Authorization
    public R<CollectUseSize> reduceDeleteCollect(
            @RequestBody FileCollectBatch collectBatch,
            @CurrentUser @Parameter(hidden = true) User user) {
        if (StringUtils.isBlank(collectBatch.getClassifyId()) || CollectionUtils.isEmpty(collectBatch.getCollectBatchList())) {
            return R.fail(400, "Please choose favorites");
        }

        if (Objects.isNull(collectService.getUserCollectClassify(collectBatch.getClassifyId(), user))) {
            return R.fail(400, "Favorite does not exist !");
        }

        CollectUseSize result = collectService.reduceCollectDeleteBatch(collectBatch.getCollectBatchList(), collectBatch.getClassifyId(), user);

        return !Objects.isNull(result) ? R.success(result) : R.fail(1, "Failed");
    }

}
