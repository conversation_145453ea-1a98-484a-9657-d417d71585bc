package com.lx.pl.controller;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.lx.pl.annotation.Authorization;
import com.lx.pl.annotation.CurrentUser;
import com.lx.pl.annotation.RateLimit;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.dto.SuspensionNotice;
import com.lx.pl.dto.TranslateParams;
import com.lx.pl.dto.generic.R;
import com.lx.pl.openai.service.Img2TextService;
import com.lx.pl.service.*;
import com.lx.pl.util.JsonUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;

@Slf4j
@Tag(name = "通用接口")
@RestController
@RequestMapping("/api/common")
public class CommonController {

    @Autowired
    CommonService commonService;

    @Autowired
    StatisticsService statisticsService;

    @Autowired
    RedisService redisService;

    @Autowired
    VersionControlService versionControlIosService;

    @Autowired
    Img2TextService img2TextService;

    @Autowired
    LogicParamsService logicParamsService;

    @Autowired
    GenService genService;

    @GetMapping("/hello")
    public String hello() {
        return "world";
    }

    @PostMapping("/callback")
    public String callback(@RequestBody Object josn) throws JsonProcessingException {
        log.info("callback:{}", JsonUtils.writeToString(josn));
        return "ok";
    }

    @Operation(summary = "返回接口版本号")
    @GetMapping("/version")
    @Authorization
    public String version() {
        return commonService.version();
    }

    @Operation(summary = "翻译，中文翻译为英文")
    @GetMapping("/translate")
    @Authorization
    @RateLimit(limit = 20, timeout = 60000)
    public R<String> translate(
            @RequestParam @Parameter(description = "需要翻译或者增强的提示词") String prompt,
            @RequestParam @Parameter(description = "处理类型 translation|enhance|translation_enhance") String mode,
            @Parameter(hidden = true) @CurrentUser User user) throws JsonProcessingException, UnsupportedEncodingException {
        if ("enhance".equals(mode) && prompt.length() > 600) {
            return R.fail(400, "enhance cannot exceed 600 !");
        }
        TranslateParams translateParams = new TranslateParams();
        translateParams.setContent(prompt);
        translateParams.setMode(mode);

        String translatedOrEnhanceText = img2TextService.translateToLanguage(translateParams, user);
        return StringUtil.isNotBlank(translatedOrEnhanceText) ? R.success(translatedOrEnhanceText) : R.fail(400, "failed to translate or enhance！");
    }


    @Operation(summary = "用户导出生图入参excel")
    @GetMapping("/genInfo-export")
    @Authorization
    public R<Boolean> genInfoExport(@RequestParam("userEmail") String userEmail,
                                    @CurrentUser @Parameter(hidden = true) User user) throws IOException {
        if (statisticsService.getGenInfoExport(user) > 0) {
            return R.fail(400, "Do not add repeatedly !");
        }
        if (userEmail.length() > 100) {
            return R.fail(400, "Email length should not exceed 100 characters !");
        }
        Boolean result = statisticsService.genInfoExport(userEmail, user);
        return result ? R.success(result) : R.fail(1, "Failed");
    }


    @GetMapping("/statistics")
    public Boolean statisticsCreatePicture() {
        statisticsService.statisticsCreatePicture();
        return Boolean.TRUE;
    }

    @Operation(summary = "获取系统时间")
    @GetMapping("/getSysTime")
    public R<Long> getSysTime(HttpServletResponse response) {
        response.setHeader("Cache-Control", "no-cache, no-store, max-age=0, must-revalidate");
        response.setHeader("Pragma", "no-cache"); // HTTP 1.0 兼容
        response.setHeader("Expires", "0"); // 立即过期，强制客户端不缓存结果
        return R.success(System.currentTimeMillis());
    }

    @Operation(summary = "返回ios版本号")
    @GetMapping("/iosVersion")
    public R<String> iosVersion() {
        return R.success(versionControlIosService.getIosVersion());
    }

    @Operation(summary = "返回android版本号")
    @GetMapping("/androidVersion")
    public R<String> androidVersion() {
        return R.success(versionControlIosService.getAndroidVersion());
    }

    @Operation(summary = "获取停服通知时间")
    @GetMapping("/suspension-time")
    public R<SuspensionNotice> getSuspensionTime() {
        return R.success(commonService.getSuspensionTime());
    }

    @Operation(summary = "获取广告配置")
    @GetMapping("/ad-config")
    public R<String> getAdConfig(HttpServletRequest request) {
        String platform = genService.getPlatform(request);
        if (StringUtil.isBlank(platform)) {
            return R.fail(400, "Illegal request !");
        }
        return R.success(logicParamsService.getAdConfig(platform));
    }
}
