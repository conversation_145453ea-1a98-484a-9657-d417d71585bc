package com.lx.pl.controller;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.lx.pl.annotation.Authorization;
import com.lx.pl.annotation.CurrentUser;
import com.lx.pl.annotation.NoToken;
import com.lx.pl.db.mysql.gen.entity.*;
import com.lx.pl.dto.*;
import com.lx.pl.dto.generic.ListInfo;
import com.lx.pl.dto.generic.R;
import com.lx.pl.exception.ServerInternalException;
import com.lx.pl.service.GenService;
import com.lx.pl.service.ImgService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

import lombok.extern.slf4j.Slf4j;
import org.apache.batik.bridge.UserAgent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@Tag(name = "图片相关接口")
@Slf4j
@RestController
@RequestMapping("/api/img")
public class ImgController {

    @Autowired
    ImgService imgService;

    @Autowired
    GenService genService;

    @Operation(summary = "init")
    @PostMapping("/init-high")
    public R<Boolean> listHistory() {
        imgService.initWebp90();
        return R.success(true);
    }

    @Operation(summary = "查询当前用户生成的图片")
    @PostMapping("/gen-history/history-list")
    @Authorization
    public R<PromptPageInfo<PromptFileRecord>> listHistory(
            @RequestParam("pageNum") Integer pageNum,
            @RequestParam("pageSize") Integer pageSize,
            @RequestParam(value = "vagueKey", required = false) String vagueKey,
            @CurrentUser @Parameter(hidden = true) User user, HttpServletRequest request) throws JsonProcessingException {
        if (pageSize > 20) {
            return R.fail(400, "pageSize exceeds 20 !");
        }
        String platform = genService.getPlatform(request);
        if (StringUtil.isBlank(platform)) {
            return R.fail(400, "Illegal request !");
        }
        Boolean notWeb = !"web".equals(platform);

        return R.success(imgService.genHistoryList(pageNum, pageSize, vagueKey, user, notWeb));
    }

    @Operation(summary = "删除某张图片")
    @PostMapping("/delete")
    @Authorization
    public R<String> deletePicture(
            @RequestParam("imgName") @Parameter(description = "图片名称") String imgName,
            @RequestParam("promptId") String promptId,
            @CurrentUser @Parameter(hidden = true) User user) {
        try {
            boolean result = imgService.deleteByFilename(imgName, promptId, user);
            return result ? R.success("") : R.fail(1, "Failed");
        } catch (Exception e) {
            return R.fail(1, "Failed");
        }
    }

//    @Operation(summary = "批量删除图片")
//    @PostMapping("/delete-batch")
//    @Authorization
//    public R<String> deleteBatch(@RequestBody List<Long> ids,
//                                 @CurrentUser @Parameter(hidden = true) User user) {
//        boolean result = imgService.deleteBatchByIds(ids, user);
//        return result ? R.success("") : R.fail(1, "Failed");
//    }

    @Operation(summary = "返回社区的示例图，prefer参数可选值：1.hot（根据点击次数返回示例） 2.fav（根据点赞数返回示例） 3.latest（根据创建时间返回最近更新的示例）")
    @GetMapping("/feed/{prefer}/{pageNum}/{pageSize}")
    @Authorization
    public R<ListInfo<ImageGenRecord>> feed(@PathVariable("prefer") String prefer,
                                            @PathVariable("pageNum") Integer pageNum,
                                            @PathVariable("pageSize") Integer pageSize,
                                            @CurrentUser @Parameter(hidden = true) User user) {
        if (pageSize > 50) {
            return R.fail(400, "pageSize exceeds 50 !");
        }
        return R.success(imgService.feed(pageNum, pageSize, prefer, user.getId()));
    }

    @Operation(summary = "分页查询Home页图片")
    @PostMapping("/explore-list")
    @Authorization
    public R<PromptPageInfo<HomeFileResult>> getExploreList(
            @RequestParam("pageNum") Integer pageNum,
            @RequestParam("pageSize") Integer pageSize,
            @RequestParam(value = "vagueKey", required = false) String vagueKey,
            @RequestParam(value = "collationName", required = false) @Parameter(description = "排序规则名称：latest : 生成时间 hot : 点赞数  likes : 用户点赞")
            String collationName,
            @CurrentUser @Parameter(hidden = true) User user, HttpServletRequest request) throws JsonProcessingException {
        String platform = genService.getPlatform(request);
        if (StringUtil.isBlank(platform)) {
            return R.fail(400, "Illegal request !");
        }
        Boolean notWeb = !"web".equals(platform);
        return R.success(imgService.getExploreList(pageNum, pageSize, vagueKey, collationName, user, notWeb));
    }

    @Operation(summary = "随机查询图片")
    @PostMapping("/random-list")
    @Authorization
    public R<RandomPageInfo<HomeFileResult>> getRandomList(
            @RequestParam(value = "pageNum", required = false) Integer pageNum,
            @RequestParam("pageSize") Integer pageSize,
            @CurrentUser @Parameter(hidden = true) User user, HttpServletRequest request) throws JsonProcessingException {
        if (pageSize > 50) {
            return R.fail(400, "pageSize exceeds 50 !");
        }
        String platform = genService.getPlatform(request);
        if (StringUtil.isBlank(platform)) {
            return R.fail(400, "Illegal request !");
        }
        Boolean notWeb = !"web".equals(platform);
        return R.success(imgService.getRandomList(pageNum, pageSize, user, notWeb));
    }

    @Operation(summary = "查询具体图片信息")
    @PostMapping("/img-detail")
    public R<FileDetailResult> getImgDetail(
            @RequestParam("imgName") @Parameter(description = "图片名称") String imgName,
            @RequestParam("promptId") String promptId,
            @RequestParam(value = "loginName", required = false, defaultValue = "") String loginName, HttpServletRequest request) throws IOException {

        String platform = genService.getPlatform(request);
        if (StringUtil.isBlank(platform)) {
            return R.fail(400, "Illegal request !");
        }
        Boolean notWeb = !"web".equals(platform);
        return R.success(imgService.getImgDetail(imgName, promptId, loginName, notWeb));
    }

    @Operation(summary = "用户点赞")
    @PostMapping("/add-like")
    @Authorization
    public R<Boolean> addLike(
            @RequestParam("imgName") @Parameter(description = "图片名称") String imgName,
            @RequestParam("promptId") String promptId,
            @RequestParam("loginName") String loginName,
            @CurrentUser @Parameter(hidden = true) User user) throws JsonProcessingException {
        if (!Objects.isNull(imgService.getUserLike(imgName, promptId, user.getLoginName()))) {
            return R.fail(400, "Do not like repeatedly !");
        }
        Boolean result = imgService.addLike(imgName, promptId, loginName, user);
        return result ? R.success(result) : R.fail(1, "Failed");
    }

    @Operation(summary = "用户取消点赞")
    @PostMapping("/reduce-like")
    @Authorization
    public R<Boolean> reduceLike(
            @RequestParam("imgName") @Parameter(description = "图片名称") String imgName,
            @RequestParam("promptId") String promptId,
            @RequestParam("loginName") String loginName,
            @CurrentUser @Parameter(hidden = true) User user) throws JsonProcessingException {
        UserLike userLike = imgService.getUserLike(imgName, promptId, user.getLoginName());
        if (Objects.isNull(userLike)) {
            return R.fail(400, "Please like first !");
        }
        Boolean result = imgService.reduceLike(userLike, loginName, user.getLoginName());
        return result ? R.success(result) : R.fail(1, "Failed");
    }


    @Operation(summary = "用户批量删除图片")
    @PostMapping("/batch-deletes-img")
    @Authorization
    public R<String> deleteByImgDeleteList(
            @RequestBody List<ImgDelete> imgDeleteList,
            @CurrentUser @Parameter(hidden = true) User user) {
        if (CollectionUtils.isEmpty(imgDeleteList)) {
            return R.fail(400, "Please select the picture to be deleted !");
        }
        if (imgDeleteList.size() > 40) {
            return R.fail(400, "Please select no more than 40 pictures to be deleted !");
        }
        boolean result = imgService.deleteByList(imgDeleteList, user);
        return result ? R.success("") : R.fail(1, "Failed");
    }

    @Operation(summary = "查询用户当前生图统计信息")
    @PostMapping("/statistics-img-nums")
    @Authorization
    public R<StatisticsImgNums> getStatisticsUserImg(
            @CurrentUser @Parameter(hidden = true) User user) throws JsonProcessingException {
        return R.success(imgService.getStatisticsUserImg(user));
    }


    @Operation(summary = "未登录分页查询")
    @PostMapping("/common-explore-list")
    public R<PromptPageInfo<HomeFileResult>> getCommonExploreList(
            @RequestParam("pageNum") Integer pageNum,
            @RequestParam("pageSize") Integer pageSize,
            @RequestParam(value = "collationName", required = false) @Parameter(description = "排序规则名称：latest : 生成时间 hot : 点赞数")
            String collationName, HttpServletRequest request) throws JsonProcessingException {
        if (pageSize > 20) {
            return R.fail(400, "pageSize exceeds 20 !");
        }
        if (pageNum > 10) {
            return R.fail(400, "pageNum exceeds 10 !");
        }
        String platform = genService.getPlatform(request);
        if (StringUtil.isBlank(platform)) {
            return R.fail(400, "Illegal request !");
        }
        Boolean notWeb = !"web".equals(platform);
        return R.success(imgService.getCommonExploreList(pageNum, pageSize, collationName, notWeb));
    }

    @Operation(summary = "用户举报")
    @PostMapping("/add-report")
    @Authorization
    public R<Boolean> addReport(
            @RequestBody UserAddReportDto userAddReportDto,
            @CurrentUser @Parameter(hidden = true) User user) {
        // 非空校验
        if (StringUtil.isBlank(userAddReportDto.getImgName())
                || StringUtil.isBlank(userAddReportDto.getPromptId())
                || ObjectUtils.isEmpty(userAddReportDto.getAuditType())) {
            return R.fail(400, "Image name and prompt ID are required.");
        }

        boolean result = imgService.addReport(userAddReportDto, user);
        return result ? R.success(result) : R.fail(1, "Failed");
    }


    @Operation(summary = "公开图片")
    @PostMapping("/public-img")
    @Authorization
    public R<Boolean> publicUserImg(@RequestParam("imgName") @Parameter(description = "图片名称") String imgName,
                                    @RequestParam("brief") @Parameter(description = "简要") String brief,
                                    @RequestParam("publicType") @Parameter(description = "公开类型everyone:所有人可见,myself:自己可见") String publicType,
                                    @RequestParam(value = "activityId", required = false) @Parameter(description = "活动id") String activityId,
                                    @RequestParam("promptId") String promptId, @CurrentUser @Parameter(hidden = true) User user) {
        if (imgService.getTodayPublicImgNums(user.getLoginName()) > 50) {
            return R.fail(400, "The total number of pictures posted in one day cannot exceed 50");
        }
        Boolean result = imgService.publicUserImg(imgName, promptId, brief, publicType, user,activityId);
        return result ? R.success(result) : R.fail(1, "Failed");
    }

    @Operation(summary = "今日能公开图片剩余数量")
    @GetMapping("/public-img-surplus-nums")
    @Authorization
    public R<Integer> publicUserImgSurplusNums(@CurrentUser @Parameter(hidden = true) User user) {
        return R.success(imgService.publicUserImgSurplusNums(user));
    }

    @Operation(summary = "查询当前用户某个任务的图片")
    @GetMapping("/select-file-record")
    @Authorization
    public R<PromptFileRecord> getGenPromptFileRecord(@RequestParam("promptId") String promptId,
                                                      @CurrentUser @Parameter(hidden = true) User user, HttpServletRequest request) throws JsonProcessingException {
        String platform = genService.getPlatform(request);
        if (StringUtil.isBlank(platform)) {
            return R.fail(400, "Illegal request !");
        }
        return R.success(imgService.getGenPromptFileRecord(promptId, user));
    }


}
