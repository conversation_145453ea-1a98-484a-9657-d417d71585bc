package com.lx.pl.controller.tcloud;


import com.lx.pl.annotation.Authorization;
import com.lx.pl.annotation.CurrentUser;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.dto.generic.R;
import com.lx.pl.dto.tcloud.AuthParam;
import com.lx.pl.dto.tcloud.AuthVo;
import com.lx.pl.enums.UploadType;
import com.lx.pl.service.tcloud.ITencentCloudService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 腾讯COS临时token
 *
 * <AUTHOR>
 */
@Slf4j
@Tag(name = "腾讯COS临时token")
@RestController
@RequestMapping("/api/bucket")
public class TencentCloudController {

    @Autowired
    private ITencentCloudService tencentCloudService;

    @PostMapping("/tmp-auth")
    @Operation(summary = "获取临时COS token")
    @Authorization
    public R<AuthVo> tmpAuth(@Parameter(hidden = true) @CurrentUser User user, @RequestBody @Validated AuthParam param) {
        if (param.getType() == null) {
            param.setType(UploadType.normal.getValue());
        }
        return R.success(tencentCloudService.getUploadAuth(param, user));
    }

}
