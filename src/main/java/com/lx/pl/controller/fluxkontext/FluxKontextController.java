package com.lx.pl.controller.fluxkontext;

import com.lx.pl.annotation.Authorization;
import com.lx.pl.annotation.CurrentUser;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.dto.fluxkontext.FluxKontextResponse;
import com.lx.pl.dto.gen.GenGenericPara;
import com.lx.pl.enums.LogicErrorCode;
import com.lx.pl.exception.LogicException;
import com.lx.pl.service.FluxKontextService;
import com.lx.pl.service.PromptFiltrationService;
import com.lx.pl.service.VipService;
import com.lx.pl.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotBlank;
import java.io.IOException;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Flux Kontext Pro API控制器
 *
 * <AUTHOR>
 */
@Slf4j
@Tag(name = "Flux Kontext Pro API")
@RestController
@RequestMapping("/api/flux-kontext")
@Validated
public class FluxKontextController {

    @Value("${bad.words.filter}")
    Boolean badWordsFilter;

    @Autowired
    private FluxKontextService fluxKontextService;

    @Autowired
    private PromptFiltrationService promptFiltrationService;

    @Autowired
    private VipService vipStandardsService;

    /**
     * 文本生图请求参数
     */
    @Data
    public static class TextToImageParams {
        @NotBlank(message = "提示词不能为空")
        private String prompt;

        private String aspectRatio = "1:1"; // 宽高比

        private Integer seed; // 随机种子

        private Boolean enablePromptCheck = true; // 是否启用prompt检查
    }

    /**
     * 图像编辑请求参数
     */
    @Data
    public static class ImageEditParams {
        @NotBlank(message = "提示词不能为空")
        private String prompt;

        @NotBlank(message = "输入图像不能为空")
        private String inputImage; // Base64编码的图像或图像URL

        private String aspectRatio = "1:1"; // 宽高比

        private Integer seed; // 随机种子

        private Boolean enablePromptCheck = true; // 是否启用prompt检查
    }

    /**
     * 文本生图
     */
    @PostMapping("/text-to-image")
    @Operation(summary = "文本生图")
    @Authorization
    public R<FluxKontextResponse.BaseResponseData> textToImage(
            @Parameter(hidden = true) @CurrentUser User user,
            @RequestBody @Validated TextToImageParams params) {

        log.info("Flux Kontext text-to-image request from user: {}, prompt: {}, enablePromptCheck: {}",
                user.getLoginName(), params.getPrompt(), params.getEnablePromptCheck());

        // 检查并发任务数限制
        if (fluxKontextService.checkFluxKontextConcurrentJobs(user, null)) {
            throw new LogicException(LogicErrorCode.EXCEED_CONCURRENT_JOBS);
        }

        // 检查用户权限
        Boolean operatePermission = vipStandardsService.judgeUserOperatePermission(user, 10);
        if (!operatePermission) {
            throw new LogicException(LogicErrorCode.NOT_ENOUGH_LUMENS_SUPPORT);
        }

        FluxKontextResponse.BaseResponseData result = fluxKontextService.textToImage(
                params.getPrompt(),
                user,
                params.getAspectRatio(),
                params.getSeed(),
                params.getEnablePromptCheck() != null ? params.getEnablePromptCheck() : true
        );

        return R.success(result);
    }

    /**
     * 图像编辑
     */
    @PostMapping("/image-edit")
    @Operation(summary = "图像编辑")
    @Authorization
    public R<FluxKontextResponse.BaseResponseData> imageEdit(
            @Parameter(hidden = true) @CurrentUser User user,
            @RequestBody @Validated ImageEditParams params) {

        log.info("Flux Kontext image-edit request from user: {}, prompt: {}, enablePromptCheck: {}",
                user.getLoginName(), params.getPrompt(), params.getEnablePromptCheck());

        // 检查并发任务数限制
        if (fluxKontextService.checkFluxKontextConcurrentJobs(user, null)) {
            throw new LogicException(LogicErrorCode.EXCEED_CONCURRENT_JOBS);
        }

        // 检查用户权限
        Boolean operatePermission = vipStandardsService.judgeUserOperatePermission(user, 15);
        if (!operatePermission) {
            throw new LogicException(LogicErrorCode.NOT_ENOUGH_LUMENS_SUPPORT);
        }

        // 处理输入图像（如果是URL，需要转换为Base64）
        String inputImageBase64 = processInputImage(params.getInputImage());

        FluxKontextResponse.BaseResponseData result = fluxKontextService.imageEdit(
                params.getPrompt(),
                inputImageBase64,
                user,
                params.getAspectRatio(),
                params.getSeed(),
                params.getEnablePromptCheck() != null ? params.getEnablePromptCheck() : true
        );

        return R.success(result);
    }

    /**
     * 兼容原GenController的create接口
     * 接收GenGenericPara格式的请求，转换为Flux Kontext API调用
     */
    @PostMapping("/create")
    @Operation(summary = "图片生成（兼容原接口）")
    @Authorization
    public R<Map<String, Object>> createCompatible(
            @Parameter(hidden = true) @CurrentUser User user,
            @RequestBody GenGenericPara genParameters,
            HttpServletRequest request) throws IOException {

        log.info("Flux Kontext compatible create request from user: {}, prompt: {}",
                user.getLoginName(), genParameters.getPrompt());

        // 基础参数验证
        if (genParameters.getPrompt() == null) {
            genParameters.setPrompt("");
        }

        // Prompt过滤验证
        Boolean filterChildSex = promptFiltrationService.filterChildSex(genParameters.getPrompt());
        if (filterChildSex) {
            throw new LogicException(LogicErrorCode.ILLEGAL_PROMPT);
        }

        // 检查并发任务数限制
        String markId = UUID.randomUUID().toString();
        if (fluxKontextService.checkFluxKontextConcurrentJobs(user, markId)) {
            throw new LogicException(LogicErrorCode.EXCEED_CONCURRENT_JOBS);
        }

        // 检查用户权限
        Boolean operatePermission = vipStandardsService.judgeUserOperatePermission(user, 10);
        if (!operatePermission) {
            throw new LogicException(LogicErrorCode.NOT_ENOUGH_LUMENS_SUPPORT);
        }

        // 转换宽高比
        String aspectRatio = convertToAspectRatio(genParameters.getResolution());

        // 判断是文本生图还是图像编辑
        FluxKontextResponse.BaseResponseData result;
        if (genParameters.getImg2imgPara() != null &&
            genParameters.getImg2imgPara().getImg_url() != null) {
            // 图像编辑
            String inputImageBase64 = processInputImage(genParameters.getImg2imgPara().getImg_url());
            result = fluxKontextService.imageEdit(
                    genParameters.getPrompt(),
                    inputImageBase64,
                    user,
                    aspectRatio,
                    null, // seed
                    true, // enablePromptCheck
                    markId,
                    true, // fastHour
                    getPlatform(request),
                    genParameters
            );
        } else {
            // 文本生图
            result = fluxKontextService.textToImage(
                    genParameters.getPrompt(),
                    user,
                    aspectRatio,
                    null, // seed
                    true, // enablePromptCheck
                    markId,
                    true, // fastHour
                    getPlatform(request),
                    genParameters
            );
        }

        // 构建兼容的返回格式
        Map<String, Object> response = new HashMap<>();
        response.put("prompt_id", result.getId());
        response.put("status", "processing");
        response.put("message", "Task submitted successfully");

        return R.success(response);
    }

    /**
     * 获取任务状态
     */
    @GetMapping("/status/{requestId}")
    @Operation(summary = "获取任务状态")
    @Authorization
    public R<FluxKontextResponse.TaskResultResponse> getTaskStatus(
            @Parameter(hidden = true) @CurrentUser User user,
            @PathVariable String requestId) {

        FluxKontextResponse.TaskResultResponse result = fluxKontextService.getTaskStatus(requestId);

        if (result == null) {
            return R.fail(404, "Task not found");
        }

        return R.success(result);
    }

    /**
     * 处理输入图像（URL转Base64或验证Base64格式）
     */
    private String processInputImage(String inputImage) {
        if (inputImage == null || inputImage.trim().isEmpty()) {
            throw new LogicException(LogicErrorCode.INVALID_PARAMETER, "输入图像不能为空");
        }

        // 如果已经是Base64格式，直接返回
        if (inputImage.startsWith("data:image/") || isBase64(inputImage)) {
            return inputImage;
        }

        // 如果是URL，需要下载并转换为Base64
        if (inputImage.startsWith("http://") || inputImage.startsWith("https://")) {
            try {
                return convertUrlToBase64(inputImage);
            } catch (Exception e) {
                log.error("Failed to convert URL to Base64: " + inputImage, e);
                throw new LogicException(LogicErrorCode.INVALID_PARAMETER, "无法处理输入图像URL");
            }
        }

        throw new LogicException(LogicErrorCode.INVALID_PARAMETER, "输入图像格式不支持");
    }

    /**
     * 检查是否为Base64格式
     */
    private boolean isBase64(String str) {
        try {
            Base64.getDecoder().decode(str);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }

    /**
     * 将URL转换为Base64
     */
    private String convertUrlToBase64(String imageUrl) throws IOException {
        // 这里应该实现URL到Base64的转换逻辑
        // 可以使用现有的图像处理服务
        throw new UnsupportedOperationException("URL to Base64 conversion not implemented yet");
    }

    /**
     * 转换分辨率为宽高比
     */
    private String convertToAspectRatio(GenGenericPara.Resolution resolution) {
        if (resolution == null) {
            return "1:1";
        }

        int width = resolution.getWidth();
        int height = resolution.getHeight();

        // 计算最大公约数
        int gcd = gcd(width, height);
        int ratioWidth = width / gcd;
        int ratioHeight = height / gcd;

        return ratioWidth + ":" + ratioHeight;
    }

    /**
     * 计算最大公约数
     */
    private int gcd(int a, int b) {
        return b == 0 ? a : gcd(b, a % b);
    }

    /**
     * 获取平台信息
     */
    private String getPlatform(HttpServletRequest request) {
        // 这里可以根据请求头或其他信息判断平台
        String userAgent = request.getHeader("User-Agent");
        if (userAgent != null) {
            if (userAgent.contains("Android")) {
                return "android";
            } else if (userAgent.contains("iPhone") || userAgent.contains("iPad")) {
                return "ios";
            }
        }
        return "web";
    }
}