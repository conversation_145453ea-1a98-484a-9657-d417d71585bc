package com.lx.pl.controller;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.lx.pl.annotation.Authorization;
import com.lx.pl.annotation.CurrentUser;
import com.lx.pl.annotation.LeakyBucketLimit;
import com.lx.pl.db.mysql.gen.entity.ImgControl;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.dto.GenGenericPara;
import com.lx.pl.dto.ImgControlSaveRequest;
import com.lx.pl.dto.PromptFileRecord;
import com.lx.pl.dto.PromptPageInfo;
import com.lx.pl.dto.generic.R;
import com.lx.pl.service.GenService;
import com.lx.pl.service.ImgControlService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.List;
import java.util.Objects;

@Tag(name = "图片控制相关接口")
@Slf4j
@RestController
@RequestMapping("/api/img-control")
public class ImgControlController {

    @Autowired
    private ImgControlService imgControlService;

    @Autowired
    private GenService genService;

    @Operation(summary = "提取图片的内容控制")
    @PostMapping("/extract")
    @Authorization
    @LeakyBucketLimit(intervalSeconds = 4, queueSize = 15)
    public R<String> extractImg(
            @RequestParam("imgUrl") String imgUrl,
            @RequestParam("controlType") String controlType,
            @CurrentUser @Parameter(hidden = true) User user, HttpServletRequest request) throws IOException {

        String platform = genService.getPlatform(request);
        if (StringUtil.isBlank(platform)) {
            return R.fail(400, "Illegal request !");
        }

        if (StringUtil.isBlank(imgUrl) || StringUtil.isBlank(controlType)) {
            return R.fail(400, "Parameter cannot be empty !");
        }

        return R.success(imgControlService.extractImg(imgUrl, controlType, user));
    }

    @Operation(summary = "保存个人图片控制")
    @PostMapping("/save")
    @Authorization
    public R<Boolean> saveImgControl(
            @RequestBody ImgControlSaveRequest imgControlSaveRequest,
            @CurrentUser @Parameter(hidden = true) User user, HttpServletRequest request) {

        String platform = genService.getPlatform(request);
        if (StringUtil.isBlank(platform)) {
            return R.fail(400, "Illegal request !");
        }

        if (StringUtil.isBlank(imgControlSaveRequest.getImgUrl()) || StringUtil.isBlank(imgControlSaveRequest.getControlType())) {
            return R.fail(400, "Parameter cannot be empty !");
        }

        int imgControlNums = imgControlService.getImgControlNums(imgControlSaveRequest.getControlType(), user);
        //用户已经达到最大的图片控制上传数
        if (imgControlNums > 10) {
            return R.fail(400, "You have reached the maximum number of uploads!");
        }

        return R.success(imgControlService.saveImgControl(imgControlSaveRequest, user));
    }

    @Operation(summary = "删除个人图片控制")
    @PostMapping("/delete")
    @Authorization
    public R<Boolean> deleteImgControl(
            @RequestParam("id") Long id,
            @RequestParam("imgType") String imgType,
            @CurrentUser @Parameter(hidden = true) User user, HttpServletRequest request) throws IOException {

        String platform = genService.getPlatform(request);
        if (StringUtil.isBlank(platform)) {
            return R.fail(400, "Illegal request !");
        }

        if (Objects.isNull(id) || StringUtil.isBlank(imgType)) {
            return R.fail(400, "Parameter cannot be empty !");
        }

        ImgControl imgControl = imgControlService.getImgControl(id, user);
        if (Objects.isNull(imgControl)) {
            return R.fail(400, "Image does not exist !");
        }

        return R.success(imgControlService.deleteImgControl(imgControl, user));
    }


    @Operation(summary = "查询个人图片控制")
    @PostMapping("/select-list")
    @Authorization
    public R<List<ImgControl>> selectImgControlList(
            @RequestParam("controlType") String controlType,
            @CurrentUser @Parameter(hidden = true) User user, HttpServletRequest request) throws IOException {

        String platform = genService.getPlatform(request);
        if (StringUtil.isBlank(platform)) {
            return R.fail(400, "Illegal request !");
        }

        if (StringUtil.isBlank(controlType)) {
            return R.fail(400, "Parameter cannot be empty !");
        }

        return R.success(imgControlService.selectImgControlList(controlType, user));
    }


}
