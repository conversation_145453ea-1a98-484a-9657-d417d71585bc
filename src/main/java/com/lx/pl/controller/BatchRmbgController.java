package com.lx.pl.controller;

import cn.hutool.core.util.StrUtil;
import com.lx.pl.annotation.Authorization;
import com.lx.pl.annotation.CurrentUser;
import com.lx.pl.db.mysql.gen.entity.ToolRmbgResult;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.dto.ToolRmbgParam;
import com.lx.pl.dto.generic.R;
import com.lx.pl.dto.tcloud.AuthParam;
import com.lx.pl.dto.tcloud.AuthVo;
import com.lx.pl.enums.LogicErrorCode;
import com.lx.pl.enums.UploadType;
import com.lx.pl.exception.LogicException;
import com.lx.pl.service.GenService;
import com.lx.pl.service.ToolRmbgResultService;
import com.lx.pl.service.tcloud.ITencentCloudService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Objects;

/**
 * tools批量去背景相关功能
 *
 * <AUTHOR>
 */
@Slf4j
@Tag(name = "tools批量去背景相关功能")
@RestController
@RequestMapping("/api/rmbg")
public class BatchRmbgController {

    @Autowired
    private ToolRmbgResultService toolRmbgResultService;

    @Autowired
    GenService genService;
    @Autowired
    private ITencentCloudService tencentCloudService;

    @PostMapping("/saveRmbg")
    @Operation(summary = "保存去背景用户上传")
    @Authorization
    public R<ToolRmbgResult> saveRmbgFile(@Parameter(hidden = true) @CurrentUser User user, @RequestBody ToolRmbgParam param, HttpServletRequest request) {
        if (Objects.isNull(param) || StrUtil.isEmpty(param.getFileUrl())) {
            return R.fail(400, "A small system hiccup. Please try again later.");
        }
        //任务数和点数校验
        LogicErrorCode errorResult = toolRmbgResultService.passVerify(user, param.getBatchId());
        if (errorResult != null) {
            throw new LogicException(errorResult);
        }

        return R.success(toolRmbgResultService.saveRmbgFile(param, user, genService.getPlatform(request)));
    }

    @PostMapping("/rmbg-status")
    @Operation(summary = "查询状态")
    @Authorization
    public R<List<ToolRmbgResult>> getRmbgResultStatus(@Parameter(hidden = true) @CurrentUser User user, @RequestBody List<Long> ids, HttpServletRequest request) {
        //ids参数判断
        if (ids == null || ids.isEmpty()) {
            return R.fail(400, "A small system hiccup. Please try again later.");
        }
        //通过ids批量查询
        List<ToolRmbgResult> rmbgResultStatus = toolRmbgResultService.getRmbgResultStatus(ids);
        return R.success(rmbgResultStatus);
    }

    @Operation(summary = "查询当前用户生成的图片")
    @PostMapping("/rmbg-history")
    @Authorization
    public R<List<ToolRmbgResult>> listHistory(
            @CurrentUser @Parameter(hidden = true) User user, HttpServletRequest request) {

        List<ToolRmbgResult> rmbgHistory = toolRmbgResultService.getRmbgHistory(user);

        return R.success(rmbgHistory);
    }

    @Operation(summary = "删除生成的图片")
    @PostMapping("/rmbg-del")
    @Authorization
    public R<List<ToolRmbgResult>> deleteHistory(
            @CurrentUser @Parameter(hidden = true) User user, @RequestBody List<Long> ids, HttpServletRequest request) {
        toolRmbgResultService.deleteBatchByIds(user, ids);
        return R.success();
    }

    @PostMapping("/batch-rmbg-tmp-auth")
    @Operation(summary = "批量去背景获取临时COS token")
    @Authorization
    public R<AuthVo> batchRmbgTmpAuth(@Parameter(hidden = true) @CurrentUser User user, @RequestBody @Validated AuthParam param) {
        //设置上传类型
        param.setType(UploadType.BATCH_RMBG.getValue());
        //任务数和点数校验
        LogicErrorCode errorResult = toolRmbgResultService.passVerify(user, param.getBatchId());
        if (errorResult != null) {
            throw new LogicException(errorResult);
        }
        return R.success(tencentCloudService.getUploadAuth(param, user));
    }
}
