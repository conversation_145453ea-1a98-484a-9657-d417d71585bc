package com.lx.pl.controller;

import com.lx.pl.annotation.Authorization;
import com.lx.pl.db.mysql.gen.entity.Dict;
import com.lx.pl.dto.generic.R;
import com.lx.pl.service.DictService;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "字典接口")
@Hidden
@RestController
@RequestMapping("/api/dict")
public class DictController {

    @Autowired
    DictService dictService;

    @Operation(summary = "删除字典内容")
    @DeleteMapping("/delete/{id}")
    @Authorization
    public R<String> delete(@PathVariable("id") Long id) {
        int result = dictService.deleteById(id);
        return result > 0 ? R.success("") : R.fail(500, "failed to delete！");
    }

    @Operation(summary = "通过id查询字典内容")
    @GetMapping("/get/{id}")
    public R<Dict> getDict(@PathVariable("id") Long id) {
        Dict dict = dictService.getDictById(id);
        return R.success(dict);
    }

    @Operation(summary = "分页查询所有字典内容")
    @GetMapping("/list/{pageNum}/{pageSize}")
    public R<List<Dict>> getDictList(@PathVariable("pageNum") Integer pageNum,
                                     @PathVariable("pageSize") Integer pageSize) {
        return R.success(dictService.listDicts(pageNum, pageSize));
    }

    @Operation(summary = "更新某条字典")
    @PostMapping("/update")
    @Authorization
    public R<String> updateById(@RequestBody Dict dict) {
        int result = dictService.updateById(dict);
        return result > 0 ? R.success("") : R.fail(500, "failed to update！");
    }

    @Operation(summary = "更新某条字典")
    @PostMapping("/update-by-key")
    @Authorization
    public R<String> updateByKey(@RequestBody Dict dict) {
        int result = dictService.updateByKey(dict);
        return result > 0 ? R.success("") : R.fail(500, "failed to update！");
    }

    @Operation(summary = "通过key查询字典内容")
    @GetMapping("/get/key/{key}")
    public R<List<Dict>> getDictById(@PathVariable("key") String key) {
        return R.success(dictService.getDictByKey(key));
    }
}
