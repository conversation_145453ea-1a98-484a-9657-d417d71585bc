package com.lx.pl.controller;

import com.lx.pl.enums.ClientType;
import com.lx.pl.enums.PushMessageReportType;
import com.lx.pl.service.message.PushNotifyService;
import com.lx.pl.vo.PushMessageReportReq;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/5/12
 * @description
 */
@Tag(name = "PUSH/站内信相关接口")
@Slf4j
@RestController
@RequestMapping("/api/message")
public class MessageController {
    @Resource
    private PushNotifyService pushNotifyService;

    @Operation(summary = "PUSH打开消息上报")
    @PostMapping("push-open-report")
    public void pushOpenReport(@RequestBody @Valid PushMessageReportReq req, HttpServletRequest request) {
        if (Objects.isNull(PushMessageReportType.getByCode(req.getMessageType()))) {
            log.error("未找到消息上报类型, messageType: {}", req.getMessageType());
            return;
        }
        String platform = request.getHeader("platform");
        if (!ClientType.mobilePlatform(platform)) {
            log.error("非移动端上报不处理, platform: {}", platform);
            return;
        }
        pushNotifyService.pushOpenReport(req);
    }
}
