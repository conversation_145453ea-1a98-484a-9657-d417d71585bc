package com.lx.pl.controller;

import com.lx.pl.service.GeoIpCityService;
import com.lx.pl.service.GeoIpCountryService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Optional;

@RestController
public class GeoIpController {

    @Resource
    private GeoIpCityService geoIpCityService;
    @Resource
    private GeoIpCountryService geoIpCountryService;

    //暂时不需要，已经删除了对应库，如果需要需要将GeoLite2-City.mmdb导入进来
//    @GetMapping("/geoCity")
//    public String getCityByIp(@RequestParam String ip) {
//        Optional<String> city = geoIpCityService.getCity(ip);
//        return city.orElse("City not found");
//    }

    @GetMapping("/geoCountry")
    public String getCountryByIp(@RequestParam String ip) {
        Optional<String> country = geoIpCountryService.getCountry(ip);
        return country.orElse("Country not found");
    }
}
