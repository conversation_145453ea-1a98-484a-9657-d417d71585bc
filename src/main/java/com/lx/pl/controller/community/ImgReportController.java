package com.lx.pl.controller.community;

import com.lx.pl.annotation.Authorization;
import com.lx.pl.annotation.CurrentUser;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.dto.generic.R;
import com.lx.pl.service.CommImgReportService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

@Tag(name = "社区图片举报相关接口")
@Slf4j
@RestController
@RequestMapping("/api/comm-img-report")
public class ImgReportController {

    @Autowired
    private CommImgReportService commImgReportService;

    @Operation(summary = "对社区图片进行举报")
    @PostMapping("/report-img")
    @Authorization
    public R<Boolean> reportCommImg(@RequestParam("commFileId") @Parameter(description = "社区图片id") String commFileId,
                                    @RequestParam("auditType") @Parameter(description = "举报原因类型") Integer auditType,
                                    @RequestParam(value = "otherContent", required = false) @Parameter(description = "其他原因具体内容") String otherContent,
                                    @CurrentUser @Parameter(hidden = true) User user) {
        if (!Objects.isNull(commImgReportService.getCommReportByUserIdAndFileId(user.getId(), commFileId))) {
            return R.fail(400, "Do not report repeatedly !");
        }
        Boolean result = commImgReportService.reportCommImg(commFileId, auditType, otherContent, user);
        return result ? R.success(result) : R.fail(1, "img does not exist");
    }

}
