package com.lx.pl.controller.community;

import com.lx.pl.annotation.Authorization;
import com.lx.pl.annotation.CurrentUser;
import com.lx.pl.db.mysql.community.entity.CommComment;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.dto.CommPageInfo;
import com.lx.pl.dto.PromptPageInfo;
import com.lx.pl.dto.generic.R;
import com.lx.pl.service.CommCommentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "社区评论相关接口")
@Slf4j
@RestController
@RequestMapping("/api/comm-comment")
public class CommCommentController {

    @Autowired
    private CommCommentService commCommentService;

    @Operation(summary = "对社区图片进行评论")
    @PostMapping("/add-comment-file")
    @Authorization
    public R<Boolean> addCommentCommFile(@RequestParam("commFileId") @Parameter(description = "社区图片id") String commFileId,
                                         @RequestParam("content") @Parameter(description = "评论内容") String content,
                                         @CurrentUser @Parameter(hidden = true) User user) {
        Boolean result = commCommentService.addCommentCommFile(commFileId, content, user);
        return result ? R.success(result) : R.fail(1, "img does not exist");
    }

    @Operation(summary = "删除图片的评论")
    @PostMapping("/delete-comment-file")
    @Authorization
    public R<Boolean> deleteCommentCommFile(@RequestParam("commFileId") @Parameter(description = "社区图片id") String commFileId,
                                            @RequestParam("commentId") @Parameter(description = "评论id") String commentId,
                                            @CurrentUser @Parameter(hidden = true) User user) {
        Boolean result = commCommentService.deleteCommentCommFile(commFileId, commentId, user);
        return result ? R.success(result) : R.fail(1, "Failed");
    }

    @Operation(summary = "对社区评论进行评论")
    @PostMapping("/add-comment-comment")
    @Authorization
    public R<Boolean> addCommentCommComment(@RequestParam("commFileId") @Parameter(description = "社区图片id") String commFileId,
                                            @RequestParam("firstCommentId") @Parameter(description = "顶级评论id") String firstCommentId,
                                            @RequestParam("commentId") @Parameter(description = "目标评论id") String commentId,
                                            @RequestParam("content") @Parameter(description = "评论内容") String content,
                                            @CurrentUser @Parameter(hidden = true) User user) {
        Boolean result = commCommentService.addCommentCommComment(commFileId, firstCommentId, commentId, content, user);
        return result ? R.success(result) : R.fail(1, "Failed");
    }

    @Operation(summary = "删除评论的评论")
    @PostMapping("/delete-comment-comment")
    @Authorization
    public R<Boolean> deleteCommentCommComment(@RequestParam("commentId") @Parameter(description = "评论id") String commentId,
                                               @CurrentUser @Parameter(hidden = true) User user) {
        Boolean result = commCommentService.deleteCommentCommComment(commentId, user);
        return result ? R.success(result) : R.fail(1, "Failed");
    }

    @Operation(summary = "获取社区评论列表")
    @GetMapping("/select-comment")
    @Authorization
    public R<CommPageInfo<CommComment>> getCommCommentList(@RequestParam(value =  "lastCommentId" , required = false) String lastCommentId,
                                                           @RequestParam("pageSize") Integer pageSize,
                                                           @RequestParam("commFileId") @Parameter(description = "社区图片id") String commFileId,
                                                           @RequestParam(value = "firstCommentId", required = false) @Parameter(description = "社区一级评论id") String firstCommentId,
                                                           @CurrentUser @Parameter(hidden = true) User user) {
        if (pageSize > 50) {
            return R.fail(400, "pageSize exceeds 50 !");
        }
        return R.success(commCommentService.getCommCommentList(lastCommentId, pageSize, commFileId, firstCommentId, user));
    }
}
