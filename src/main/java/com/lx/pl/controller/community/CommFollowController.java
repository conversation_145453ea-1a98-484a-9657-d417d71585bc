package com.lx.pl.controller.community;

import com.lx.pl.annotation.Authorization;
import com.lx.pl.annotation.CurrentUser;
import com.lx.pl.db.mysql.community.dto.CommFollowWithStats;
import com.lx.pl.db.mysql.community.entity.CommFollow;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.dto.CommPageInfo;
import com.lx.pl.dto.PromptPageInfo;
import com.lx.pl.dto.generic.R;
import com.lx.pl.service.CommFollowService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "社区关注相关接口")
@Slf4j
@RestController
@RequestMapping("/api/comm-follow")
public class CommFollowController {

    @Autowired
    private CommFollowService commFollowService;

    @Operation(summary = "关注用户")
    @PostMapping("/add-follow")
    @Authorization
    public R<Boolean> addCommFollow(@RequestParam("userId") @Parameter(description = "关注用户的id") Long userId,
                                    @CurrentUser @Parameter(hidden = true) User user) {
        Boolean result = commFollowService.addCommFollow(userId, user);
        return result ? R.success(result) : R.fail(1, "Please refresh the page to update the status before proceeding.");
    }

    @Operation(summary = "取消关注用户")
    @PostMapping("/reduce-follow")
    @Authorization
    public R<Boolean> reduceCommFollow(@RequestParam("userId") @Parameter(description = "关注目标用户的id") Long userId,
                                       @CurrentUser @Parameter(hidden = true) User user) {
        Boolean result = commFollowService.reduceCommFollow(userId, user);
        return result ? R.success(result) : R.fail(1, "Please refresh the page to update the status before proceeding.");
    }

    @Operation(summary = "获取用户关注或者粉丝列表")
    @GetMapping("/select-follow")
    @Authorization
    public R<CommPageInfo<CommFollow>> getCommFollowList(@RequestParam(value = "lastFollowId", required = false) String lastFollowId,
                                                         @RequestParam("pageSize") Integer pageSize,
                                                         @RequestParam("selectType") @Parameter(description = "selectType : follow : 关注列表 fans : 粉丝列表") String selectType,
                                                         @CurrentUser @Parameter(hidden = true) User user) {
        if (pageSize > 50) {
            return R.fail(400, "pageSize exceeds 50 !");
        }
        return R.success(commFollowService.getCommFollowList(lastFollowId, pageSize, selectType, user));
    }

    @Operation(summary = "获取用户关注或者粉丝列表V2")
    @GetMapping("/select-followV2")
    public R<CommPageInfo<CommFollowWithStats>> getCommFollowListWithStats(@RequestParam(value = "lastFollowId", required = false) String lastFollowId,
                                                                           @RequestParam("pageSize") Integer pageSize,
                                                                           @RequestParam("selectType") @Parameter(description = "selectType : follow : 关注列表 fans : 粉丝列表") String selectType,
                                                                           @CurrentUser @Parameter(hidden = true) User user) {
        if (pageSize > 50) {
            return R.fail(400, "pageSize exceeds 50 !");
        }
        return R.success(commFollowService.getCommFollowListWithStats(lastFollowId, pageSize, selectType, user));
    }

    @Operation(summary = "查询当前用户是否被关注")
    @GetMapping("/judge-follow")
    @Authorization
    public R<Boolean> judgeTargetUserFollow(@RequestParam("userId") @Parameter(description = "关注目标用户的id") Long userId,
                                            @CurrentUser @Parameter(hidden = true) User user) {
        Boolean result = commFollowService.judgeTargetUserFollow(userId, user);
        return R.success(result);
    }
}
