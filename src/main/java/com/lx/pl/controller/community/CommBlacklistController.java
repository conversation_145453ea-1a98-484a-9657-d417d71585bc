package com.lx.pl.controller.community;

import com.lx.pl.annotation.Authorization;
import com.lx.pl.annotation.CurrentUser;
import com.lx.pl.db.mysql.community.entity.CommBlacklist;
import com.lx.pl.db.mysql.community.entity.CommFollow;
import com.lx.pl.db.mysql.community.entity.CommLike;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.dto.CommPageInfo;
import com.lx.pl.dto.generic.R;
import com.lx.pl.service.CommBlacklistService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

@Tag(name = "社区用户拉黑相关接口")
@Slf4j
@RestController
@RequestMapping("/api/comm-blacklist")
public class CommBlacklistController {

    @Autowired
    CommBlacklistService commBlacklistService;


    @Operation(summary = "拉黑用户")
    @PostMapping("/add-blacklist")
    @Authorization
    public R<Boolean> addCommBlacklist(@RequestParam("userId") @Parameter(description = "拉黑用户的id") Long userId,
                                    @CurrentUser @Parameter(hidden = true) User user) {
        if (!Objects.isNull(commBlacklistService.getCommBlacklistByUserIdAndTargetUserId(userId, user))) {
            return R.fail(400, "Do not add blacklist repeatedly !");
        }
        if (userId.equals(user.getId())) {
            return R.fail(400, "Do not add blacklist !");
        }
        Boolean result = commBlacklistService.addCommBlacklist(userId, user);
        return result ? R.success(result) : R.fail(1, "Failed");
    }

    @Operation(summary = "移除拉黑的用户")
    @PostMapping("/reduce-blacklist")
    @Authorization
    public R<Boolean> reduceCommBlacklist(@RequestParam("userId") @Parameter(description = "移除拉黑用户的id") Long userId,
                                       @CurrentUser @Parameter(hidden = true) User user) {
        CommBlacklist commBlacklist = commBlacklistService.getCommBlacklistByUserIdAndTargetUserId(userId, user);
        if (Objects.isNull(commBlacklist)) {
            return R.fail(400, "Please add blacklist first !");
        }
        if (userId.equals(user.getId())) {
            return R.fail(400, "Do not reduce blacklist !");
        }
        Boolean result = commBlacklistService.reduceCommBlacklist(userId, user);
        return result ? R.success(result) : R.fail(1, "Failed");
    }

    @Operation(summary = "获取拉黑用户列表")
    @GetMapping("/select-blacklist")
    @Authorization
    public R<CommPageInfo<CommBlacklist>> getCommBlacklist(@RequestParam(value =  "lastBlacklistId" , required = false) String lastBlacklistId,
                                                           @RequestParam("pageSize") Integer pageSize,
                                                           @CurrentUser @Parameter(hidden = true) User user) {
        if (pageSize > 50) {
            return R.fail(400, "pageSize exceeds 50 !");
        }
        return R.success(commBlacklistService.getCommBlacklist(lastBlacklistId, pageSize, user));
    }
}
