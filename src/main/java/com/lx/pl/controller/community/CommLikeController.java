package com.lx.pl.controller.community;

import com.lx.pl.annotation.Authorization;
import com.lx.pl.annotation.CurrentUser;
import com.lx.pl.db.mysql.community.entity.CommLike;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.dto.CommPageInfo;
import com.lx.pl.dto.PromptPageInfo;
import com.lx.pl.dto.generic.R;
import com.lx.pl.service.CommImgService;
import com.lx.pl.service.CommLikeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

@Tag(name = "社区点赞相关接口")
@Slf4j
@RestController
@RequestMapping("/api/comm-like")
public class CommLikeController {

    @Autowired
    private CommImgService commImgService;

    @Autowired
    private CommLikeService commLikeService;

    @Operation(summary = "对社区图片进行点赞")
    @PostMapping("/add-like-file")
    @Authorization
    public R<Boolean> addLikeCommFile(@RequestParam("commFileId") @Parameter(description = "社区图片id") String commFileId,
                                      @CurrentUser @Parameter(hidden = true) User user) {
        if (Objects.isNull(commImgService.getCommFileById(commFileId))) {
            return R.fail(400, "Img deleted !");
        }
        if (!Objects.isNull(commLikeService.getCommLikeByUserIdAndFileId(user.getId(), commFileId))) {
            return R.fail(400, "Do not like repeatedly !");
        }
        Boolean result = commLikeService.addLikeCommFile(commFileId, user);
        return result ? R.success(result) : R.fail(1, "Comment does not exist");
    }

    @Operation(summary = "对社区图片取消点赞")
    @PostMapping("/reduce-like-file")
    @Authorization
    public R<Boolean> reduceLikeCommFile(@RequestParam("commFileId") @Parameter(description = "社区图片id") String commFileId,
                                         @CurrentUser @Parameter(hidden = true) User user) {
        if (Objects.isNull(commImgService.getCommFileById(commFileId))) {
            return R.fail(400, "Img deleted !");
        }
        CommLike commLike = commLikeService.getCommLikeByUserIdAndFileId(user.getId(), commFileId);
        if (Objects.isNull(commLike)) {
            return R.fail(400, "Please like first !");
        }
        Boolean result = commLikeService.reduceLikeCommFile(commFileId, commLike, user);
        return result ? R.success(result) : R.fail(1, "Comment does not exist");
    }

    @Operation(summary = "对社区评论进行点赞")
    @PostMapping("/add-like-comment")
    @Authorization
    @Deprecated
    public R<Boolean> addLikeCommComment(@RequestParam("commFileId") @Parameter(description = "社区图片id") String commFileId,
                                         @RequestParam("commentId") @Parameter(description = "社区评论id") String commentId,
                                         @CurrentUser @Parameter(hidden = true) User user) {
        Boolean result = commLikeService.addLikeCommComment(commFileId, commentId, user);
        return result ? R.success(result) : R.fail(1, "Comment does not exist");
    }

    @Operation(summary = "对社区评论取消点赞")
    @PostMapping("/reduce-like-comment")
    @Authorization
    @Deprecated
    public R<Boolean> reduceLikeCommComment(@RequestParam("commFileId") @Parameter(description = "社区图片id") String commFileId,
                                            @RequestParam("commentId") @Parameter(description = "社区评论id") String commentId,
                                            @CurrentUser @Parameter(hidden = true) User user) {
        Boolean result = commLikeService.reduceLikeCommComment(commFileId, commentId, user);
        return result ? R.success(result) : R.fail(1, "Comment does not exist");
    }


    @Operation(summary = "获取社区图片点赞列表")
    @GetMapping("/select-like")
    @Authorization
    @Deprecated
    public R<CommPageInfo<CommLike>> getCommLikeList(@RequestParam(value = "lastLikeId", required = false) String lastLikeId,
                                                     @RequestParam("pageSize") Integer pageSize,
                                                     @RequestParam("commFileId") @Parameter(description = "社区图片id") String commFileId,
                                                     @RequestParam(value = "commentId", required = false) @Parameter(description = "社区评论id") String commentId,
                                                     @CurrentUser @Parameter(hidden = true) User user) {
        if (pageSize > 50) {
            return R.fail(400, "pageSize exceeds 50 !");
        }
        return R.success(commLikeService.getCommLikeList(lastLikeId, pageSize, commFileId, commentId, user));
    }

//    @Operation(summary = "处理历史图片点赞")
//    @PostMapping("/deal-history-like")
//    @Deprecated
//    public R<Boolean> dealHistoryCommLike(
//            @RequestParam("tableName") String tableName
//    ) {
//        return R.success(commLikeService.dealHistoryCommLike(tableName));
//    }
}
