package com.lx.pl.controller.community;

import com.lx.pl.annotation.Authorization;
import com.lx.pl.annotation.CurrentUser;
import com.lx.pl.db.mysql.community.entity.CommUser;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.dto.generic.R;
import com.lx.pl.service.CommUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

@Tag(name = "社区用户相关接口")
@Slf4j
@RestController
@RequestMapping("/api/comm-user")
public class CommUserController {

    @Autowired
    private CommUserService commUserService;

    @Operation(summary = "新增社区用户")
    @PostMapping("/add-user")
    @Authorization
    public R<Boolean> addCommUser(@CurrentUser @Parameter(hidden = true) User user) {
        Boolean result = commUserService.addCommUser(user);
        return result ? R.success(result) : R.fail(1, "Failed");
    }

    @Operation(summary = "查询社区用户信息")
    @PostMapping("/select-user")
    @Authorization
    public R<CommUser> getCommUser(@RequestParam(value = "userId", required = false) @Parameter(description = "其他人的用户id") Long userId,
                                   @CurrentUser @Parameter(hidden = true) User user) {
        Long targetUserId = !Objects.isNull(userId) ? userId : user.getId();
        return R.success(commUserService.findUserByUserIdAndFollow(targetUserId,user));
    }

//    @Operation(summary = "新增社区所有用户")
//    @PostMapping("/add-all-user")
//    @Deprecated
//    public R<Boolean> addAllCommUser() {
//        Boolean result = commUserService.addAllCommUser();
//        return R.success(result);
//    }
}
