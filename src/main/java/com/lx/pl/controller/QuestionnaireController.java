package com.lx.pl.controller;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.lx.pl.annotation.Authorization;
import com.lx.pl.annotation.CurrentUser;
import com.lx.pl.db.mysql.gen.entity.QuestionnaireRecord;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.dto.QuestionnaireAnswerParams;
import com.lx.pl.dto.generic.R;
import com.lx.pl.service.GenService;
import com.lx.pl.service.QuestionnaireAnswerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Objects;

@Tag(name = "问卷调查接口")
@Slf4j
@RestController
@RequestMapping("/api/questionnaire")
public class QuestionnaireController {

    @Autowired
    private QuestionnaireAnswerService questionnaireAnswerService;

    @Autowired
    private GenService genService;

    @Operation(summary = "提交问卷并领取lumen")
    @PostMapping(value = "/execute-answer")
    @Authorization
    public R<Boolean> executeAnswer(@RequestBody QuestionnaireAnswerParams params,
                                    @CurrentUser @Parameter(hidden = true) User user, HttpServletRequest request)
            throws IOException {

        String platform = genService.getPlatform(request);
        if (StringUtil.isBlank(platform) || StringUtil.isBlank(params.getQuestionnaireId())) {
            return R.fail(400, "Illegal request !");
        }

        Long answerNums = questionnaireAnswerService.getQuestionnaireAnswer(params.getQuestionnaireId(), platform, user);

        if (!Objects.isNull(answerNums) && answerNums > 0) {
            return R.fail(400, "Do not execute repeatedly !");
        }

        Boolean answerResult = questionnaireAnswerService.executeAnswer(params, platform, user);
        return answerResult ? R.success(answerResult) : R.fail(1, "Execute failed");
    }


    @Operation(summary = "查询问卷的内容")
    @GetMapping(value = "/select-questionnaire-detail")
    @Authorization
    public R<QuestionnaireRecord> getQuestionnaireDetail(@RequestParam(value = "questionnaireId", required = false) String questionnaireId,
                                                         @CurrentUser @Parameter(hidden = true) User user, HttpServletRequest request)
            throws IOException {

        String platform = genService.getPlatform(request);
        if (StringUtil.isBlank(platform)) {
            return R.fail(400, "Illegal request !");
        }

        Long answerNums = questionnaireAnswerService.getQuestionnaireAnswer(questionnaireId, platform, user);

        if (!Objects.isNull(answerNums) && answerNums > 0) {
            return R.fail(400, "Do not execute repeatedly !");
        }

        QuestionnaireRecord record = questionnaireAnswerService.getQuestionnaireDetail(questionnaireId, platform, user);
        return R.success(record);
    }
}
