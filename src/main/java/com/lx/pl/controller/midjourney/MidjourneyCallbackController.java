package com.lx.pl.controller.midjourney;

import com.lx.pl.dto.generic.R;
import com.lx.pl.dto.midjourney.MidjourneyResponse;
import com.lx.pl.service.MidjourneyCallbackService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Midjourney回调控制器
 *
 * <AUTHOR>
 */
@Slf4j
@Tag(name = "Midjourney回调接口")
@RestController
@RequestMapping("/api/midjourney/callback")
public class MidjourneyCallbackController {

    @Autowired
    private MidjourneyCallbackService midjourneyCallbackService;

    /**
     * 处理Midjourney回调
     */
    @PostMapping("/webhook")
    @Operation(summary = "Midjourney回调处理")
    public R<String> handleCallback(@RequestBody MidjourneyResponse.TaskStatusResponse callbackData) {

        log.info("Received Midjourney callback: {}", callbackData);

        try {
            midjourneyCallbackService.handleCallback(callbackData);
            return R.success("回调处理成功");
        } catch (Exception e) {
            log.error("Handle Midjourney callback error", e);
            return R.fail(400, "回调处理失败: " + e.getMessage());
        }
    }
}
