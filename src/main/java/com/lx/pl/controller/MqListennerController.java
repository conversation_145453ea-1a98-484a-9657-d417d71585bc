package com.lx.pl.controller;


import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.autoconfigure.ListenerContainerConfiguration;
import org.apache.rocketmq.client.support.DefaultListenerContainer;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.List;
import java.util.Objects;

@Slf4j
@Tag(name = "MQ 控制")
@RestController
@RequestMapping("/api/mq")
public class MqListennerController {
    @Resource
    private ListenerContainerConfiguration listenerContainerConfiguration;


    @GetMapping("/close-consumer")
    public String closeConsumer() {
        if (!Objects.isNull(listenerContainerConfiguration)) {
            try {
                Class<?> clazz = listenerContainerConfiguration.getClass().getSuperclass();
                Field containersField = clazz.getDeclaredField("containers");
                // 设置字段可访问
                containersField.setAccessible(true);
                // 获取字段值
                List<DefaultListenerContainer> defaultListenerContainers = (List<DefaultListenerContainer>) containersField.get(this.listenerContainerConfiguration);
                defaultListenerContainers.forEach(defaultListenerContainer -> {
                    log.info("defaultListenerContainer:{}", defaultListenerContainer.getConsumerGroup());
                    defaultListenerContainer.stop();

                });
                // 打印字段值
                System.out.println(defaultListenerContainers);
            } catch (NoSuchFieldException | IllegalAccessException e) {
                log.error("关闭失败", e);
                return "error";
            }
        }
        return "ok";
    }
}
