package com.lx.pl.controller;

import com.lx.pl.annotation.Authorization;
import com.lx.pl.annotation.CurrentUser;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.db.mysql.gen.entity.VipStandards;
import com.lx.pl.dto.OldUserLumens;
import com.lx.pl.dto.UserLumens;
import com.lx.pl.dto.generic.R;
import com.lx.pl.service.LumenService;
import com.lx.pl.service.VipService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Tag(name = "vip相关接口")
@Slf4j
@RestController
@RequestMapping("/api/vip")
public class VipController {

    @Autowired
    VipService vipStandardsService;
    @Autowired
    private LumenService lumenService;

    @Operation(summary = "查询vip的标准信息")
    @PostMapping("/resource-list")
    @Authorization
    public R<List<VipStandards>> listHistory(@CurrentUser @Parameter(hidden = true) User user) {
        return R.success(vipStandardsService.getVipStandardsList(user));
    }

    @Operation(summary = "查询用户的点数信息(兼容移动端后续删除)")
    @GetMapping("/lumens-message")
    @Authorization
    public R<OldUserLumens> getOldUserLumens(@CurrentUser @Parameter(hidden = true) User user) {
        return R.success(vipStandardsService.getOldUserLumens(user));
    }

    @Operation(summary = "查询用户的点数信息")
    @GetMapping("/lumens-message-detail")
    @Authorization
    public R<UserLumens> getUserLumens(@CurrentUser @Parameter(hidden = true) User user) {
        return R.success(lumenService.getUserLumens(user));
    }


    @Operation(summary = "查询用户的充值点数信息")
    @GetMapping("/lumens-recharge")
    @Authorization
    public R<Integer> getUserRechargeLumens(@CurrentUser @Parameter(hidden = true) User user) {
        return R.success(vipStandardsService.getUserRechargeLumens(user));
    }

    @Operation(summary = "查询用户能否试用")
    @GetMapping("/trail-flag")
    @Authorization
    public R<Boolean> canTrail(@CurrentUser @Parameter(hidden = true) User user) {
        return R.success(vipStandardsService.canTrail(user.getId()));
    }

    @Operation(summary = "查询用户能首充")
    @GetMapping("/cant-first-gift")
    @Authorization
    public R<Boolean> canFirstGift(@CurrentUser @Parameter(hidden = true) User user) {
        return R.success(vipStandardsService.canFirstGift(user.getId()));
    }
}
