package com.lx.pl.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.lx.pl.annotation.Authorization;
import com.lx.pl.annotation.CurrentUser;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.dto.*;
import com.lx.pl.dto.generic.R;
import com.lx.pl.enums.LogicErrorCode;
import com.lx.pl.enums.VipType;
import com.lx.pl.exception.LogicException;
import com.lx.pl.service.HistoryImgService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "历史页图片相关接口")
@Slf4j
@RestController
@RequestMapping("/api/history")
public class HistoryImgController {

    @Autowired
    HistoryImgService historyImgService;

    @Operation(summary = "查询当前用户生成的历史图片")
    @GetMapping("/history-img-list")
    @Authorization
    public R<PromptPageInfo<HistoryImgResult>> getHistoryList(
            @RequestParam("pageNum") Integer pageNum,
            @RequestParam("pageSize") Integer pageSize,
            @RequestParam(value = "vagueKey", required = false) String vagueKey,
            @RequestParam(value = "collationName") @Parameter(description = "排序规则名称：Ascending : 升序 Descending : 降序")
            String collationName,
            @RequestParam(value = "notCollection", required = false) @Parameter(description = "是否去掉已收藏的数据") Boolean  notCollection,
            @CurrentUser @Parameter(hidden = true) User user) throws JsonProcessingException {
        if (pageSize > 200) {
            return R.fail(400, "pageSize exceeds 200 !");
        }
        return R.success(historyImgService.getHistoryList(pageNum, pageSize, vagueKey,collationName,notCollection,user));
    }


}
