package com.lx.pl.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.lx.pl.annotation.Authorization;
import com.lx.pl.annotation.CurrentUser;
import com.lx.pl.db.mysql.community.entity.LumenRewardLog;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.dto.CommPageInfo;
import com.lx.pl.dto.TaskLumenRsult;
import com.lx.pl.dto.generic.R;
import com.lx.pl.service.LumenTaskService;
import com.lx.pl.vo.WatchADTaskDetailVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "lumen任务相关接口")
@Slf4j
@RestController
@RequestMapping("/api/lumen-task")
public class LumenTaskController {

    @Autowired
    LumenTaskService lumenTaskService;

//    @Operation(summary = "获取lumen任务标准列表")
//    @GetMapping("/select-standards")
//    @Authorization
//    public R<List<TaskLumenStandards>> getLumenTaskStandards(
//            @CurrentUser @Parameter(hidden = true) User user) throws JsonProcessingException {
//        return R.success(lumenTaskService.getLumenTaskStandards(user));
//    }


//    @Operation(summary = "更新每日任务")
//    @PostMapping("/reset-tasks")
//    @Authorization
//    public R<Boolean> resetTask(
//            @CurrentUser @Parameter(hidden = true) User user) throws JsonProcessingException {
//        Boolean result = lumenTaskService.resetTask(user);
//        return result ? R.success(result) : R.fail(1, "reset task failed !");
//    }

    @Operation(summary = "获取lumen任务完成列表")
    @GetMapping("/select-tasks")
    @Authorization
    public R<List<TaskLumenRsult>> getLumenTask(
            @CurrentUser @Parameter(hidden = true) User user) throws JsonProcessingException {
        return R.success(lumenTaskService.getLumenTask(user));
    }

    @Operation(summary = "完成加入社区或者分享图片的任务")
    @PostMapping("/finish-tasks")
    @Authorization
    public R<Boolean> finishTasks(@RequestParam("taskId") @Parameter(description = "任务id") String taskId,
                                  @CurrentUser @Parameter(hidden = true) User user) throws JsonProcessingException {
        Boolean result = lumenTaskService.finishTasks(taskId, user);
        return result ? R.success(result) : R.fail(1, "finish task failed !");
    }

    @Operation(summary = "领取对应任务的lumen币")
    @PostMapping("/receive-task-reward")
    @Authorization
    public R<Boolean> receiveTaskReward(@RequestBody List<String> taskIds,
                                        @CurrentUser @Parameter(hidden = true) User user) throws JsonProcessingException {
        if (CollectionUtils.isEmpty(taskIds) || taskIds.size() > 5) {
            return R.fail(400, "Parameter cannot be empty !");
        }

        Boolean result = lumenTaskService.receiveTaskReward(taskIds, user);
        return result ? R.success(result) : R.fail(1, "receive task reward failed !");
    }

    @Operation(summary = "获取lumen任务领取日志")
    @PostMapping("/get-task-reward-log")
    @Authorization
    public R<CommPageInfo<LumenRewardLog>> getTaskRewardLog(@RequestParam(value = "rewardLogId", required = false) String rewardLogId,
                                                            @RequestParam("pageSize") Integer pageSize,
                                                            @CurrentUser @Parameter(hidden = true) User user) throws Exception {
        return R.success(lumenTaskService.getTaskRewardLog(rewardLogId, pageSize, user));
    }

    @Operation(summary = "获取用户观看广告任务详情")
    @GetMapping("/watch-ad-task-detail")
    @Authorization
    public R<WatchADTaskDetailVO> queryWatchADTaskDetail(@CurrentUser @Parameter(hidden = true) User user){
        return R.success(lumenTaskService.queryWatchADTaskDetail(user));
    }

    @Operation(summary = "用户拉起广告")
    @PostMapping("/begin-watch-ad")
    @Authorization
    public R<Boolean> beginWatchAD(@CurrentUser @Parameter(hidden = true) User user){
        return R.success(lumenTaskService.beginWatchAD(user));
    }

    @Operation(summary = "用户完成观看广告任务")
    @PostMapping("/finish-watch-ad")
    @Authorization
    public R<Boolean> finishWatchAD(@CurrentUser @Parameter(hidden = true) User user){
        return R.success(lumenTaskService.finishWatchAD(user));
    }
}
