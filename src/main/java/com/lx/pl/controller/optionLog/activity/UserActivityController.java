package com.lx.pl.controller.optionLog.activity;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;

@RestController
@RequestMapping("/activity")
public class UserActivityController {

    @Autowired
    private UserActivityService userActivityService;

    @PostMapping("/log")
    public void logUserActivity(@RequestParam String userId, HttpServletRequest request) {
        userActivityService.logUserActivity(userId, request);
    }

    @GetMapping("/dau")
    public long getDailyActiveUsers(@RequestParam Date date) {
        return userActivityService.getDailyActiveUsers(date);
    }

    @GetMapping("/wau")
    public long getWeeklyActiveUsers(@RequestParam Date startDate, @RequestParam Date endDate) {
        return userActivityService.getWeeklyActiveUsers(startDate, endDate);
    }

    @GetMapping("/mau")
    public long getMonthlyActiveUsers(@RequestParam Date startDate, @RequestParam Date endDate) {
        return userActivityService.getMonthlyActiveUsers(startDate, endDate);
    }
}
