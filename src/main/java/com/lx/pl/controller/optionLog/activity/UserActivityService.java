package com.lx.pl.controller.optionLog.activity;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.lx.pl.constant.LogicConstants;
import com.lx.pl.enums.ClientType;
import com.lx.pl.service.RedisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Set;

@Service
public class UserActivityService {

    @Autowired
    private UserActivityRepository userActivityRepository;
    @Autowired
    private RedisService redisService;

    public void logUserActivity(String userId, HttpServletRequest request) {
        Date now = new Date();
        Date startOfDay = getStartOfDay(now);

        UserActivity activity = new UserActivity();
        activity.setUserId(userId);
        activity.setDate(startOfDay);

        try {
            userActivityRepository.insert(activity);
        } catch (DuplicateKeyException e) {
            // 忽略唯一键冲突异常
        }

        //分端存入redis
        String platform = request.getHeader("platform");
        String key = generateDAVKey(platform, startOfDay);
        redisService.addDataToSet(key, Set.of(userId));


    }

    private String generateDAVKey(String platform, Date startOfDay) {
        ClientType clientType = ClientType.getByValue(platform);
        return LogicConstants.DAILY_UNIQUE_VISITOR_KEY_PREFIX + DateUtil.format(startOfDay, DatePattern.NORM_DATE_PATTERN) + ":" + clientType.getValue();
    }

    public long getDailyActiveUsers(Date date) {
        Date startOfDay = getStartOfDay(date);
        Date endOfDay = getEndOfDay(date);

        List<UserActivity> activities = userActivityRepository.findByDateBetween(startOfDay, endOfDay);
        return activities.stream().map(UserActivity::getUserId).distinct().count();
    }

    public long getWeeklyActiveUsers(Date startDate, Date endDate) {
        List<UserActivity> activities = userActivityRepository.findByDateBetween(startDate, endDate);
        return activities.stream().map(UserActivity::getUserId).distinct().count();
    }

    public long getMonthlyActiveUsers(Date startDate, Date endDate) {
        List<UserActivity> activities = userActivityRepository.findByDateBetween(startDate, endDate);
        return activities.stream().map(UserActivity::getUserId).distinct().count();
    }

    private Date getStartOfDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    private Date getEndOfDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTime();
    }
}
