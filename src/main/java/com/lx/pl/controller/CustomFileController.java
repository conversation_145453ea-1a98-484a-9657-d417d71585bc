package com.lx.pl.controller;

import cn.hutool.core.util.StrUtil;
import com.google.common.util.concurrent.RateLimiter;
import com.lx.pl.annotation.Authorization;
import com.lx.pl.annotation.CurrentUser;
import com.lx.pl.annotation.RateLimit;
import com.lx.pl.db.mysql.gen.entity.CustomFile;
import com.lx.pl.db.mysql.gen.entity.ToolRmbgResult;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.dto.CustomFileParam;
import com.lx.pl.dto.ToolRmbgParam;
import com.lx.pl.dto.generic.R;
import com.lx.pl.dto.generic.StatusCodeEnum;
import com.lx.pl.service.GenService;
import com.lx.pl.service.ICustomFileService;
import com.lx.pl.service.ToolRmbgResultService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Objects;

/**
 * 用户上传
 *
 * <AUTHOR>
 */
@Slf4j
@Tag(name = "用户自定义上传")
@RestController
@RequestMapping("/api/custom-file")
public class CustomFileController {

    @Autowired
    private ICustomFileService customFileService;

    @Autowired
    private ToolRmbgResultService toolRmbgResultService;

    @Autowired
    GenService genService;

    private static RateLimiter rateLimiter = RateLimiter.create(30);

    @PostMapping("/save")
    @Operation(summary = "保存用户上传")
    @Authorization
    @RateLimit
    public R<CustomFile> saveCustomFile(@Parameter(hidden = true) @CurrentUser User user, @RequestBody CustomFileParam param, HttpServletRequest request) {
        // 30 permitsPerSecond
        if (!rateLimiter.tryAcquire()) {
            return R.fail(400, "failed to upload, please try later!");
        }
        if (Objects.isNull(param) || StrUtil.isEmpty(param.getFileUrl())) {
            return R.fail(400, "failed to upload, param error！");
        }
        String platform = genService.getPlatform(request);
        CustomFile customFile = customFileService.saveCustomFile(param, user, platform);
        if (customFile != null) {
            return R.success(customFile);
        }
        return R.fail(StatusCodeEnum.UPLOAD_LIMIT.getCode(), "failed to upload, limit 100 count everyday！");
    }

    @PostMapping("/check-can-upload")
    @Authorization
    public R<Boolean> checkUserCanUpload(@Parameter(hidden = true) @CurrentUser User user) {
        return R.success(customFileService.queryCanUpload(user));
    }

}
