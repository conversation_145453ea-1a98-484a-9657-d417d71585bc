package com.lx.pl.dto.mq;

import lombok.Data;

/**
 * MJ任务状态轮询消息体
 *
 * <AUTHOR>
 */
@Data
public class MjTaskPollingVo {

    /**
     * 任务ID
     */
    private String jobId;

    /**
     * 用户登录名
     */
    private String loginName;

    /**
     * 当前轮询次数
     */
    private Integer currentAttempt;

    /**
     * 最大轮询次数
     */
    private Integer maxAttempts;
    
    /**
     * 轮询间隔（秒）
     */
    private Integer pollingInterval;

    /**
     * 任务创建时间戳
     */
    private Long createTimestamp;

    public MjTaskPollingVo() {
    }

    public MjTaskPollingVo(String jobId, String loginName, Integer currentAttempt,
                           Integer maxAttempts, Integer pollingInterval, Long createTimestamp) {
        this.jobId = jobId;
        this.loginName = loginName;
        this.currentAttempt = currentAttempt;
        this.maxAttempts = maxAttempts;
        this.pollingInterval = pollingInterval;
        this.createTimestamp = createTimestamp;
    }
}
