package com.lx.pl.dto.mq;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;

import java.util.List;

/**
 * MJ图片处理结果消息体
 *
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class MjImageProcessResultVo {

    /**
     * 任务ID (jobId)
     */
    private String jobId;

    /**
     * 用户登录名
     */
    private String loginName;

    /**
     * markId
     */
    private String markId;

    /**
     * 处理状态 (SUCCESS/FAILED)
     */
    private String status;

    /**
     * 错误信息 (如果处理失败)
     */
    private String errorMessage;

    /**
     * 处理结果列表
     */
    private List<ProcessedImageInfo> processedImages;

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    @ToString
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ProcessedImageInfo {
        /**
         * promptFile的ID
         */
        private Long promptFileId;

        /**
         * 原始图片URL
         */
        private String originalUrl;

        /**
         * 压缩后的缩略图URL
         */
        private String thumbnailUrl;

        /**
         * 高清缩略图URL
         */
        private String highThumbnailUrl;

        /**
         * 迷你缩略图URL
         */
        private String miniThumbnailUrl;

        /**
         * 30%高清图URL
         */
        private String highMiniUrl;

        /**
         * 处理状态 (SUCCESS/FAILED)
         */
        private String processStatus;

        /**
         * 错误信息
         */
        private String errorMessage;
    }
}
