package com.lx.pl.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class ImgControlSaveRequest {
    @Schema(description = "图片地址")
    private String imgUrl;

    @Schema(description = "图片高度")
    private Integer height;

    @Schema(description = "图片宽度")
    private Integer width;

    @Schema(description = "真实宽")
    private Integer realWidth;

    @Schema(description = "真实高")
    private Integer realHeight;

    @Schema(description = "控制类型（canny: 主体, depth: 轮廓, openpose: 姿势）")
    private String controlType;
}
