package com.lx.pl.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class QuestionnaireAnswerParams {

    @Schema(description = "问卷 ID")
    private String questionnaireId;

    @Schema(description = "问卷标题")
    private String questionnaireTitle;

    @Schema(description = "单选题1答案")
    private String scAnswer1;

    @Schema(description = "单选题2答案")
    private String scAnswer2;

    @Schema(description = "单选题3答案")
    private String scAnswer3;

    @Schema(description = "单选题4答案")
    private String scAnswer4;

    @Schema(description = "单选题5答案")
    private String scAnswer5;

    @Schema(description = "单选题6答案")
    private String scAnswer6;

    @Schema(description = "单选题7答案")
    private String scAnswer7;

    @Schema(description = "单选题8答案")
    private String scAnswer8;

    @Schema(description = "单选题9答案")
    private String scAnswer9;

    @Schema(description = "单选题10答案")
    private String scAnswer10;

    @Schema(description = "多选题1答案")
    private String mcAnswer1;

    @Schema(description = "多选题2答案")
    private String mcAnswer2;

    @Schema(description = "多选题3答案")
    private String mcAnswer3;

    @Schema(description = "多选题4答案")
    private String mcAnswer4;

    @Schema(description = "多选题5答案")
    private String mcAnswer5;

    @Schema(description = "多选题6答案")
    private String mcAnswer6;

    @Schema(description = "多选题7答案")
    private String mcAnswer7;

    @Schema(description = "多选题8答案")
    private String mcAnswer8;

    @Schema(description = "多选题9答案")
    private String mcAnswer9;

    @Schema(description = "多选题10答案")
    private String mcAnswer10;

    @Schema(description = "问答题1答案")
    private String essayAnswer1;

    @Schema(description = "问答题2答案")
    private String essayAnswer2;

    @Schema(description = "问答题3答案")
    private String essayAnswer3;

    @Schema(description = "问答题4答案")
    private String essayAnswer4;

    @Schema(description = "问答题5答案")
    private String essayAnswer5;

    @Schema(description = "评分题1答案")
    private String gradeAnswer1;

    @Schema(description = "评分题2答案")
    private String gradeAnswer2;

    @Schema(description = "评分题3答案")
    private String gradeAnswer3;

    @Schema(description = "评分题4答案")
    private String gradeAnswer4;

    @Schema(description = "评分题5答案")
    private String gradeAnswer5;
}
