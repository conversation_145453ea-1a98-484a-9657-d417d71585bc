package com.lx.pl.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "用户举报传参")
public class UserAddReportDto {

    @Schema(description = "图片名称")
    private String imgName;

    @Schema(description = "promptId")
    private String promptId;

    @Schema(description = "审核类型(1.Violence  2. Pornography 3.Racial discrimination  4 Copyright infringement  5 Other) ")
    private Integer auditType;

    @Schema(description = "其他内容")
    private String otherContent;

    @Schema(description = "用户账号名(请求头带token即可非前端参数) ")
    private String loginName;

    @Schema(description = "用户id(请求头带token即可非前端参数) ")
    private String createBy;
}
