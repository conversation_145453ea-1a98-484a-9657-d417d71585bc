package com.lx.pl.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class StatisticsImgNums {

    @Schema(description = "用户当天生图数")
    private Integer todayCreateImgNums;

    @Schema(description = "用户总的生图数量")
    private Integer allCreateImgNums;

    @Schema(description = "每日免费点数")
    private Integer dailyLumens;

    @Schema(description = "剩余每日免费点数")
    private Integer useDailyLumens;
}
