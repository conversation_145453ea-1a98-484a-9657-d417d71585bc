package com.lx.pl.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;

@ToString
@Data
public class GenGenericResp extends GenGenericPara {

    /**
     * 任务插队顺序
     */
    private Integer number;

    private String promptId;

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 标记id
     */
    private String markId;

    /**
     * 当前任务排队序号
     */
    private Integer index;

    /**
     * 生图原始操作
     */
    private String originCreate;

    /**
     * 任务创建日期
     */
    private LocalDateTime createTimestamp;

    /**
     * 是否公开(0:未公开 1:已公开 2:审核中  3.已拒绝)
     */
    private Integer isPublic;


    /**
     * 是否属于fastHour机制内的任务
     */
    private Boolean fastHour;

    /**
     * 功能类型
     */
    private String featureName;

    /**
     * 任务扣除点数
     */
    private Integer costLumen;
}
