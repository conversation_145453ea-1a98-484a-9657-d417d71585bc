package com.lx.pl.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.databind.JsonNode;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class PromptFileRecord {

    @Schema(description = "任务id")
    private Long id;

    @Schema(description = "用户名称")
    private String loginName;

    @Schema(description = "用户名称")
    private String userName;

    @Schema(description = "生图信息")
    private String avatar;

    @Schema(description = "生图信息")
    private GenGenericPara genInfo;

    private String promptId;

    @Schema(description = "生图时间")
    private LocalDateTime createTimestamp;

    @Schema(description = "生图成功后地址")
    private List<ImgUrl> img_urls;

    @ToString
    @Data
    public static class ImgUrl {

        @Schema(description = "图片ID")
        private Long id;

        @Schema(description = "文件名称")
        private String imgName;

        @Schema(description = "图片生成后地址")
        private String imgUrl;

        @Schema(description = "缩略图名称")
        private String thumbnailName;

        @Schema(description = "缩略图路径")
        private String thumbnailUrl;

        @Schema(description = "高清缩略图名称")
        private String highThumbnailName;

        @Schema(description = "高清缩略图路径")
        private String highThumbnailUrl;

        @Schema(description = "小图路径")
        private String miniThumbnailUrl;

        @Schema(description = "30% 高清图")
        private String highMiniUrl;

        @Schema(description = "敏感信息")
        private String sensitive;

        @Schema(description = "点赞数")
        private int likeNums;

        @Schema(description = "生成图片的宽")
        private int realWidth;

        @Schema(description = "生成图片的高")
        private int realHeight;

        @Schema(description = "生图原始操作")
        private String originCreate;

        @Schema(description = "是否公开(0:未公开 1:已公开 2:审核中  3.已拒绝)")
        private Integer isPublic;

        /**
         * 拒绝内容描述
         */
        @Schema(description = "拒绝内容描述")
        private String rejectionContent;

        @Schema(description = "收藏数")
        private Integer collectNums;
    }
}
