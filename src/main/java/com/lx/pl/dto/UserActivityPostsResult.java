package com.lx.pl.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class UserActivityPostsResult {

    @Schema(description ="社区图片id")
    private String id;

    @Schema(description ="图片id")
    private String fileId;

    @Schema(description = "公开类型：everyone ： 所有人可见  myself ： 自己可见 ")
    private String publicType ;

    @Schema(description = "是否公开(1:已公开 2:审核中)")
    private Integer isPublic;

    /**
     * 30% 高清图
     */
    @Schema(description = "30% 高清图 (默认30% 高清图 老数据可能30% 高清图 若没有则展示(thumbnailUrl 或者imgUrl 优先级为(highMiniUrl -- thumbnailUrl -- imgUrl)))")
    private String highMiniUrl;

    /**
     * 文件路径
     */
    @Schema(description = "文件路径(原图)")
    private String imgUrl;

    /**
     * 缩略图路径
     */
    @Schema(description = "缩略图路径")
    private String thumbnailUrl;

}
