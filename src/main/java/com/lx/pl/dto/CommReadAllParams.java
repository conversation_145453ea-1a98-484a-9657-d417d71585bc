package com.lx.pl.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class CommReadAllParams {

    @Schema(description = "已读点赞数")
    private Integer readLikeNums;

    @Schema(description = "已读点赞最新的id")
    private String likeLastId;

    @Schema(description = "已读点评论数")
    private Integer readCommentNums;

    @Schema(description = "已读点评论最新的id")
    private String commentLastId;

    @Schema(description = "已读系统更新数")
    private Integer readSysUpdateNums;

    @Schema(description = "已读系统更新最新的id")
    private String sysUpdateLastId;

    @Schema(description = "已读平台消息数")
    private Integer readPlatformNums;

    @Schema(description = "已读平台消息最新的id")
    private String platformMessageLastId;

    @Schema(description = "已读平台活动最新的id")
    private String platformActivityLastId;
}
