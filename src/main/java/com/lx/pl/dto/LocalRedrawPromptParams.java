package com.lx.pl.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class LocalRedrawPromptParams {

    @Schema(description = "图片url")
    private String img_url;

    @Schema(description = "局部重绘过的图片url")
    private String mask_img_url;

    @Schema(description = "模型id")
    private String model_id;

    @Schema(description = "正向提示词")
    private String prompt;

    @Schema(description = "标记id")
    private String mark_id;

    @Schema(description = "生图回调接口")
    private String callback_url;

    @Schema(description = "服务器地址id")
    private String address;

    @Schema(description = "是否开启模型加速，默认为：false")
    private Boolean accelerated;

}
