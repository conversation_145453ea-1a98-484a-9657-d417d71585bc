package com.lx.pl.dto;

import com.fasterxml.jackson.databind.JsonNode;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class FileDetailResult {

    @Schema(description = "用户名称")
    private String userName;

    @Schema(description = "用户名称")
    private String loginName;

    @Schema(description = "头像信息")
    private String avatar;

    @Schema(description = "文件名称")
    private String imgName;

    @Schema(description = "缩略图名称")
    private String thumbnailName;

    @Schema(description = "文件路径")
    private String imgUrl;

    @Schema(description = "缩略图路径")
    private String thumbnailUrl;

    private String promptId;

    @Schema(description = "创建日期")
    private LocalDateTime createTimestamp;

    @Schema(description = "生图信息")
    private GenGenericPara genInfo;

    @Schema(description = "生成图片的宽")
    private int realWidth;

    @Schema(description = "生成图片的高")
    private int realHeight;

    @Schema(description = "模型名称")
    private String modelDisplay;

    @Schema(description = "模型图标")
    private String modelAvatar;

    @Schema(description = "生图原始操作")
    private String originCreate;

    @Schema(description = "敏感信息：涉黄等")
    private String sensitiveMessage;

    @Schema(description = "标签")
    private List<String> tagList;

}
