package com.lx.pl.manager;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.lx.pl.constant.LogicConstants;
import com.lx.pl.constant.PromptTemplateConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Component
public class PromptTemplateManager {
    @Autowired
    private StringRedisTemplate redisTemplate;
    private static final ObjectMapper objectMapper = new ObjectMapper();
    private final Cache<String, Map<String, Map<String, String>>> localCache = CacheBuilder.newBuilder()
            .expireAfterWrite(65, TimeUnit.MINUTES)
            .build();

    public String getTemplate(String mainCategory, String subCategory) {
        Map<String, Map<String, String>> cachedTemplates = localCache.getIfPresent(LogicConstants.PROMPT_TEMPLATES_CACHE);
        if (cachedTemplates == null) {
            refreshCache();
            cachedTemplates = localCache.getIfPresent(LogicConstants.PROMPT_TEMPLATES_CACHE);
        }
        if (cachedTemplates == null) {
            cachedTemplates = PromptTemplateConstants.DEFAULT_TEMPLATES;
        }

        Map<String, String> subCategoryMap = cachedTemplates.get(mainCategory);
        return (subCategoryMap != null) ? subCategoryMap.get(subCategory) : null;
    }

    private void refreshCache() {
        try {
            String cachedData = redisTemplate.opsForValue().get(LogicConstants.PROMPT_TEMPLATES_CACHE);
            if (cachedData == null) {
                cachedData = objectMapper.writeValueAsString(PromptTemplateConstants.DEFAULT_TEMPLATES);
                redisTemplate.opsForValue().set(LogicConstants.PROMPT_TEMPLATES_CACHE, cachedData);
            }
            Map<String, Map<String, String>> redisTemplates = objectMapper.readValue(cachedData, Map.class);
            localCache.put(LogicConstants.PROMPT_TEMPLATES_CACHE, redisTemplates);
        } catch (IOException e) {
            localCache.put(LogicConstants.PROMPT_TEMPLATES_CACHE, PromptTemplateConstants.DEFAULT_TEMPLATES);
        }
    }

    // 替换模板中的 {prompt} 占位符
    private String replacePrompt(String template, String prompt) {
        if (template == null) {
            return prompt;
        }
        if (prompt == null) {
            return prompt = "";
        }
        return template.replace("{prompt}", prompt);
    }

    public String getStyleHelperPrompt(String mainCategory, String subCategory, String prompt) {
        String template = getTemplate(mainCategory, subCategory);
        return replacePrompt(template, prompt);
    }
}
