package com.lx.pl.manager;

import java.util.concurrent.TimeUnit;

public interface TokenManager {

    /**
     * 创建一个token关联上指定用户
     *
     * @param userId 指定用户的id
     * @return 生成的token
     */
    public String createToken(long userId, long role);

    /**
     * 检查token是否有效
     *
     * @param token
     * @return 是否有效
     */
    public boolean checkToken(String token);

    public long getUserId(String token);

    public void deleteToken(String token, String userId);

    public String getTokenByUserId(long userId);

    public String getUserIdByToken(String token);

    public Long getKeyExpireTime(String key);

    public void createUserIdByToken(String token, Long userId, Long expireTime, TimeUnit timeUnit);
}
