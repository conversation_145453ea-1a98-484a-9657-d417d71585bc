package com.lx.pl.constant;

import java.util.HashMap;
import java.util.Map;

public class PromptTemplateConstants {

    // 默认模板数据
    public static final Map<String, Map<String, String>> DEFAULT_TEMPLATES = new HashMap<>();

    static {
        addDefaultTemplate("Style", "Neon", "neon {prompt}. dark, neon signs, high contrast, low light, vibrant, highly detailed");
        addDefaultTemplate("Style", "Fantasy", "ethereal fantasy concept art of {prompt}. magnificent, celestial, ethereal, painterly, epic, majestic, magical, fantasy art, cover art, dreamy");
        addDefaultTemplate("Style", "Van Gogh", "van gogh, vibrant, contrasting, brushstroke style, {prompt}");
        addDefaultTemplate("Style", "GTA", "GTA-style artwork, satirical, exaggerated, pop art style, vibrant colors, iconic characters, action-packed, {prompt}");
        addDefaultTemplate("Style", "Comic", "comic {prompt}. graphic illustration, comic art, graphic novel art, vibrant, highly detailed");
        addDefaultTemplate("Style", "Pop Art", "pop Art style {prompt}. bright colors, bold outlines, popular culture themes, ironic or kitsch");
        addDefaultTemplate("Style", "Sci-Fi", "sci-fi style {prompt}. futuristic, technological, alien worlds, space themes, advanced civilizations");
        addDefaultTemplate("Style", "Simple", "A simple character illustration of {prompt}");
        addDefaultTemplate("Style", "1980's anime", "1980's anime screencap, {prompt}");
        addDefaultTemplate("Style", "3D animation", "pixar style, 3d disney style, {prompt}");
        addDefaultTemplate("Style", "Caricature", "caricature style, exaggerated features, visual humor, {prompt}");
        addDefaultTemplate("Style", "Dark fantasy", "dark fantasy, dull color, {prompt}");
        addDefaultTemplate("Style", "Poster design", "{prompt}, in style of election poster design");
        addDefaultTemplate("Style", "Pixel art", "{prompt}, in style of pixel art");
        addDefaultTemplate("Style", "Monochrome", "monochrome {prompt}. black and white, contrast, tone, texture, detailed");
        addDefaultTemplate("Style", "Graffiti", "Graffiti Art Style, {prompt}, dynamic, dramatic, vibrant colors, graffiti art style");
        addDefaultTemplate("Style", "Surrealism", "Surrealism, {prompt}, expressive, dramatic, organic lines and forms, dreamlike and mysterious, Surrealism");
        addDefaultTemplate("Style", "Glitch", "Glitchcore Art Style, {prompt}, dynamic, dramatic, distorted, vibrant colors, glitchcore art style");
        addDefaultTemplate("Style", "Silhouette", "Silhouette Art, {prompt}, high contrast, well defined, Silhouette Art");
        addDefaultTemplate("Style", "Flat Papercraft", "flat papercut style {prompt}. silhouette, clean cuts, paper, sharp edges, minimalist, color block");


        // 添加 Artify 类别的模板
        addDefaultTemplate("Painting", "Sketch", "pencil sketch of {prompt}, black lines, Black and white colors, high contrast, clean background");
        addDefaultTemplate("Painting", "Watercolor", "((watercolor style)), visible texture, soft and blurred edges, {prompt}");
        addDefaultTemplate("Painting", "Oil Painting", "Oil painting style of {prompt}, brushstroke style, bold line");
        addDefaultTemplate("Painting", "Lineart", "line art drawing {prompt}. professional, sleek, modern, minimalist, graphic, line art, vector graphics");

        // 添加 Photography 类别的模板
        addDefaultTemplate("Photography", "Depth of field", "large depth of field, deep depth of field, highly detailed, {prompt}");
        addDefaultTemplate("Photography", "Film noir", "film noir style {prompt}, monochrome, high contrast, dramatic shadows, 1940s style, mysterious, cinematic");
        addDefaultTemplate("Photography", "Long exposure", "long exposure photo of {prompt}. Blurred motion, streaks of light, surreal, dreamy, ghosting effect, highly detailed");
        addDefaultTemplate("Photography", "Analog film", "analog film photo {prompt}. faded film, desaturated, 35mm photo, grainy, vignette, vintage, Kodachrome, Lomography, stained, highly detailed, found footage");
        addDefaultTemplate("Photography", "Cinematic", "cinematic film still {prompt}. shallow depth of field, vignette, highly detailed, high budget, bokeh, cinemascope, moody, epic, gorgeous, film grain, grainy");
        addDefaultTemplate("Photography", "Faded Photo", "Faded Polaroid Photo, {prompt}, analog, old faded photo, old polaroid");
        addDefaultTemplate("Photography", "Tilt Shift", "tilt-shift photo of {prompt} . selective focus, miniature effect, blurred background, highly detailed, vibrant, perspective control");
    }

    // 添加默认模板
    private static void addDefaultTemplate(String mainCategory, String subCategory, String template) {
        DEFAULT_TEMPLATES.computeIfAbsent(mainCategory, k -> new HashMap<>()).put(subCategory, template);
    }
}