package com.lx.pl.constant;

/**
 * <AUTHOR>
 */
public interface LockPrefixConstant {

    /**
     * 用户操作锁前缀
     */
    String USER_DO_LOCK_PREFIX = "user:do:";

    /**
     * 用户checkUserTaskQueue锁前缀
     */
    String PRELOADING_LOCK_PREFIX = "user:preloading:";

    /**
     * 用户concurrentExecution锁前缀
     */
    String CONCURRENT_EXECUTION_LOCK_PREFIX = "user:concurrentExecution:";

    /**
     * 用户fastCreate锁前缀
     */
    String FAST_CREATE_LOCK_PREFIX = "user:fastCreate:";

    /**
     * transactionId 锁前缀
     */
    String TRANSACTION_ID_LOCK_PREFIX = "apple:transaction:";

    String OPERATE_PERMISSION_LOCK_PREFIX = "user:operatePermission:";

    /**
     * 用户完成观看广告任务锁前缀
     */
    String FINISH_WATCH_AD_TASK_LOCK_PREFIX = "user:finishTask:watchAD:";

    /**
     * 用户拉起广告锁前缀
     */
    String BEGIN_WATCH_AD_TASK_LOCK_PREFIX = "user:beginTask:watchAD:";

    /**
     * 处理用户lumen分布式锁前缀
     */
    String DEAL_USER_LUMEN_LOCK_PREFIX = "dealLumensAfterCreate:";
}
