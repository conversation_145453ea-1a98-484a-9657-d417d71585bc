package com.lx.pl.Handler;

import com.github.lianjiatech.retrofit.spring.boot.degrade.FallbackFactory;
import com.lx.pl.client.LanguageTranslateApi;
import com.lx.pl.client.NdApi;

public class LanguageTranslateFallbackFactory implements FallbackFactory<LanguageTranslateApi> {
    @Override
    public LanguageTranslateApi create(Throwable cause) {
        System.out.println("触发熔断了!" + cause.getMessage() + " cause:" + cause);
        return null;
    }
}
