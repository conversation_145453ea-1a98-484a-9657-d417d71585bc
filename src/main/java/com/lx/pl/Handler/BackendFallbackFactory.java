package com.lx.pl.Handler;

import com.github.lianjiatech.retrofit.spring.boot.degrade.FallbackFactory;
import com.lx.pl.client.BackendApi;
import com.lx.pl.dto.BackendPromptParams;
import com.lx.pl.dto.BackendPromptResult;
import org.springframework.stereotype.Component;
import retrofit2.Response;

@Component
public class BackendFallbackFactory implements FallbackFactory<BackendApi> {
    @Override
    public BackendApi create(Throwable cause) {
        System.out.println("触发熔断了!" + cause.getMessage() + " cause:" + cause);
        return null;
    }
}
