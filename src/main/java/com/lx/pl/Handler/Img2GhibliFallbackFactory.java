package com.lx.pl.Handler;

import com.github.lianjiatech.retrofit.spring.boot.degrade.FallbackFactory;
import com.lx.pl.client.EmailApi;
import com.lx.pl.client.Img2GhibliApi;
import com.lx.pl.client.Img2TextApi;

public class Img2GhibliFallbackFactory implements FallbackFactory<Img2GhibliApi> {

    @Override
    public Img2GhibliApi create(Throwable cause) {
        System.out.println("触发熔断了!" + cause.getMessage() + " cause:" + cause);
        return null;
    }
}
