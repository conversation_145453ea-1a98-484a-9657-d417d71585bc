package com.lx.pl.Handler;

import org.springframework.stereotype.Component;

import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;

@Component
public class TranslateTaskRejectedExecutionHandler implements RejectedExecutionHandler {

    @Override
    public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
        // 这里可以根据需要抛出异常或者记录日志
        throw new RuntimeException("Server is busy, please try again later.");
    }
}
