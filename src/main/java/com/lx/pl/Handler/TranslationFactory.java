package com.lx.pl.Handler;

import com.github.lianjiatech.retrofit.spring.boot.degrade.FallbackFactory;
import com.lx.pl.client.TranslationApi;
import org.springframework.stereotype.Component;

@Component
public class TranslationFactory implements FallbackFactory<TranslationApi> {
    @Override
    public TranslationApi create(Throwable cause) {
        System.out.println("触发熔断了!" + cause.getMessage() + " cause:" + cause);
        return null;
    }
}
