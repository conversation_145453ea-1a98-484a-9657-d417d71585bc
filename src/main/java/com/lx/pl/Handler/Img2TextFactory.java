package com.lx.pl.Handler;

import com.github.lianjiatech.retrofit.spring.boot.degrade.FallbackFactory;
import com.lx.pl.client.Img2TextApi;
import org.springframework.stereotype.Component;

@Component
public class Img2TextFactory implements FallbackFactory<Img2TextApi> {
    @Override
    public Img2TextApi create(Throwable cause) {
        System.out.println("触发熔断了!" + cause.getMessage() + " cause:" + cause);
        return null;
    }
}
