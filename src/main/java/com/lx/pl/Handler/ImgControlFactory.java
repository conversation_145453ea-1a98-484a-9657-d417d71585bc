package com.lx.pl.Handler;

import com.github.lianjiatech.retrofit.spring.boot.degrade.FallbackFactory;
import com.lx.pl.client.Img2TextApi;
import com.lx.pl.client.ImgControlApi;

public class ImgControlFactory implements FallbackFactory<ImgControlApi> {

    @Override
    public ImgControlApi create(Throwable cause) {
        System.out.println("触发熔断了!" + cause.getMessage() + " cause:" + cause);
        return null;
    }
}
