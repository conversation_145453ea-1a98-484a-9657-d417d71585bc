package com.lx.pl.openai.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.lx.pl.openai.dto.captionResponseSub.CaptionResChoice;
import com.lx.pl.openai.dto.captionResponseSub.CaptionResUsage;
import lombok.Data;

import java.util.List;


@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CaptionResponse {

    private String id;
    private String object;
    private Long created;
    private String model;
    private List<CaptionResChoice> choices;
    private CaptionResUsage usage;
}