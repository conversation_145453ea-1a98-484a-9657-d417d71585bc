package com.lx.pl.openai.dto.captionResponseSub;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CaptionResChoice {

    private Integer index;
    private CaptionResMessage message;
    private String logprobs;
    @JsonProperty("finish_reason")
    private String finishReason;
    @JsonProperty("stop_reason")
    private Object stopReason;
}