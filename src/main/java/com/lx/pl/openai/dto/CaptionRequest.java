package com.lx.pl.openai.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class CaptionRequest {

    private String model;
    private List<Map<String, Object>> messages;
    private Double temperature;
    @JsonProperty("top_p")
    private Double topP;
    private String stream;
    private Integer max_tokens;
}