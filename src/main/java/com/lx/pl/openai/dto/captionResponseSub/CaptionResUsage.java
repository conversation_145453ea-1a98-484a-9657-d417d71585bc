package com.lx.pl.openai.dto.captionResponseSub;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CaptionResUsage {

    @JsonProperty("prompt_tokens")
    private Integer promptTokens;
    @JsonProperty("total_tokens")
    private Integer totalTokens;
    @JsonProperty("completion_tokens")
    private Integer completionTokens;
    @JsonProperty("prompt_tokens_details")
    private String promptTokensDetails;
}