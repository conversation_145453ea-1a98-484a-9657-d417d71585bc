package com.lx.pl.openai.controller;

import com.lx.pl.annotation.Authorization;
import com.lx.pl.annotation.CurrentUser;
import com.lx.pl.annotation.LeakyBucketLimit;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.dto.TranslateParams;
import com.lx.pl.dto.generic.R;
import com.lx.pl.enums.LogicErrorCode;
import com.lx.pl.openai.service.Img2TextService;
import com.lx.pl.service.VipService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@Tag(name = "openai相关接口")
@RequestMapping("/api/open")
@Slf4j
public class OpenAiController {
    @Autowired
    private Img2TextService img2TextService;

    @Autowired
    private VipService vipService;

    @Operation(summary = "图片反推提示词")
    @PostMapping("/img-to-text")
    @Authorization
    @LeakyBucketLimit(intervalSeconds = 1, queueSize = 50)
    public R<String> generateImageCaption(@RequestParam @Parameter(description = "图片url") String imageUrl,
                                          @RequestParam @Parameter(description = "反推类型(tag,des)") String type,
                                          @CurrentUser @Parameter(hidden = true) User user) {
        //具有操作权限
        Boolean operatePermission = vipService.judgeUserOperatePermission(user, 1);

        //不足点数不支持
        if (!operatePermission) {
            return R.fail(Integer.valueOf(LogicErrorCode.NOT_ENOUGH_LUMENS_SUPPORT.getCode()),
                    LogicErrorCode.NOT_ENOUGH_LUMENS_SUPPORT.getMessage());
        }

        String caption = img2TextService.generateImageCaption(imageUrl, type, user);
        return R.success(caption);
    }


    @Operation(summary = "翻译接口")
    @PostMapping("/translate-to-language")
    @Authorization
    @LeakyBucketLimit(intervalSeconds = 0.5, queueSize = 50)
    public R<String> translateToLanguage(@RequestBody TranslateParams translateParams,
                                         @CurrentUser @Parameter(hidden = true) User user) {
        //普通用户暂时不支持
//        if (VipType.basic.getValue().equals(user.getVipType())) {
//            return R.fail(Integer.valueOf(LogicErrorCode.NOT_VIP.getCode()),
//                    LogicErrorCode.NOT_VIP.getMessage());
//        }
        if ("enhance".equals(translateParams.getMode()) && translateParams.getContent().length() > 600) {
            return R.fail(400, "enhance cannot exceed 600 !");
        }
        String caption = img2TextService.translateToLanguage(translateParams, user);
        return R.success(caption);
    }


    @Operation(summary = "吉卜力风格转换接口")
    @PostMapping("/img-to-ghibli")
    @Authorization
//    @LeakyBucketLimit(intervalSeconds = 20, queueSize = 5)
    public R<String> imgToGhibli(@RequestParam @Parameter(description = "图片url") String imageUrl,
                                 @CurrentUser @Parameter(hidden = true) User user) {
        //具有操作权限
        Boolean operatePermission = vipService.judgeUserOperatePermission(user, 10);

        //不足点数不支持
        if (!operatePermission) {
            return R.fail(Integer.valueOf(LogicErrorCode.NOT_ENOUGH_LUMENS_SUPPORT.getCode()),
                    LogicErrorCode.NOT_ENOUGH_LUMENS_SUPPORT.getMessage());
        }

        String caption = img2TextService.imgToGhibli(imageUrl, user);
        return R.success(caption);
    }


}
