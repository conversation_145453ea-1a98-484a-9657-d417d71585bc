package com.lx.pl.db.mysql.community.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.JsonNode;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Document(collection = "user_platform_message")
public class UserPlatformMessage {

    @Id
    private String id;

    /**
     * 用户信息
     */
    private AccountInfo accountInfo;

    /**
     * 标题信息
     */
    private String title;

    /**
     * 简介
     */
    private String introduction;

    /**
     * 详情
     */
    private String details;

    /**
     * 图片id
     */
    private String fileId;

    /**
     * 数据库的消息id(做关联用)
     */
    private String messageId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 已读时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime readTime;

    /**
     * 目标用户是否已读
     */
    private Boolean read;

    /**
     * 小图路径
     */
    private String miniThumbnailUrl;

    /**
     * 私信类型: 1 : 精选  2 : 普通私信  3.会员升级通知  4.活动获奖通知
     */
    private Integer messType;

    /**
     * 会员类型：basic-非会员，standard-普通会员，pro-高级会员
     */
    private String planLevel;

    /**
     * 时间区间：year, month
     */
    private String  priceInterval;

    /**
     * 会员生效时间
     */
    private Long vipEndTime;

    /**
     * 社区活动名称
     */
    private String commActivityTitle;

    /**
     * 社区活动奖项名称
     */
    private String commActivityLevelName;

    /**
     * 消息所属平台，多个端以英逗号分割
     */
    private List<String> platform;
}
