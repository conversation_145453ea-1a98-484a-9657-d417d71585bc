package com.lx.pl.util;

import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.OutputFile;

import java.io.File;
import java.util.Collections;

public class MapperGenerator {

    String url = "************************************************************************************************************************************************************************";
    String username = "ne";
    String password = "HiwjqefiwojfsdfG88#";

    public static void main(String[] args) {
        MapperGenerator m = new MapperGenerator();
        m.gen();
    }

    public void gen() {
        String userDir = System.getProperty("user.dir");//获取当前工作目录
        String outputDir = userDir + File.separator + "src" + File.separator + "main";

        FastAutoGenerator.create(url, username, password)
                .globalConfig(builder -> {
                    builder.author("xiang") // 设置作者
//                            .enableSwagger() // 开启 swagger 模式
                            .fileOverride() // 覆盖已生成文件
                            .disableOpenDir()
                            .outputDir(outputDir + File.separator + "java"); // 指定输出目录
                })
                .packageConfig(builder -> {
                    builder.parent("com.lx.pl.db.mysql.gen") // 设置父包名
//                            .moduleName("system") // 设置父包模块名
                            .pathInfo(Collections.singletonMap(OutputFile.mapperXml, outputDir +
                                    File.separator + "resources" + File.separator + "mapper")); // 设置mapperXml生成路径
                })
                .strategyConfig(builder -> {
                    builder.addTablePrefix("sys_", "gpt_");
                })
                .templateConfig(builder -> {
                    builder.controller("");
                    builder.service("");
                    builder.serviceImpl("");
                })
                .execute();
    }
}
