package com.lx.pl.util;

import com.lx.pl.config.BadWordsLoader;
import com.lx.pl.config.StartupDataLoader;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class PromptFiltrationUtil {

    // 使用 HashSet 初始化儿童相关的词汇
    private static final Set<String> childKeywords = new HashSet<>(
            Set.of("child", "children", "toddler", "preschooler", "preteen", "kid", "kindergartner", "pupil", "juvenile", "babe", "youngster", "little one", "tot", "kiddo",
                    "little boy", "little girl", "little boys", "little girls", "gal", "lass", "lad")
    );

    // 缓存已编译的正则表达式
    private static final Map<String, Pattern> patternCache = new ConcurrentHashMap<>();

    // 获取内存中存储的敏感词列表，并预先转换为小写
    private static final Set<String> explicitSearchWords = StartupDataLoader.getExplicitSearchWords().stream()
            .map(String::toLowerCase)
            .collect(Collectors.toSet());

    private static final Set<String> badWordsSet = BadWordsLoader.getBadWordsSet().stream()
            .map(String::toLowerCase)
            .collect(Collectors.toSet());

    private static final Set<String> adultBadWordsSet = BadWordsLoader.getPornBadWordsSet().stream()
            .map(String::toLowerCase)
            .collect(Collectors.toSet());

    // 过滤儿童色情敏感词
    public static Boolean filterChildSex(String prompt) {

        if (StringUtils.isBlank(prompt)) {
            return Boolean.FALSE;
        }

        // 将提示词转为小写，避免重复转换
        String lowerCasePrompt = prompt.toLowerCase();

        // 使用正则表达式拆分字符串成单词，这里考虑简单的英文单词分隔符（空格、标点等）
        String words = lowerCasePrompt.replaceAll("\\W+", " ");
        // 判断是否包含儿童相关的关键词
        boolean containsChildKeyword = childKeywords.stream()
                .anyMatch(keyword -> containsKeywordInWords(words, keyword));

        if (!containsChildKeyword) {
            // 如果提示词不包含儿童相关的词汇，直接返回 false
            return Boolean.FALSE;
        }

        // 判断提示词是否包含敏感词列表中的词
        boolean containsExplicitWord = explicitSearchWords.stream()
                .anyMatch(sensitiveWord -> containsKeywordInWords(words, sensitiveWord));

        return containsExplicitWord; // 如果有敏感词则返回 true，否则返回 false
    }


    /**
     * 成人色情词汇拦截
     *
     * @param prompt
     * @return
     */
    public static Boolean filterAdultBadWords(String prompt) {
        if (StringUtils.isBlank(prompt)) {
            return Boolean.FALSE;
        }

        // 将提示词转为小写，避免重复转换
        String lowerCasePrompt = prompt.toLowerCase();

        // 使用正则表达式拆分字符串成单词，这里考虑简单的英文单词分隔符（空格、标点等）
        String words = lowerCasePrompt.replaceAll("\\W+", " ");

        // 判断提示词是否包含badwords词列表中的词
        boolean containsExplicitWord = adultBadWordsSet.stream()
                .anyMatch(sensitiveWord -> containsKeywordInWords(words, sensitiveWord));

        return containsExplicitWord; // 如果有敏感词则返回 true，否则返回 false
    }

    /**
     * 色情词汇拦截
     *
     * @param prompt
     * @return
     */
    public static Boolean filterBadWords(String prompt) {
        if (StringUtils.isBlank(prompt)) {
            return Boolean.FALSE;
        }

        // 将提示词转为小写，避免重复转换
        String lowerCasePrompt = prompt.toLowerCase();

        // 使用正则表达式拆分字符串成单词，这里考虑简单的英文单词分隔符（空格、标点等）
        String words = lowerCasePrompt.replaceAll("\\W+", " ");

        // 判断提示词是否包含badwords词列表中的词
        boolean containsExplicitWord = badWordsSet.stream()
                .anyMatch(sensitiveWord -> containsKeywordInWords(words, sensitiveWord));

        return containsExplicitWord; // 如果有敏感词则返回 true，否则返回 false
    }

    public static String filterAndRemoveBadWords(String prompt) {
        if (StringUtils.isBlank(prompt)) {
            return "";
        }

        // 将提示词转为小写，避免大小写影响匹配
        String lowerCasePrompt = prompt.toLowerCase();

        // 遍历 badWordsSet，逐个替换敏感词
        for (String badWord : badWordsSet) {
            lowerCasePrompt = lowerCasePrompt.replace(badWord, "");
        }

        return lowerCasePrompt.trim();
    }

    // 检查单词数组中是否包含指定的关键字
    private static boolean containsKeywordInWords(String word, String keyword) {
        // 构造正则，确保匹配前面有非字母字符，且完全匹配关键字
        Pattern pattern = getPattern(keyword);
        return pattern.matcher(word) // 将单词数组重组成一个字符串
                .find();
    }

    //精准字符串匹配正则
    private static Pattern getPattern(String keyword) {
        // 构造正则，确保匹配前面有非字母字符，且完全匹配关键字
        String regex = "(?<![a-zA-Z])" + Pattern.quote(keyword) + "(?![a-zA-Z])";
        Pattern pattern = patternCache.computeIfAbsent(regex, k -> Pattern.compile(k, Pattern.CASE_INSENSITIVE));
        return pattern;
    }

}
