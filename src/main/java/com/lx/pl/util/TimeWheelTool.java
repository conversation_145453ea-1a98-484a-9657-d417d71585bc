package com.lx.pl.util;

import io.netty.util.HashedWheelTimer;
import io.netty.util.Timeout;
import io.netty.util.TimerTask;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

public class TimeWheelTool {

    private static final Logger logger = LoggerFactory.getLogger("schedule-task");
    public static HashedWheelTimer timer;
    public static Map<String, Timeout> taskMap;

    static {
        timer = new HashedWheelTimer();
        taskMap = new ConcurrentHashMap<>();
    }

    public static void scheduleTask(String taskId, Runnable task, long delay, TimeUnit unit) {
        logger.info("添加定时任务：taskid：" + taskId);
        TimerTask timerTask = timeout -> {
            task.run();
            taskMap.remove(taskId);
        };

        Timeout timeout = timer.newTimeout(timerTask, delay, unit);
        taskMap.put(taskId, timeout);
    }

    /**
     * @Description: 循环调用的话需要再task中重新设置调用方法
     * @Param: taskId, task, targetHour, targetMinute
     * @return: void
     * @Author: senlin_he
     * @Date: 2024/7/9
     */
    public static void dailyScheduleTask(String taskId, Runnable task, int targetHour, int targetMinute) {
        logger.info("添加每日定时任务：taskid：" + taskId);
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime targetTime = LocalDateTime.of(now.toLocalDate(), LocalTime.of(targetHour, targetMinute));
        if (now.toLocalTime().isAfter(LocalTime.of(targetHour, targetMinute))) {
            targetTime = targetTime.plusDays(1);
        }

        long initialDelay = now.until(targetTime, ChronoUnit.MINUTES);
        TimerTask timerTask = timeout -> {
            task.run();
            taskMap.remove(taskId);
        };

        Timeout timeout = timer.newTimeout(timerTask, initialDelay, TimeUnit.MINUTES);
        taskMap.put(taskId, timeout);
    }

    public static void stopTask(String taskId) {
        logger.info("停止定时任务：taskid：" + taskId);
        Timeout timeout = taskMap.get(taskId);
        if (timeout != null) {
            timeout.cancel();
            taskMap.remove(taskId);
        }
    }

    public static void stopAllTasks() {
        for (Timeout timeout : taskMap.values()) {
            timeout.cancel();
        }
        taskMap.clear();
    }

    public static void stop() {
        logger.info("停止所有定时任务");
        timer.stop();
        stopAllTasks();
    }
}