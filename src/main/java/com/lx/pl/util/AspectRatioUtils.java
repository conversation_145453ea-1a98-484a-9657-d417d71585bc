package com.lx.pl.util;

import com.lx.pl.dto.GenGenericPara;
import com.lx.pl.dto.Resolution;

/**
 * 宽高比工具类
 * 根据前端SHAPE_ALL配置处理分辨率和宽高比转换
 */
public class AspectRatioUtils {

    /**
     * 前端SHAPE_ALL配置对应的分辨率信息
     */
    public static class ShapeInfo {
        public final int width;
        public final int height;
        public final String label;
        public final String value;
        public final String baseVersion;

        public ShapeInfo(int width, int height, String label, String value, String baseVersion) {
            this.width = width;
            this.height = height;
            this.label = label;
            this.value = value;
            this.baseVersion = baseVersion;
        }
    }

    /**
     * 前端SHAPE_ALL配置数组
     */
    private static final ShapeInfo[] SHAPE_ALL = {
            new ShapeInfo(1024, 1024, "1:1", "1024 x 1024", "SDXL"),
            new ShapeInfo(1536, 640, "21:9", "1536 x 640", "SDXL"),
            new ShapeInfo(640, 1536, "9:21", "640 x 1536", "SDXL"),
            new ShapeInfo(1472, 704, "19:9", "1472 x 704", "SDXL"),
            new ShapeInfo(704, 1472, "9:19", "704 x 1472", "SDXL"),
            new ShapeInfo(1280, 640, "2:1", "1280 x 640", "SDXL"),
            new ShapeInfo(640, 1280, "1:2", "640 x 1280", "SDXL"),
            new ShapeInfo(1344, 768, "16:9", "1344 x 768", "SDXL"),
            new ShapeInfo(768, 1344, "9:16", "768 x 1344", "SDXL"),
            new ShapeInfo(1216, 768, "8:5", "1216 x 768", "SDXL"),
            new ShapeInfo(768, 1216, "5:8", "768 x 1216", "SDXL"),
            new ShapeInfo(1216, 832, "3:2", "1216 x 832", "SDXL"),
            new ShapeInfo(832, 1216, "2:3", "832 x 1216", "SDXL"),
            new ShapeInfo(1152, 896, "4:3", "1152 x 896", "SDXL"),
            new ShapeInfo(896, 1152, "3:4", "896 x 1152", "SDXL"),
            new ShapeInfo(1088, 960, "6:5", "1088 x 960", "SDXL"),
            new ShapeInfo(960, 1088, "5:6", "960 x 1088", "SDXL")
    };

    /**
     * 根据宽高获取对应的aspectRatio字符串
     *
     * @param width  宽度
     * @param height 高度
     * @return aspectRatio字符串，格式为"width x height"
     */
    public static String getAspectRatio(int width, int height) {
        // 首先尝试精确匹配
        for (ShapeInfo shape : SHAPE_ALL) {
            if (shape.width == width && shape.height == height) {
                return shape.value;
            }
        }

        // 如果没有精确匹配，返回自定义格式
        return width + " x " + height;
    }

    /**
     * 根据GenGenericPara获取aspectRatio
     *
     * @param genParameters 生图参数
     * @return aspectRatio字符串
     */
    public static String getAspectRatio(GenGenericPara genParameters) {
        if (genParameters == null || genParameters.getResolution() == null) {
            return "1024 x 1024"; // 默认值
        }

        Resolution resolution = genParameters.getResolution();
        return getAspectRatio(resolution.getWidth(), resolution.getHeight());
    }

    /**
     * 根据宽高获取对应的比例标签（如1:1, 16:9等）
     *
     * @param width  宽度
     * @param height 高度
     * @return 比例标签
     */
    public static String getAspectRatioLabel(int width, int height) {
        // 首先尝试精确匹配
        for (ShapeInfo shape : SHAPE_ALL) {
            if (shape.width == width && shape.height == height) {
                return shape.label;
            }
        }

//        // 如果没有精确匹配，计算最简比例
//        int gcd = gcd(width, height);
//        int ratioWidth = width / gcd;
//        int ratioHeight = height / gcd;
//        return ratioWidth + ":" + ratioHeight;
        return "1:1";
    }

    /**
     * 根据GenGenericPara获取比例标签
     *
     * @param genParameters 生图参数
     * @return 比例标签
     */
    public static String getAspectRatioLabel(GenGenericPara genParameters) {
        if (genParameters == null || genParameters.getResolution() == null) {
            return "1:1"; // 默认值
        }

        Resolution resolution = genParameters.getResolution();
        return getAspectRatioLabel(resolution.getWidth(), resolution.getHeight());
    }

    /**
     * 检查给定的分辨率是否在支持的SHAPE_ALL列表中
     *
     * @param width  宽度
     * @param height 高度
     * @return 是否支持
     */
    public static boolean isSupportedResolution(int width, int height) {
        for (ShapeInfo shape : SHAPE_ALL) {
            if (shape.width == width && shape.height == height) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取最接近的支持分辨率
     *
     * @param width  目标宽度
     * @param height 目标高度
     * @return 最接近的ShapeInfo，如果没有找到则返回默认的1:1
     */
    public static ShapeInfo getClosestSupportedResolution(int width, int height) {
        double targetRatio = (double) width / height;
        ShapeInfo closest = SHAPE_ALL[0]; // 默认1:1
        double minDifference = Double.MAX_VALUE;

        for (ShapeInfo shape : SHAPE_ALL) {
            double shapeRatio = (double) shape.width / shape.height;
            double difference = Math.abs(targetRatio - shapeRatio);

            if (difference < minDifference) {
                minDifference = difference;
                closest = shape;
            }
        }

        return closest;
    }

    /**
     * 计算最大公约数
     *
     * @param a 数字a
     * @param b 数字b
     * @return 最大公约数
     */
    private static int gcd(int a, int b) {
        while (b != 0) {
            int temp = b;
            b = a % b;
            a = temp;
        }
        return a;
    }

    /**
     * 获取所有支持的分辨率信息
     *
     * @return ShapeInfo数组
     */
    public static ShapeInfo[] getAllSupportedResolutions() {
        return SHAPE_ALL.clone();
    }
}
