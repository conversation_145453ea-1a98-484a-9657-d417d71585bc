package com.lx.pl.util;


public class ShardingCalculatorUtil {

    /**
     * 根据 login_name 计算 ShardingSphere 路由的表名
     *
     * @param loginName 用户的 login_name
     * @param tablePrefix 表名前缀，例如 gpt_prompt_file
     * @param totalTables 总表数，例如 20
     * @return 计算出的表名
     */
    public static String calculateShardingTable(String loginName, String tablePrefix, Integer totalTables) {
        // 使用 MD5 哈希算法计算 loginName 的哈希值
        Integer hash =loginName.hashCode();
        // 根据总表数计算表索引
        Integer tableIndex = Math.abs(hash % totalTables);
        // 返回最终的表名
        return tablePrefix + "_" + tableIndex;
    }


}
