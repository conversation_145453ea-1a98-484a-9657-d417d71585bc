package com.lx.pl.util;

import java.util.regex.Pattern;

/**
 * @Description: url路径校验类
 * @Author: senlin_he
 * @Date: 2025/2/21
 */
public class UrlValidator {

    private static final Pattern VALID_URL_PATTERN = Pattern.compile(
            "^https://([a-zA-Z0-9-]+\\.)*piclumen\\.com/.+$"
    );

    /**
     * 静态方法：校验图片 URL 是否符合规则
     *
     * @param imageUrl 图片 URL
     * @return 如果 URL 符合规则返回 true，否则返回 false
     */
    public static boolean isValidImageUrl(String imageUrl) {
        if (imageUrl == null || imageUrl.isEmpty()) {
            return false;
        }
        return VALID_URL_PATTERN.matcher(imageUrl).matches();
    }

    /**
     * 校验图片 URL 并抛出异常（如果不符合规则）
     *
     * @param imageUrl 图片 URL
     * @throws IllegalArgumentException 如果 URL 不符合规则
     */
    public static void validateImageUrl(String imageUrl) {
        // 检查当前环境是否为 prod
        if (!MyEnvironmentUtils.isProdEnvironment()) {
            return; // 非 prod 环境不校验
        }
        if (!isValidImageUrl(imageUrl)) {
            throw new IllegalArgumentException("Invalid params");
        }
    }
}