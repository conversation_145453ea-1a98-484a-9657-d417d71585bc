package com.lx.pl.util;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class JsonUtils {

    private static ObjectMapper objectMapper;

    @Autowired
    public JsonUtils(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    /**
     * 将String转换成实体类
     *
     * @param jsonString
     * @param clazz
     * @param <T>
     * @return
     * @throws JsonProcessingException
     */
    public static <T> T fromString(String jsonString, Class<T> clazz) throws JsonProcessingException {
        return objectMapper.readValue(jsonString, clazz);
    }

    /**
     * 将String转换成实体类(支持嵌套)
     *
     * @param jsonString
     * @param typeReference
     * @param <T>
     * @return
     * @throws JsonProcessingException
     */
    public static <T> T writeToTypeReference(String jsonString, TypeReference<T> typeReference) throws JsonProcessingException {
        return objectMapper.readValue(jsonString, typeReference);
    }

    /**
     * 将json转换成实体类
     *
     * @param jsonNode
     * @param clazz
     * @param <T>
     * @return
     * @throws JsonProcessingException
     */
    public static <T> T fromJsonNode(JsonNode jsonNode, Class<T> clazz) throws JsonProcessingException {
        return objectMapper.treeToValue(jsonNode, clazz);
    }

    /**
     * 将json数据转换成list
     */
    public static <T> List<T> writeToList(String json, Class<T> beanType) throws JsonProcessingException {
        JavaType javaType = objectMapper.getTypeFactory().constructParametricType(List.class, beanType);
        return objectMapper.readValue(json, javaType);
    }

    /**
     * 将对象转换成string
     *
     * @param value
     * @return
     * @throws JsonProcessingException
     */
    public static String writeToString(Object value) throws JsonProcessingException {
        return objectMapper.writeValueAsString(value);
    }

    /**
     * 将对象转换成json
     *
     * @param value
     * @return
     * @throws JsonProcessingException
     */
    public static JsonNode writeToJsonNode(Object value) throws JsonProcessingException {
        return objectMapper.valueToTree(value);
    }

    /**
     * 将字符串转换成json
     *
     * @param value
     * @return
     * @throws JsonProcessingException
     */
    public static JsonNode writeStringToJsonNode(String value) throws JsonProcessingException {
        return objectMapper.readTree(value);
    }

    /**
     * 将一个对象转换为另一个类型的对象
     *
     * @param fromValue 源对象
     * @param toValueType 目标类型
     * @param <T> 目标类型泛型
     * @return 转换后的对象
     */
    public static <T> T convertValue(Object fromValue, Class<T> toValueType) {
        return objectMapper.convertValue(fromValue, toValueType);
    }

    /**
     * 将一个对象转换为另一个类型的对象（支持泛型）
     *
     * @param fromValue 源对象
     * @param toValueTypeRef 目标类型引用
     * @param <T> 目标类型泛型
     * @return 转换后的对象
     */
    public static <T> T convertValue(Object fromValue, TypeReference<T> toValueTypeRef) {
        return objectMapper.convertValue(fromValue, toValueTypeRef);
    }

    /**
     * 将JSON字符串反序列化为指定类型的对象
     *
     * @param content JSON字符串
     * @param valueType 目标类型
     * @param <T> 目标类型泛型
     * @return 反序列化后的对象
     * @throws JsonProcessingException JSON处理异常
     */
    public static <T> T readValue(String content, Class<T> valueType) throws JsonProcessingException {
        return objectMapper.readValue(content, valueType);
    }

    /**
     * 将JSON字符串反序列化为指定类型的对象（支持泛型）
     *
     * @param content JSON字符串
     * @param valueTypeRef 目标类型引用
     * @param <T> 目标类型泛型
     * @return 反序列化后的对象
     * @throws JsonProcessingException JSON处理异常
     */
    public static <T> T readValue(String content, TypeReference<T> valueTypeRef) throws JsonProcessingException {
        return objectMapper.readValue(content, valueTypeRef);
    }

}
