package com.lx.pl.util;

import java.lang.management.ManagementFactory;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.Date;

import org.apache.commons.lang3.time.DateFormatUtils;

/**
 * 时间工具类
 */
public class DateUtils extends org.apache.commons.lang3.time.DateUtils {

    public static String YYYY = "yyyy";

    public static String YYYY_MM = "yyyy-MM";

    public static String YYYY_MM_DD = "yyyy-MM-dd";

    public static String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";

    public static String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

    public static String YYYY_MM_DD_00_00_00 = "yyyy-MM-dd 00:00:00";

    private static String[] parsePatterns = {
            "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM",
            "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM",
            "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM"};

    /**
     * 获取当前Date型日期
     *
     * @return Date() 当前日期
     */
    public static Date getNowDate() {
        return new Date();
    }

    /**
     * 获取当前日期, 默认格式为yyyy-MM-dd
     *
     * @return String
     */
    public static String getDate() {
        return dateTimeNow(YYYY_MM_DD);
    }

    public static final String getTime() {
        return dateTimeNow(YYYY_MM_DD_HH_MM_SS);
    }

    public static final String dateTimeNow() {
        return dateTimeNow(YYYYMMDDHHMMSS);
    }

    public static final String dateTimeNow(final String format) {
        return parseDateToStr(format, new Date());
    }

    public static final String dateTime(final Date date) {
        return parseDateToStr(YYYY_MM_DD, date);
    }

    public static final String parseDateToStr(final String format, final Date date) {
        return new SimpleDateFormat(format).format(date);
    }

    public static final Date dateTime(final String format, final String ts) {
        try {
            return new SimpleDateFormat(format).parse(ts);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 日期路径 即年/月/日 如2018/08/08
     */
    public static final String datePath() {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyy/MM/dd");
    }

    /**
     * 日期路径 即年/月/日 如20180808
     */
    public static final String dateTime() {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyyMMdd");
    }

    /**
     * 日期型字符串转化为日期 格式
     */
    public static Date parseDate(Object str) {
        if (str == null) {
            return null;
        }
        try {
            return parseDate(str.toString(), parsePatterns);
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 获取服务器启动时间
     */
    public static Date getServerStartDate() {
        long time = ManagementFactory.getRuntimeMXBean().getStartTime();
        return new Date(time);
    }

    /**
     * 计算相差天数
     */
    public static int differentDaysByMillisecond(Date date1, Date date2) {
        return Math.abs((int) ((date2.getTime() - date1.getTime()) / (1000 * 3600 * 24)));
    }

    /**
     * 计算两个时间差
     */
    public static String getDatePoor(Date endDate, Date nowDate) {
        long nd = 1000 * 24 * 60 * 60;
        long nh = 1000 * 60 * 60;
        long nm = 1000 * 60;
        // long ns = 1000;
        // 获得两个时间的毫秒时间差异
        long diff = endDate.getTime() - nowDate.getTime();
        // 计算差多少天
        long day = diff / nd;
        // 计算差多少小时
        long hour = diff % nd / nh;
        // 计算差多少分钟
        long min = diff % nd % nh / nm;
        // 计算差多少秒//输出结果
        // long sec = diff % nd % nh % nm / ns;
        return day + "天" + hour + "小时" + min + "分钟";
    }

    /**
     * 增加 LocalDateTime ==> Date
     */
    public static Date toDate(LocalDateTime temporalAccessor) {
        ZonedDateTime zdt = temporalAccessor.atZone(ZoneId.systemDefault());
        return Date.from(zdt.toInstant());
    }

    /**
     * 增加 LocalDate ==> Date
     */
    public static Date toDate(LocalDate temporalAccessor) {
        LocalDateTime localDateTime = LocalDateTime.of(temporalAccessor, LocalTime.of(0, 0, 0));
        ZonedDateTime zdt = localDateTime.atZone(ZoneId.systemDefault());
        return Date.from(zdt.toInstant());
    }

    /**
     * 获取一个永久时间
     *
     * @return
     */
    public static Date getPermanentTime() {
        Calendar calendar = Calendar.getInstance();
        //设置一个永久的时间
        calendar.set(9999, Calendar.DECEMBER, 31);
        return calendar.getTime();
    }

    public static Date getBeijingTime() {
        // 假设服务器当前时间是北京时间
        ZonedDateTime serverTime = ZonedDateTime.now(ZoneId.of("Asia/Shanghai"));
        // 转换为北京时间
        ZonedDateTime beijingTime = serverTime.withZoneSameInstant(ZoneId.of("Asia/Shanghai"));
        return Date.from(beijingTime.toInstant());
    }


    public static final String formatDateByPattern(Date date, String pattern) {
        return DateFormatUtils.format(date, pattern);
    }

    /**
     * 获取日期所在前n天凌晨
     */
    public static Date getNumsDayDateBefore(Date date, Integer nums) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DATE, nums); // 减去nums天
        return calendar.getTime();
    }

    /**
     * 获取日期所在星期的第一天凌晨
     */
    public static Date getFirstDayDateOfWeek(Date date) {
        final Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        final int last = cal.getActualMinimum(Calendar.DAY_OF_WEEK);
        cal.set(Calendar.DAY_OF_WEEK, last);
        return cal.getTime();
    }

    /**
     * 获取日期所在月的第一天凌晨
     */
    public static Date getFirstDayDateOfMonth(Date date) {
        final Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        final int last = cal.getActualMinimum(Calendar.DAY_OF_MONTH);
        cal.set(Calendar.DAY_OF_MONTH, last);
        return cal.getTime();
    }

    /**
     * 将日期转换为服务器本地日期
     *
     * @param date
     * @return
     */
    public static LocalDate dateToLocalDate(Date date) {
        Instant instant = date.toInstant();
        LocalDate localDate = instant.atZone(ZoneId.systemDefault()).toLocalDate();
        return localDate;
    }

    /**
     * 将字符串转换成日期
     *
     * @param dateStr
     * @return
     */
    public static LocalDateTime stringToLocalDate(String dateStr) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(YYYY_MM_DD_HH_MM_SS);
        return LocalDateTime.parse(dateStr, formatter);
    }

    /**
     * 获取当前N天前北京时间凌晨的数据
     *
     * @return
     */
    public static LocalDateTime getNBeforeLocalDate(int n) {
        // 获取当前北京时间
        LocalDateTime now = LocalDateTime.now(ZoneId.of("Asia/Shanghai"));

        // 计算 N 天前的日期
        LocalDateTime nDaysAgo = now.minusDays(n);

        // 调整时间为当天的零点（即午夜 00:00:00）
        LocalDateTime nDaysAgoMidnight = nDaysAgo.truncatedTo(ChronoUnit.DAYS);

        return nDaysAgoMidnight;
    }

    //获取给定时间的凌晨时间
    public static LocalDateTime getStartOfDay(Date date) {
        return dateToLocalDate(date).atStartOfDay();
    }

    public static LocalDateTime getStartOfDay() {
        return LocalDate.now().atStartOfDay();
    }

    /**
     * 将 LocalDateTime 转换为毫秒级时间戳（北京时间）
     *
     * @param dateTime LocalDateTime 时间对象
     * @return 时间戳（毫秒）
     */
    public static long getTimestampMillis(LocalDateTime dateTime) {
        if (dateTime == null) {
            throw new IllegalArgumentException("DateTime cannot be null");
        }
        return dateTime.atZone(ZoneId.of("Asia/Shanghai")).toInstant().toEpochMilli();
    }

    public static String convertBeijingToUTC(String beijingDate) {
        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(YYYY_MM_DD);

        // 解析日期字符串为 LocalDate（没有时区信息）
        LocalDate localDate = LocalDate.parse(beijingDate, formatter);

        // 将 LocalDate 视为 北京时间 (Asia/Shanghai) 的 00:00
        ZonedDateTime beijingTime = localDate.atStartOfDay(ZoneId.of("Asia/Shanghai"));

        // 转换为 UTC 时间
        ZonedDateTime utcTime = beijingTime.withZoneSameInstant(ZoneId.of("UTC"));

        // 格式化为 yyyy-MM-dd 格式字符串
        return utcTime.format(formatter);
    }

    public static Date getGreenwichTime() {
        // 假设服务器当前时间是北京时间
        ZonedDateTime serverTime = ZonedDateTime.now(ZoneId.of("Asia/Shanghai"));
        // 转换为格林威治标准时间 (UTC)
        ZonedDateTime greenwichTime = serverTime.withZoneSameInstant(ZoneId.of("UTC"));
        return Date.from(greenwichTime.toInstant());
    }

    /**
     * 将 LocalDateTime 转换为秒级时间戳（北京时间）
     *
     * @param dateTime LocalDateTime 时间对象
     * @return 时间戳（秒）
     */
    public static long getTimestamp(LocalDateTime dateTime) {
        if (dateTime == null) {
            throw new IllegalArgumentException("DateTime cannot be null");
        }
        return dateTime.atZone(ZoneId.of("Asia/Shanghai")).toInstant().getEpochSecond();
    }

    /**
     * 将LocalDateTime 转换成 Date
     * @param temporalAccessor
     * @return
     */
    public static Date localDateTimetoDate(LocalDateTime temporalAccessor) {
        ZonedDateTime zdt = temporalAccessor.atZone(ZoneId.of("Asia/Shanghai"));
        return Date.from(zdt.toInstant());
    }

    public static LocalDateTime convertToUtcLocalDateTime(long timestamp) {
        // 将时间戳转换为 Instant
        Instant instant = Instant.ofEpochMilli(timestamp);

        // 将 Instant 转换为 UTC 的 LocalDateTime
        LocalDateTime utcLocalDateTime = LocalDateTime.ofInstant(instant, ZoneOffset.UTC);

        return utcLocalDateTime;
    }

    /**
     * 将基于UTC的时间戳转换为北京时间的LocalDateTime
     *
     * @param utcEpochSecond UTC时间戳（秒）
     * @return 北京时间的LocalDateTime
     */
    public static LocalDateTime convertUtcToBeijingLocalDateTime(long utcEpochSecond) {
        // 将UTC时间戳转换为Instant
        Instant instant = Instant.ofEpochSecond(utcEpochSecond);

        // 将Instant转换为北京时间的LocalDateTime
        ZonedDateTime beijingZonedDateTime = instant.atZone(ZoneId.of("Asia/Shanghai"));
        return beijingZonedDateTime.toLocalDateTime();
    }

}
