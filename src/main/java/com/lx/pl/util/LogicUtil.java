package com.lx.pl.util;

/**
 * <AUTHOR>
 * @date 2025/6/6
 * @description
 */
public class LogicUtil {

    /**
     * 根据像素计算点数
     */
    public static int calculateCostLumenByPixel(int picWidth, int picHeight) {
        //计算总像素在200W以下，200W-300W，300W-400W，400W以上的数量
        if (picWidth * picHeight <= 2000000) {
            return 1;
        } else if (picWidth * picHeight <= 3000000) {
            return 2;
        } else if (picWidth * picHeight <= 4000000) {
            return 3;
        } else {
            return 4;
        }
    }
}
