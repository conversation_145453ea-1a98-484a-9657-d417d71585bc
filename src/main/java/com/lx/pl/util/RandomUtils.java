package com.lx.pl.util;

import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.IntStream;

/**
 * 随机类
 *
 * <AUTHOR>
 */
public class RandomUtils {

    /**
     * 随机类，去掉最有一页不要，全随机
     *
     * <AUTHOR>
     */
    public static List<Integer> calculateRandomPagesAll(int total, int pageSize) {
        int totalPages = (int) Math.ceil((double) total / pageSize);
        List<Integer> pages = new ArrayList<>();
        IntStream.rangeClosed(1, totalPages)
                .filter(page -> page != totalPages || total % pageSize == 0)
                .forEach(pages::add);
        SecureRandom secureRandom = new SecureRandom();
        Collections.shuffle(pages, secureRandom);
        return pages;
    }

    /**
     * 随机类，保证最后一个分页不参与随机
     *
     * <AUTHOR>
     */
    public static List<Integer> calculateRandomPagesWithOutLast(int total, int pageSize) {
        int totalPages = (int) Math.ceil((double) total / pageSize);
        List<Integer> pages = new ArrayList<>();
        IntStream.rangeClosed(1, totalPages)
                .forEach(pages::add);

        // 将前面的页数进行随机排序
        List<Integer> randomPages = new ArrayList<>(pages.subList(0, pages.size() - 1));
        SecureRandom secureRandom = new SecureRandom();
        Collections.shuffle(randomPages, secureRandom);
        randomPages.add(pages.get(pages.size() - 1)); // 将最后一页添加回去

        return randomPages;
    }

    private static List<Integer> weightedRandomSelection(List<Integer> pages, int[] weights) {
        SecureRandom random = new SecureRandom();
        List<Integer> selectedPages = new ArrayList<>();
        List<Integer> remainingPages = new ArrayList<>(pages);
        int[] remainingWeights = weights.clone();

        while (!remainingPages.isEmpty()) {
            // 预计算累积权重
            int[] cumulativeWeights = new int[remainingWeights.length];
            cumulativeWeights[0] = remainingWeights[0];
            for (int i = 1; i < remainingWeights.length; i++) {
                cumulativeWeights[i] = cumulativeWeights[i - 1] + remainingWeights[i];
            }

            // 随机选择一个值
            int randValue = random.nextInt(cumulativeWeights[cumulativeWeights.length - 1]);

            // 使用二分查找确定随机值对应的页码
            int selectedIndex = binarySearch(cumulativeWeights, randValue);

            // 选中页码并添加到结果中
            selectedPages.add(remainingPages.remove(selectedIndex));

            // 移除已选页码的权重
            int[] newWeights = new int[remainingWeights.length - 1];
            for (int i = 0, j = 0; i < remainingWeights.length; i++) {
                if (i != selectedIndex) {
                    newWeights[j++] = remainingWeights[i];
                }
            }
            remainingWeights = newWeights;
        }

        return selectedPages;
    }

    // 二分查找累积权重
    private static int binarySearch(int[] cumulativeWeights, int target) {
        int low = 0, high = cumulativeWeights.length - 1;
        while (low < high) {
            int mid = (low + high) / 2;
            if (target < cumulativeWeights[mid]) {
                high = mid;
            } else {
                low = mid + 1;
            }
        }
        return low;
    }

}
