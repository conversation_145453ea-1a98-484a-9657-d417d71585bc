package com.lx.pl.util;

import com.lx.pl.db.mysql.gen.entity.User;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.io.*;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

public class AESUtil {
    private static final String FIXED_KEY = "fclzCSIsBUV1Xk1T"; // 固定密钥，长度16字节
    private static final String ALGORITHM = "AES";
    private static final String TRANSFORMATION = "AES/ECB/PKCS5Padding";

    // 加密方法：对 String 进行加密
    public static String encryptString(String plainText) throws Exception {
        Cipher cipher = Cipher.getInstance(TRANSFORMATION);
        SecretKeySpec secretKey = new SecretKeySpec(FIXED_KEY.getBytes(), ALGORITHM);
        cipher.init(Cipher.ENCRYPT_MODE, secretKey);

        // 将字符串转换为字节数组进行加密
        byte[] encryptedBytes = cipher.doFinal(plainText.getBytes());

        // 使用 Base64 编码加密后的字节数组，以便于存储和传输
        return Base64.getEncoder().encodeToString(encryptedBytes);
    }


    public static void main(String[] args) throws Exception {
        User user = new User();
        user.setSex("man");
        List<User> list = new ArrayList<>();
        list.add(user);
        // Encrypt the object
        String encryptedData = encryptString(String.valueOf(list));

        System.out.println("Decrypted Object: " + encryptedData);
    }
}
