package com.lx.pl.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.w3c.dom.NodeList;

import javax.imageio.IIOImage;
import javax.imageio.ImageIO;
import javax.imageio.ImageReader;
import javax.imageio.ImageWriter;
import javax.imageio.metadata.IIOInvalidTreeException;
import javax.imageio.metadata.IIOMetadata;
import javax.imageio.metadata.IIOMetadataFormatImpl;
import javax.imageio.metadata.IIOMetadataNode;
import javax.imageio.stream.ImageInputStream;
import javax.imageio.stream.ImageOutputStream;
import java.io.File;
import java.io.IOException;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Iterator;

@Service
@Slf4j
public class IIOMetadataUpdater {

    public static void main(final String[] args) throws IOException {
        LocalDateTime now = LocalDateTime.now();
        log.info("500次写入开始时间{}", now);
        for (int i = 0; i <= 500; i++
        ) {
            File in = new File("C:\\Users\\<USER>\\Downloads\\piclumen-1727145138533.png");
            log.info("第{}次写入开始时间：{}", i, LocalDateTime.now());
            writeImageMetadata(in, "1girl, in anime style, headshot, attractive, cyberpunk and neno light, gather beautiful cleavage --piclumen --aspect 9:16 --negative-prompt \"watermark, 3d,  monochrome\" --guidance 4.5 --steps 30 --seed 123456 -bs 4 -mn \"flux.1-schnell\"");
        }
        LocalDateTime end = LocalDateTime.now();
        log.info("500次写入开始时间{}", end);
        log.info("500次写入耗时{}", Duration.between(now, end));
        log.info("500次写入平均耗时{}", (Duration.between(now, end).toMillis()) / 1000);
    }


    public static void writeImageMetadata(File file, String parameters) throws IOException {
//    LocalDateTime now = LocalDateTime.now();
//    log.info("写入数据开始时间：{}",now);
        try (ImageInputStream input = ImageIO.createImageInputStream(file);
             ImageOutputStream output = ImageIO.createImageOutputStream(file)) {

            Iterator<ImageReader> readers = ImageIO.getImageReaders(input);
            ImageReader reader = readers.next();

            reader.setInput(input);
            IIOImage image = reader.readAll(0, null);

            addTextEntry(image.getMetadata(), "parameters", parameters);

            ImageWriter writer = ImageIO.getImageWriter(reader);
            writer.setOutput(output);
            writer.write(image);
        }

        try (ImageInputStream input = ImageIO.createImageInputStream(file)) {
            Iterator<ImageReader> readers = ImageIO.getImageReaders(input);
            ImageReader reader = readers.next();

            reader.setInput(input);
            String value = getTextEntry(reader.getImageMetadata(0), "foo");
        }
//    LocalDateTime end = LocalDateTime.now();
//    log.info("写入数据结束时间：{}",end);
//    log.info("写入数据耗时：{}", Duration.between(now,end));
    }

    private static String createOutputName(final File file) {
        String name = file.getName();
        int dotIndex = name.lastIndexOf('.');

        String baseName = name.substring(0, dotIndex);
        String extension = name.substring(dotIndex);

        return baseName + "_copy" + extension;
    }

    private static void addTextEntry(final IIOMetadata metadata, final String key, final String value) throws IIOInvalidTreeException {
        IIOMetadataNode textEntry = new IIOMetadataNode("TextEntry");
        textEntry.setAttribute("keyword", key);
        textEntry.setAttribute("value", value);

        IIOMetadataNode text = new IIOMetadataNode("Text");
        text.appendChild(textEntry);

        IIOMetadataNode root = new IIOMetadataNode(IIOMetadataFormatImpl.standardMetadataFormatName);
        root.appendChild(text);

        metadata.mergeTree(IIOMetadataFormatImpl.standardMetadataFormatName, root);
    }

    private static String getTextEntry(final IIOMetadata metadata, final String key) {
        IIOMetadataNode root = (IIOMetadataNode) metadata.getAsTree(IIOMetadataFormatImpl.standardMetadataFormatName);
        NodeList entries = root.getElementsByTagName("TextEntry");

        for (int i = 0; i < entries.getLength(); i++) {
            IIOMetadataNode node = (IIOMetadataNode) entries.item(i);
            if (node.getAttribute("keyword").equals(key)) {
                return node.getAttribute("value");
            }
        }

        return null;
    }
}