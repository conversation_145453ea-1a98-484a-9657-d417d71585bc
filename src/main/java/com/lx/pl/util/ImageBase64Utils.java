package com.lx.pl.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.Base64;

/**
 * 图像Base64工具类
 * 
 * <AUTHOR>
 */
@Slf4j
public class ImageBase64Utils {
    
    /**
     * 将MultipartFile转换为Base64字符串
     */
    public static String fileToBase64(MultipartFile file) {
        try {
            byte[] bytes = file.getBytes();
            String contentType = file.getContentType();
            
            // 确定MIME类型
            String mimeType = "image/png";
            if (contentType != null) {
                if (contentType.contains("jpeg") || contentType.contains("jpg")) {
                    mimeType = "image/jpeg";
                } else if (contentType.contains("png")) {
                    mimeType = "image/png";
                } else if (contentType.contains("webp")) {
                    mimeType = "image/webp";
                }
            }
            
            String base64 = Base64.getEncoder().encodeToString(bytes);
            return "data:" + mimeType + ";base64," + base64;
            
        } catch (IOException e) {
            log.error("Error converting file to base64", e);
            throw new RuntimeException("文件转换失败");
        }
    }
    
    /**
     * 将URL图片转换为Base64字符串
     */
    public static String urlToBase64(String imageUrl) {
        try {
            URL url = new URL(imageUrl);
            
            try (InputStream inputStream = url.openStream();
                 ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
                
                byte[] imageBytes = outputStream.toByteArray();
                String base64 = Base64.getEncoder().encodeToString(imageBytes);
                
                // 根据URL扩展名确定MIME类型
                String mimeType = "image/png";
                String lowerUrl = imageUrl.toLowerCase();
                if (lowerUrl.endsWith(".jpg") || lowerUrl.endsWith(".jpeg")) {
                    mimeType = "image/jpeg";
                } else if (lowerUrl.endsWith(".png")) {
                    mimeType = "image/png";
                } else if (lowerUrl.endsWith(".webp")) {
                    mimeType = "image/webp";
                }
                
                return "data:" + mimeType + ";base64," + base64;
            }
            
        } catch (IOException e) {
            log.error("Error converting URL to base64: " + imageUrl, e);
            throw new RuntimeException("URL图片转换失败");
        }
    }
    
    /**
     * 验证Base64字符串是否为有效的图片格式
     */
    public static boolean isValidImageBase64(String base64String) {
        if (base64String == null || base64String.trim().isEmpty()) {
            return false;
        }
        
        // 检查是否包含data:image前缀
        if (!base64String.startsWith("data:image/")) {
            return false;
        }
        
        // 检查是否包含base64标识
        if (!base64String.contains("base64,")) {
            return false;
        }
        
        try {
            // 提取实际的base64数据
            String base64Data = base64String.substring(base64String.indexOf("base64,") + 7);
            Base64.getDecoder().decode(base64Data);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }
    
    /**
     * 从完整的Base64字符串中提取纯Base64数据（去掉data:image/...;base64,前缀）
     */
    public static String extractBase64Data(String fullBase64String) {
        if (fullBase64String == null || !fullBase64String.contains("base64,")) {
            return fullBase64String;
        }
        
        return fullBase64String.substring(fullBase64String.indexOf("base64,") + 7);
    }
    
    /**
     * 获取Base64字符串的MIME类型
     */
    public static String getMimeType(String base64String) {
        if (base64String == null || !base64String.startsWith("data:")) {
            return "image/png";
        }
        
        int semicolonIndex = base64String.indexOf(";");
        if (semicolonIndex > 0) {
            return base64String.substring(5, semicolonIndex); // 去掉"data:"前缀
        }
        
        return "image/png";
    }
    
    /**
     * 检查图片大小是否超过限制（字节）
     */
    public static boolean isImageSizeValid(String base64String, long maxSizeBytes) {
        if (!isValidImageBase64(base64String)) {
            return false;
        }
        
        try {
            String base64Data = extractBase64Data(base64String);
            byte[] imageBytes = Base64.getDecoder().decode(base64Data);
            return imageBytes.length <= maxSizeBytes;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }
}
