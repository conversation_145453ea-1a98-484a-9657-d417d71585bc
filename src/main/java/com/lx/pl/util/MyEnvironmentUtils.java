package com.lx.pl.util;

import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
public class MyEnvironmentUtils implements ApplicationContextAware {

    private static ApplicationContext applicationContext;

    /**
     * 检查当前环境是否为生产环境（prod）
     *
     * @return 如果当前环境为 prod 返回 true，否则返回 false
     */
    public static boolean isProdEnvironment() {
        Environment environment = applicationContext.getEnvironment();
        String[] activeProfiles = environment.getActiveProfiles();
        for (String profile : activeProfiles) {
            if ("prod".equalsIgnoreCase(profile)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查当前环境是否为预生产环境（staging）
     *
     * @return 如果当前环境为 staging 返回 true，否则返回 false
     */
    public static boolean isStagingEnvironment() {
        Environment environment = applicationContext.getEnvironment();
        String[] activeProfiles = environment.getActiveProfiles();
        for (String profile : activeProfiles) {
            if ("staging".equalsIgnoreCase(profile)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查当前环境是否为预生产环境（dev）
     *
     * @return 如果当前环境为 dev 返回 true，否则返回 false
     */
    public static boolean isDevEnvironment() {
        Environment environment = applicationContext.getEnvironment();
        String[] activeProfiles = environment.getActiveProfiles();
        for (String profile : activeProfiles) {
            if ("dev".equalsIgnoreCase(profile)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }
}