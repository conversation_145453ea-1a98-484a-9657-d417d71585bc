package com.lx.pl.util;

import com.lx.pl.dto.Base64MultipartFile;
import lombok.extern.slf4j.Slf4j;
import net.coobird.thumbnailator.Thumbnails;
import org.apache.batik.ext.awt.image.codec.png.PNGImageWriter;
import org.apache.batik.transcoder.TranscoderException;
import org.apache.batik.transcoder.TranscoderInput;
import org.apache.batik.transcoder.TranscoderOutput;
import org.apache.batik.transcoder.image.PNGTranscoder;
import org.apache.commons.imaging.ImageReadException;
import org.apache.commons.imaging.Imaging;
import org.apache.tomcat.util.codec.binary.Base64;
import org.springframework.util.Base64Utils;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import javax.imageio.ImageReader;
import javax.imageio.stream.ImageInputStream;
import javax.imageio.stream.ImageOutputStream;
import javax.swing.*;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.awt.image.ImageObserver;
import java.io.*;
import java.net.URL;
import java.util.Iterator;
import java.util.Locale;
import java.util.zip.GZIPInputStream;

@Slf4j
public class FileUtils {


    public static void saveByteDataToFile(byte[] data, String filePath) throws IOException {
        try (FileOutputStream fos = new FileOutputStream(filePath)) {
            fos.write(data);
        }
    }

    /**
     * 根据URL地址获取文件
     *
     * @param path URL网络地址
     * @return File
     */
    public static File getFileByHttpURL(String path) {
        String newUrl = path.split("[?]")[0];
        String[] suffix = newUrl.split("/");
        //得到最后一个分隔符后的名字
        String fileName = suffix[suffix.length - 1];
        File file = null;
        InputStream inputStream = null;
        OutputStream outputStream = null;
        try {
            file = File.createTempFile("report", fileName);//创建临时文件
            URL urlFile = new URL(newUrl);
            inputStream = urlFile.openStream();
            outputStream = new FileOutputStream(file);

            int bytesRead = 0;
            byte[] buffer = new byte[8192];
            while ((bytesRead = inputStream.read(buffer, 0, 8192)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
        } catch (Exception e) {
            log.error("获取文件失败", e);
        } finally {
            try {
                if (null != outputStream) {
                    outputStream.close();
                }
                if (null != inputStream) {
                    inputStream.close();
                }

            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return file;
    }


    /**
     * 获取缩略图
     *
     * @param file
     * @return File
     */
    public static File createThumbnail(File file) throws IOException {
        File outputFile = File.createTempFile("thumbnail", file.getName().substring(0, file.getName().lastIndexOf(".")) + ".webp");//创建临时文件
        // 使用Thumbnails生成缩略图
        Thumbnails.of(file)
                .scale(1) // 不改变宽高
                .outputFormat("webp") // 设置输出格式为webp
                .outputQuality(0.75f) // 设置输出质量为原来的0.75倍
                .toFile(outputFile);

        return outputFile;
    }

    /**
     * 获取高清缩略图
     *
     * @param file
     * @return File
     */
    public static File createHighThumbnail(File file) throws IOException {
        File outputFile = File.createTempFile("highThumbnail", file.getName().substring(0, file.getName().lastIndexOf(".")) + ".webp");//创建临时文件
        // 使用Thumbnails生成缩略图
        Thumbnails.of(file)
                .scale(1) // 不改变宽高
                .outputFormat("webp") // 设置输出格式为webp
                .outputQuality(0.9f) // 设置输出质量为原来的0.9倍
                .toFile(outputFile);

        return outputFile;
    }

    /**
     * 生成马赛克图片（先生成缩略图，再放大）
     *
     * @param file
     * @return File
     */
    public static File createMosaic(File file) throws IOException {
//    File outputFile = File.createTempFile("mosaic",file.getName().substring(0,file.getName().lastIndexOf(".")) + ".webp");//创建临时文件
//    File thumbnailFile = File.createTempFile("tempFile",file.getName().substring(0,file.getName().lastIndexOf(".")) + ".webp");
//    // 第一步：生成缩略图，质量设置为最低
//    Thumbnails.of(file)
//            .scale(0.01) // 等比例缩小100倍
//            .outputQuality(0.1f) // 设置输出质量为最低
//            .toFile(thumbnailFile); // 保存缩略图
//
//    // 第二步：从缩略图等比例放大到最终尺寸
//    Thumbnails.of(thumbnailFile)
//            .scale(100) // 等比例放大100倍
//            .toFile(outputFile); // 保存最终图片;
//
//    //删除旧有的图片
//    thumbnailFile.delete();
//    file.delete();

        return convertToMosaic(file);
    }

    public static File convertToMosaic(File file) throws IOException {
        BufferedImage originalImage = ImageIO.read(file);
        BufferedImage mosaicImage = createMosaicImage(originalImage, 180);

        File outputFile = File.createTempFile("mosaic", file.getName().substring(0, file.getName().lastIndexOf(".")) + ".webp");//创建临时文件
        ImageIO.write(mosaicImage, "webp", outputFile);

        file.delete();
        return outputFile;
    }

    private static BufferedImage createMosaicImage(BufferedImage originalImage, int tileSize) {
        int width = originalImage.getWidth();
        int height = originalImage.getHeight();
        BufferedImage mosaicImage = new BufferedImage(width, height, originalImage.getType());

        for (int y = 0; y < height; y += tileSize) {
            for (int x = 0; x < width; x += tileSize) {
                Color avgColor = getAverageColor(originalImage, x, y, tileSize);
                fillBlock(mosaicImage, x, y, tileSize, avgColor);
            }
        }

        return mosaicImage;
    }

    private static Color getAverageColor(BufferedImage image, int x, int y, int tileSize) {
        int r = 0, g = 0, b = 0, count = 0;

        for (int dy = 0; dy < tileSize; dy++) {
            for (int dx = 0; dx < tileSize; dx++) {
                if (x + dx < image.getWidth() && y + dy < image.getHeight()) {
                    Color color = new Color(image.getRGB(x + dx, y + dy));
                    r += color.getRed();
                    g += color.getGreen();
                    b += color.getBlue();
                    count++;
                }
            }
        }

        return new Color(r / count, g / count, b / count);
    }

    private static void fillBlock(BufferedImage image, int x, int y, int tileSize, Color color) {
        for (int dy = 0; dy < tileSize; dy++) {
            for (int dx = 0; dx < tileSize; dx++) {
                if (x + dx < image.getWidth() && y + dy < image.getHeight()) {
                    image.setRGB(x + dx, y + dy, color.getRGB());
                }
            }
        }
    }

    /**
     * 将图片转换成File类型
     *
     * @param multipartFile
     * @param prefix
     * @return
     * @throws IOException
     */
    public static File transferToFile(MultipartFile multipartFile, String prefix) throws IOException {
        File file = null;
        try {
            String originalFilename = multipartFile.getOriginalFilename();
            file = File.createTempFile(prefix, originalFilename);
            multipartFile.transferTo(file);
        } catch (IOException e) {
            log.error("Failed to convert multipart file to file: ", e);
        }
        return file;
    }

    public static File convertFileToPng(File inputFile) throws IOException, TranscoderException, ImageReadException, InterruptedException {
        // 2. 检测文件类型（GIF、AVIF、其他图像格式）
        String formatName = detectImageFormat(inputFile);

        File pngFile;
        if ("gif".equalsIgnoreCase(formatName)) {
            // 3.1 处理动图，提取第一帧并转换为 PNG
            pngFile = extractFirstFrameAndConvertToPng(inputFile);
        } else if ("svg".equalsIgnoreCase(formatName)) {
            pngFile = convertSvgToPng(new FileInputStream(inputFile));
        } else if ("ico".equalsIgnoreCase(formatName)) {
            pngFile = convertFileToPngByApacheCommons(inputFile);
        } else if ("xbm".equalsIgnoreCase(formatName)) {
            pngFile = convertXbmFileToPng(inputFile);
        } else if ("svgz".equalsIgnoreCase(formatName)) {
            pngFile = convertSvgzToPng(inputFile);
        } else {
            // 3.2 处理静态图片，直接转换为 PNG
            pngFile = convertNormalFileToPng(inputFile);
        }

        // 4. 删除临时文件（可选）
        inputFile.delete();

        return pngFile;
    }

    // 将svg，svgz格式的图片转换为 PNG 格式
    private static File convertSvgToPng(InputStream inputStream) throws TranscoderException, IOException {
        PNGTranscoder transcoder = new PNGTranscoder();
        TranscoderInput input = new TranscoderInput(inputStream);

        // 创建一个临时文件用于存储转换后的 PNG
        File pngFile = File.createTempFile("converted_", ".png");
        try (OutputStream outputStream = new FileOutputStream(pngFile)) {
            TranscoderOutput output = new TranscoderOutput(outputStream);
            // 执行转换
            transcoder.transcode(input, output);
        }

        // 返回生成的 PNG 文件
        return pngFile;
    }

    // 将任意格式的图片转换为 PNG 格式
    public static File convertNormalFileToPng(File inputFile) throws IOException {
        // 使用 Thumbnailator 处理普通图像文件转换为 PNG
        BufferedImage image = ImageIO.read(inputFile);
        File pngFile = File.createTempFile("converted_", ".png");
        Thumbnails.of(image).scale(1).outputFormat("png").toFile(pngFile);
        return pngFile;
    }

    //检测图片类型
    public static String detectImageFormat(File file) throws IOException {
        String fileName = file.getName().toLowerCase(Locale.ROOT);
        // 找到最后一个点的位置，以获取后缀
        int dotIndex = fileName.lastIndexOf(".");
        if (dotIndex == -1) {
            return "未知格式";  // 文件没有后缀
        }
        // 提取后缀并检查是否在支持的格式列表中
        String extension = fileName.substring(dotIndex + 1).toLowerCase();
        return extension;
    }

    // 提取动图的第一帧并转换为 PNG 格式
    private static File extractFirstFrameAndConvertToPng(File inputFile) throws IOException {
        BufferedImage firstFrame;
        try (ImageInputStream iis = ImageIO.createImageInputStream(inputFile)) {
            ImageReader reader = ImageIO.getImageReaders(iis).next();
            reader.setInput(iis);
            firstFrame = reader.read(0);  // 提取第一帧
        }

        File pngFile = File.createTempFile("first_frame_", ".png");
        ImageIO.write(firstFrame, "png", pngFile);
        return pngFile;
    }

    public static File convertFileToPngByApacheCommons(File inputFile) throws IOException, ImageReadException {
        // 使用 Apache Commons Imaging 处理 ICO 文件
        BufferedImage image = Imaging.getBufferedImage(inputFile);

        if (image == null) {
            throw new IOException("无法读取输入文件");
        }

        File pngFile = File.createTempFile("converted_", ".png");
        ImageIO.write(image, "png", pngFile);

        return pngFile;
    }

    public static File convertXbmFileToPng(File inputFile) throws IOException, InterruptedException {
        // 使用 Toolkit 读取 XBM 文件
        Image image = Toolkit.getDefaultToolkit().createImage(inputFile.getAbsolutePath());

        // 创建 ImageObserver 确保图像加载完成
        ImageObserver observer = (img, infoflags, x, y, width, height) -> (infoflags & ImageObserver.ALLBITS) != 0;

        // 等待直到图像完全加载
        while (image.getWidth(observer) == -1 || image.getHeight(observer) == -1) {
            try {
                Thread.sleep(50); // 等待图像加载
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new IOException("图像加载被中断", e);
            }
        }

        // 将 Image 转换为 BufferedImage
        BufferedImage bufferedImage = new BufferedImage(
                image.getWidth(observer), image.getHeight(observer), BufferedImage.TYPE_INT_ARGB);

        Graphics2D g2d = bufferedImage.createGraphics();
        g2d.drawImage(image, 0, 0, observer);
        g2d.dispose();

        // 保存为 PNG 文件
        File pngFile = File.createTempFile("converted_", ".png");
        ImageIO.write(bufferedImage, "png", pngFile);

        return pngFile;
    }

    public static File convertSvgzToPng(File svgzFile) throws IOException {
        // 解压缩 SVGZ 文件
        File svgFile = File.createTempFile("temp_svg_", ".svg");
        try (FileInputStream fis = new FileInputStream(svgzFile);
             GZIPInputStream gis = new GZIPInputStream(fis);
             FileOutputStream fos = new FileOutputStream(svgFile)) {
            byte[] buffer = new byte[1024];
            int len;
            while ((len = gis.read(buffer)) > 0) {
                fos.write(buffer, 0, len);
            }
        }

        // 使用 Batik 进行转换
        PNGTranscoder transcoder = new PNGTranscoder();
        TranscoderInput input = new TranscoderInput(svgFile.toURI().toString());

        File pngFile = File.createTempFile("converted_", ".png");
        try (FileOutputStream outputStream = new FileOutputStream(pngFile)) {
            TranscoderOutput output = new TranscoderOutput(outputStream);
            transcoder.transcode(input, output);
        } catch (Exception e) {
            throw new IOException("SVG 转换为 PNG 失败", e);
        }

        // 删除临时 SVG 文件
        svgFile.delete();

        return pngFile;
    }

    /**
     * 将 Base64 字符串转换为 MultipartFile
     *
     * @param base64      Base64 字符串（可包含前缀）
     * @param fileName    文件名
     * @param contentType 内容类型 (例如: image/png)
     * @return MultipartFile
     */
    public static MultipartFile convert(String base64, String fileName, String contentType) {
        try {
            // 去掉 Base64 前缀 (data:image/png;base64,)
            if (base64.contains(",")) {
                base64 = base64.split(",")[1];
            }

            // Base64 解码
            byte[] decodedBytes = Base64Utils.decodeFromString(base64);

            return new Base64MultipartFile(decodedBytes, fileName, contentType);
        } catch (Exception e) {
            throw new RuntimeException("Failed to convert Base64 to MultipartFile", e);
        }
    }
}
