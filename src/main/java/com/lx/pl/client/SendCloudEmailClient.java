package com.lx.pl.client;


import com.sendcloud.sdk.builder.SendCloudBuilder;
import com.sendcloud.sdk.config.Credential;
import com.sendcloud.sdk.config.Region;
import com.sendcloud.sdk.core.SendCloud;
import com.sendcloud.sdk.model.MailAddressReceiver;
import com.sendcloud.sdk.model.MailBody;
import com.sendcloud.sdk.model.SendCloudMail;
import com.sendcloud.sdk.model.TextContent;
import com.sendcloud.sdk.util.ResponseData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import retrofit2.http.Field;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class SendCloudEmailClient {

    @Value("${sendcloud.api.key}")
    String apiKey;
    @Value("${sendcloud.api.user}")
    String apiUser;

    public ResponseData sendEmail(
            @Field("from") String from,
            @Field("fromName") String fromName,
            @Field("to") String to,
            @Field("subject") String subject,
            @Field("html") String html) {
        MailAddressReceiver receiver = new MailAddressReceiver();
        receiver.addTo(to);
        MailBody body = new MailBody();
        body.setFrom(from);
        body.setFromName(fromName);
        body.setSubject(subject);
        body.setReplyTo(from);
        TextContent content = new TextContent();
        content.setContent_type(TextContent.ScContentType.html);
        content.setText(html);
        SendCloudMail mail = new SendCloudMail();
        mail.setTo(receiver);
        mail.setBody(body);
        mail.setContent(content);
        Credential credential = new Credential(apiUser, apiKey);
        SendCloud sc = SendCloudBuilder.build();
        try {
            ResponseData res = sc.sendMail(mail, credential, Region.CN);
            return res;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
