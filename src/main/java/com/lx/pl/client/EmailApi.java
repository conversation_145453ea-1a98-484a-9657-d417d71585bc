package com.lx.pl.client;

import com.github.lianjiatech.retrofit.spring.boot.core.RetrofitClient;
import com.github.lianjiatech.retrofit.spring.boot.interceptor.Intercept;
import com.lx.pl.Handler.EmailFallbackFactory;
import com.lx.pl.dto.SendCloudMailResult;
import com.lx.pl.interceptor.TokenInterceptor;
import retrofit2.Response;
import retrofit2.http.Field;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.POST;

@RetrofitClient(baseUrl = "${sendcloud.Url}", fallbackFactory = EmailFallbackFactory.class)
@Intercept(handler = TokenInterceptor.class)
public interface EmailApi {

    /**
     * 生图
     *
     * @param
     * @return 发送邮件
     */
    @FormUrlEncoded
    @POST("/apiv2/mail/send")
    Response<SendCloudMailResult> sendEmail(@Field("apiUser") String apiUser,
                                            @Field("apiKey") String apiKey,
                                            @Field("from") String from,
                                            @Field("fromName") String fromName,
                                            @Field("to") String to,
                                            @Field("subject") String subject,
                                            @Field("html") String html);
}
