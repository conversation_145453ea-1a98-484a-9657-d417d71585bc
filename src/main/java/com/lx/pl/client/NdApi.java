package com.lx.pl.client;

import com.github.lianjiatech.retrofit.spring.boot.core.RetrofitClient;
import com.github.lianjiatech.retrofit.spring.boot.interceptor.Intercept;
import com.lx.pl.Handler.BackendFallbackFactory;
import com.lx.pl.Handler.NdFallbackFactory;
import com.lx.pl.dto.NsfwResult;
import com.lx.pl.dto.generic.R;
import com.lx.pl.interceptor.TokenInterceptor;
import okhttp3.MultipartBody;
import retrofit2.Response;
import retrofit2.http.*;

import java.util.List;


@RetrofitClient(baseUrl = "${nd.url}", fallbackFactory = NdFallbackFactory.class)
@Intercept(handler = TokenInterceptor.class)
public interface NdApi {


    /**
     * 图片鉴黄接口
     *
     * @param filePart
     * @return
     */
    @Multipart
    @POST("/api/v1/detect")
    Response<R<List<NsfwResult>>> ndPicture(@Part MultipartBody.Part filePart);
}
