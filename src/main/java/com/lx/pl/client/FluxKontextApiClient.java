package com.lx.pl.client;

import com.lx.pl.dto.fluxkontext.FluxKontextRequest;
import com.lx.pl.dto.fluxkontext.FluxKontextResponse;
import retrofit2.Call;
import retrofit2.http.*;

/**
 * Flux Kontext Pro API客户端接口
 *
 * <AUTHOR>
 */
public interface FluxKontextApiClient {

    /**
     * 文本生图 / 图像编辑
     */
    @POST("/v1/flux-kontext-pro")
    Call<FluxKontextResponse.BaseResponseData> createTask(
            @Header("x-key") String apiKey,
            @Body Object request
    );

    /**
     * 获取任务结果
     */
    @GET("/v1/get_result")
    Call<FluxKontextResponse.TaskResultResponse> getResult(
            @Header("x-key") String apiKey,
            @Query("id") String requestId
    );
}
