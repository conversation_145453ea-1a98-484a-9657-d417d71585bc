package com.lx.pl.client;

import com.github.lianjiatech.retrofit.spring.boot.core.RetrofitClient;
import com.github.lianjiatech.retrofit.spring.boot.interceptor.Intercept;
import com.lx.pl.Handler.Img2TextFactory;
import com.lx.pl.interceptor.TokenInterceptor;
import com.lx.pl.openai.dto.CaptionRequest;
import com.lx.pl.openai.dto.CaptionResponse;
import retrofit2.Response;
import retrofit2.http.*;

@RetrofitClient(baseUrl = "${img2text.url}", fallbackFactory = Img2TextFactory.class)
@Intercept(handler = TokenInterceptor.class)
public interface Img2TextApi {

    /**
     * 图像描述生成或增强
     *
     * @param request 包含模型、消息、温度等参数的请求对象
     * @return 返回生成的描述或增强信息
     */
    @Headers("Content-Type: application/json")
    @POST("/v1/chat/completions")
    Response<CaptionResponse> generateCaption(
            @Body CaptionRequest request
    );
}
