package com.lx.pl.client;

import com.github.lianjiatech.retrofit.spring.boot.core.RetrofitClient;
import com.github.lianjiatech.retrofit.spring.boot.interceptor.Intercept;
import com.lx.pl.Handler.Img2TextFactory;
import com.lx.pl.Handler.ImgControlFactory;
import com.lx.pl.dto.ImgControlRequest;
import com.lx.pl.interceptor.TokenInterceptor;
import com.lx.pl.openai.dto.CaptionRequest;
import com.lx.pl.openai.dto.CaptionResponse;
import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.Response;
import retrofit2.http.Body;
import retrofit2.http.Headers;
import retrofit2.http.POST;

@RetrofitClient(baseUrl = "${ImgControl.url}", fallbackFactory = ImgControlFactory.class)
@Intercept(handler = TokenInterceptor.class)
public interface ImgControlApi {

    /**
     * 提取图像里的内容
     *
     * @param request 等参数的请求对象
     * @return 返回生成的描述或增强信息
     */
    @POST("/process")
    Call<ResponseBody> extractImgContent(@Body ImgControlRequest request);
}
