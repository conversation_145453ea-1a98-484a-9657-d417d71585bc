package com.lx.pl.client;

import com.github.lianjiatech.retrofit.spring.boot.core.RetrofitClient;
import com.github.lianjiatech.retrofit.spring.boot.interceptor.Intercept;
import com.lx.pl.Handler.TranslationFactory;
import com.lx.pl.dto.TranslationResult;
import com.lx.pl.interceptor.TokenInterceptor;
import retrofit2.Response;
import retrofit2.http.Field;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.POST;

@RetrofitClient(baseUrl = "${translation.url}", fallbackFactory = TranslationFactory.class)
@Intercept(handler = TokenInterceptor.class)
public interface TranslationApi {

    /**
     * 翻译或者增强
     *
     * @param
     * @return 返回翻译或者增强信息
     */
    @FormUrlEncoded
    @POST("/predict")
    Response<TranslationResult> translationPrompt(@Field("mode") String mode,
                                                  @Field("prompt") String prompt,
                                                  @Field("temperature") float temperature,
                                                  @Field("do_sample") String do_sample,
                                                  @Field("top_p") float top_p);
}
