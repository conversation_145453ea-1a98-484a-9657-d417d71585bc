package com.lx.pl.client;

import com.github.lianjiatech.retrofit.spring.boot.core.RetrofitClient;
import com.github.lianjiatech.retrofit.spring.boot.interceptor.Intercept;
import com.lx.pl.Handler.Img2GhibliFallbackFactory;
import com.lx.pl.Handler.Img2TextFactory;
import com.lx.pl.dto.Img2GhibliRequest;
import com.lx.pl.interceptor.TokenInterceptor;
import com.lx.pl.openai.dto.CaptionRequest;
import com.lx.pl.openai.dto.CaptionResponse;
import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.Response;
import retrofit2.http.*;

import java.util.Map;

@RetrofitClient(baseUrl = "${img2Ghibli.url}", fallbackFactory = Img2GhibliFallbackFactory.class)
@Intercept(handler = TokenInterceptor.class)
public interface Img2GhibliApi {

    @POST("/ghibli-generate-image")
    Call<Map<String, Object>> generateCaption(
            @Body Img2GhibliRequest request
    );
}
