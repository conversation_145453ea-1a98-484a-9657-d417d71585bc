package com.lx.pl.client;

import com.github.lianjiatech.retrofit.spring.boot.core.RetrofitClient;
import com.github.lianjiatech.retrofit.spring.boot.interceptor.Intercept;
import com.lx.pl.Handler.BackendFallbackFactory;
import com.lx.pl.dto.BackendAppleKeysResult;
import com.lx.pl.interceptor.TokenInterceptor;
import retrofit2.Response;
import retrofit2.http.GET;

@RetrofitClient(baseUrl = "${apple.iss.url}", fallbackFactory = BackendFallbackFactory.class)
@Intercept(handler = TokenInterceptor.class)
public interface AppleApi {

    @GET("/auth/keys")
    Response<BackendAppleKeysResult> getModelInformation();
}
