package com.lx.pl.exception;

import com.lx.pl.enums.AppleErrorCode;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 支付异常类
 */
public class PayAppleException extends BaseException {
    Logger log = LoggerFactory.getLogger("apple-pay-msg");

    private static final long serialVersionUID = 1L;

//    public PaymentException(String code, Object[] args) {
//        super("payment", code, args, null);
//    }
//
//    public PaymentException(String code, Object[] args, Throwable cause) {
//        super("payment", code, args, String.valueOf(cause));
//    }

    public PayAppleException(AppleErrorCode errorCode) {
        super("payment", errorCode.getCode(), null, errorCode.getMessage());
        log.error("apple支付异常代码: {}, 消息: {}, {}", errorCode.getCode(), errorCode.getMessage(), errorCode.getDescription());
    }

    public PayAppleException(AppleErrorCode errorCode, Throwable cause) {
        super("payment", errorCode.getCode(), null, String.valueOf(cause));
        log.error("apple支付异常代码: {}, 消息: {}, {}", errorCode.getCode(), errorCode.getMessage(), errorCode.getDescription());
    }

}
