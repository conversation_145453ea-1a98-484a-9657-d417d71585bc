package com.lx.pl.exception;

import com.lx.pl.enums.StripeErrorCode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 支付异常类
 */
public class PaymentException extends BaseException {

    Logger log = LoggerFactory.getLogger("stripe-pay-msg");
    private static final long serialVersionUID = 1L;

//    public PaymentException(String code, Object[] args) {
//        super("payment", code, args, null);
//    }
//
//    public PaymentException(String code, Object[] args, Throwable cause) {
//        super("payment", code, args, String.valueOf(cause));
//    }

    public PaymentException(StripeErrorCode errorCode) {
        super("payment", errorCode.getCode(), null, errorCode.getMessage());
        log.error("PaymentException: {} {} {}", errorCode.getCode(), errorCode.getMessage(), errorCode.getDescription());
    }

    public PaymentException(StripeErrorCode errorCode, Throwable cause) {
        super("payment", errorCode.getCode(), null, String.valueOf(cause));
        log.error("PaymentException: {} {} {}", errorCode.getCode(), errorCode.getMessage(), errorCode.getDescription(), cause);
    }

}
