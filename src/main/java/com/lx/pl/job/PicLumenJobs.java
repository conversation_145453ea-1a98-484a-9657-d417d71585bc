//package com.lx.pl.job;
//
//import com.lx.pl.service.*;
//import lombok.extern.slf4j.Slf4j;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Component;
//
//@Slf4j
//@Component
//public class PicLumenJobs {
//
//    private static final Logger logger = LoggerFactory.getLogger("schedule-task");
//
//    @Value("${statistics.kpi}")
//    Boolean statisticsKpi;
//
//    @Autowired
//    StatisticsService statisticsService;
//
//    @Autowired
//    ResettingUserMessageService messageService;
//
//    @Autowired
//    LoadBalanceService loadBalanceService;
//
//    @Autowired
//    EventLogService eventLogService;
//
//    /**
//     * kpi统计
//     */
////  @Scheduled(cron = "0 30 0 * * ?")
//    public void ScheduleTask() {
//        logger.info("每日定时任务");
//        try {
//            if (statisticsKpi) {
//                statisticsService.statisticsCreatePicture();
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//            logger.error("piclumen统计kpi数据报错");
//        }
//    }
//
//    /**
//     * 每天零时区零点（对应北京时间八点）重置用户当日生图数量
//     */
//    @Scheduled(cron = "0 0 8 * * ?")
//    public void resettingUserCreateImgNumTask() {
//        logger.info("重置用户当日生图数量任务");
//        try {
//            messageService.resettingUserCreateImgNumTask();
//        } catch (Exception e) {
//            e.printStackTrace();
//            logger.error("重置用户当日生图数量报错");
//        }
//    }
//
//    @Scheduled(cron = "*/3 * * * * ?")
//    public void dealProcessTask() {
//        try {
//            loadBalanceService.senQueueIndex();
//        } catch (Exception e) {
//            e.printStackTrace();
//            logger.error("任务队列发送排队信息报错");
//        }
//    }
//
//    // 定时任务 - 每月最后一天的晚上执行
//    //@Scheduled(cron = "0 0 23 L * ?")
//    public void createNextMonthTable() {
//        logger.info("开始生成下一个月的日志表");
//        try {
//            eventLogService.createNextMonthTable();
//        } catch (Exception e) {
//            e.printStackTrace();
//            logger.error("重置用户当日生图数量报错");
//        }
//    }
//
//
//}
