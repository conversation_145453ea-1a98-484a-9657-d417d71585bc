package com.lx.pl.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Properties;

/**
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "spring.mail")
public class MailProperties {

    private String host;

    private String username;

    private String password;

    private String from;

    private Integer port;

    private List<String> toUsers;

    // 额外的邮件属性配置
    @Value("${spring.mail.properties.mail.smtp.auth:true}")
    private Boolean auth; // 是否启用 SMTP 认证
    @Value("${spring.mail.properties.mail.smtp.starttls.enable:true}")
    private Boolean starttlsEnable; // 是否启用 STARTTLS 加密
    @Value("${spring.mail.properties.mail.smtp.starttls.required:true}")
    private Boolean starttlsRequired; // 是否强制使用 STARTTLS
    @Value("${spring.mail.properties.mail.smtp.ssl.protocols:TLSv1.2}")
    private String sslProtocols; // 指定 SSL/TLS 协议版本
    @Value("${spring.mail.properties.mail.smtp.ssl.trust:smtp.gmail.com}")
    private String sslTrust; // 可信的 SMTP 服务器域名

    @Bean
    public JavaMailSenderImpl mailSender() {
        JavaMailSenderImpl mailSender = new JavaMailSenderImpl();
        mailSender.setHost(host);
        mailSender.setPort(port);
        mailSender.setUsername(username);
        mailSender.setPassword(password);

        Properties props = mailSender.getJavaMailProperties();
        props.put("mail.transport.protocol", "smtp"); // 使用 SMTP 协议
        props.put("mail.smtp.auth", Boolean.TRUE.equals(auth) ? "true" : "false"); // 启用 SMTP 认证
        props.put("mail.smtp.starttls.enable", Boolean.TRUE.equals(starttlsEnable) ? "true" : "false"); // 启用 STARTTLS 加密
        props.put("mail.smtp.starttls.required", Boolean.TRUE.equals(starttlsRequired) ? "true" : "false"); // 强制使用 STARTTLS
        if (sslProtocols != null) {
            props.put("mail.smtp.ssl.protocols", sslProtocols); // 指定 SSL/TLS 协议版本
        }
        if (sslTrust != null) {
            props.put("mail.smtp.ssl.trust", sslTrust); // 信任指定的 SMTP 服务器
        }

        return mailSender;
    }

}
