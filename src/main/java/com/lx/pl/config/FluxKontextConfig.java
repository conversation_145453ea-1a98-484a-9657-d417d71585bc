package com.lx.pl.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Flux Kontext Pro API配置
 *
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "flux.kontext.api")
public class FluxKontextConfig {

    /**
     * API基础URL
     */
    private String baseUrl = "https://api.bfl.ai";

    /**
     * API密钥
     */
    private String apiKey;

    /**
     * 回调URL
     */
    private String callbackUrl;

    /**
     * 默认超时时间（秒）
     */
    private Integer defaultTimeout = 300;

    /**
     * 最大重试次数
     */
    private Integer maxRetries = 3;

    /**
     * 默认安全容忍度
     */
    private Integer defaultSafetyTolerance = 2;

    /**
     * 默认输出格式
     */
    private String defaultOutputFormat = "jpeg";

    /**
     * 是否启用Prompt检查
     */
    private Boolean promptCheckEnabled = true;

    /**
     * 轮询间隔（毫秒）
     */
    private Long pollingInterval = 2000L;

    /**
     * 最大轮询次数
     */
    private Integer maxPollingAttempts = 150; // 5分钟

    /**
     * 是否启用翻译
     */
    private Boolean translationEnabled = false;
}
