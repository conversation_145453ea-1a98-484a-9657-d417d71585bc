package com.lx.pl.config;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.cfg.CoercionAction;
import com.fasterxml.jackson.databind.cfg.CoercionInputShape;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import retrofit2.converter.jackson.JacksonConverterFactory;


@Configuration
public class RetrofitConfig {

    @Bean
    public JacksonConverterFactory jacksonConverterFactory() {
        ObjectMapper objectMapper = new ObjectMapper();

        // 配置 Jackson 允许将空字符串转换为空对象
        // 全局配置：允许将空字符串转换为空对象
        objectMapper.coercionConfigFor(Object.class)
                .setCoercion(CoercionInputShape.EmptyString, CoercionAction.AsEmpty);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        return JacksonConverterFactory.create(objectMapper);
    }
}
