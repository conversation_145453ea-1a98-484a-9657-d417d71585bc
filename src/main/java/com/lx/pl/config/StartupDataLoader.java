package com.lx.pl.config;

import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Component
public class StartupDataLoader {

    // 存储文件内容的内存变量
    private static Set<String> explicitSearchWords = new HashSet<>();

    /**
     * 在 Spring Boot 启动时加载 resources/explicit_search_words.txt 文件的内容
     */
    @PostConstruct
    public void loadExplicitSearchWords() {
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(getClass().getClassLoader().getResourceAsStream("explicit_search_words.txt"), StandardCharsets.UTF_8))) {
            
            String line;
            while ((line = reader.readLine()) != null) {
                // 将文件中的每一行存入列表中
                explicitSearchWords.add(line.trim());
            }
        } catch (IOException | NullPointerException e) {
            System.err.println("Failed to load explicit_search_words.txt: " + e.getMessage());
        }
    }

    /**
     * 提供静态方法来获取文件中的搜索词
     */
    public static Set<String> getExplicitSearchWords() {
        return explicitSearchWords; // 返回副本，避免外部修改
    }
}
