package com.lx.pl.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "push.notification-config")
@Getter
@Setter
public class PushNotifyProperties {

    private String apnsP8TeamId;

    private String apnsP8KeyId;

    private String apnsP8FilePath;

    private String apnsEnvironment;

    private String apnsAppBundledId;

    private String fcmMessagingScope;

    private String fcmIOSEndPoint;

    private String fcmIOSFilePath;


//    private String fcmAndroidEndPoint;
//
//    private String fcmAndroidFilePath;


}
