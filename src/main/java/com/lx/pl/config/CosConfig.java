package com.lx.pl.config;

import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.http.HttpProtocol;
import com.qcloud.cos.region.Region;
import com.qcloud.cos.transfer.TransferManager;
import com.tencent.cloud.cos.util.Jackson;
import lombok.Getter;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PreDestroy;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 腾讯云初始化配置
 */
@Configuration
public class CosConfig {

    // 存储桶上的id
    @Value("${tencent.appId}")
    public String appId;

    @Value("${tencent.secretId}")
    public String secretId;

    @Value("${tencent.secretKey}")
    public String secretKey;

    @Value("${tencent.region}")
    public String region;

    @Value("${tencent.bucket}")
    @Getter
    public String bucket;

    @Value("${tencent.bucketOld:piclumen-1324066212}")
    @Getter
    public String bucketOld;

    @Getter
    @Value("${tencent.cos.domain}")
    private String cosDomain;

    /**
     * 全球加速域名
     */
    @Getter
    @Value("${tencent.cos-accelerate.domain}")
    private String cosAccelerateDomain;
    /**
     * 全球加速
     */
    @Value("${tencent.accelerate.cosSuffix}")
    public String cosSuffix;

    /**
     * 内网全球加速
     */
    @Value("${tencent.internal-accelerate.cosSuffix}")
    public String intranetCosSuffix;

    /**
     * 无加速
     */
    @Value("${tencent.base.suffix}")
    private String baseSuffix;
    /**
     * 默认10L * 1024 * 1024
     */
    @Value("${tencent.content.length:10485760}")
    private Long contentLength;
    @Value("${tencent.token.duration:300}")
    @Getter
    private String cosTokenDuration;

    /**
     * 是否启用内网加速上传
     */
    @Value("${tencent.enable-internal-accelerate:false}")
    @Getter
    private Boolean enableInternalAccelerateUpload;

    /**
     * 90% webp
     */
    @Value("${tencent.style.thumbnail-rule:imageMogr2/format/webp/interlace/0/quality/75}")
    @Getter
    private String thumbnailRule;

    /**
     * 75% webp
     */
    @Value("${tencent.style.high-thumbnail-rule:imageMogr2/format/webp/interlace/0/quality/90}")
    @Getter
    private String highThumbnailRule;

    /**
     * webp转png
     */
    @Value("${tencent.webpToPngRule:imageMogr2/format/png/interlace/0/quality/100}")
    @Getter
    private String webpToPngRule;

    @Value("${tencent.miniRule:imageMogr2/thumbnail/x280}")
    @Getter
    private String miniRule;


    // 腾讯云全球加速
    public COSClient cosAccelerateCosClientClient;
    // 腾讯云内网全球加速
    public COSClient nwAccelerateCosClient;

    @Bean(name = "transferManager", destroyMethod = "shutdownNow")
    public TransferManager accelerateTransferManager() {
        return createTransferManager(baseSuffix);
    }

    private TransferManager createTransferManager(String suffix) {
        // 1 初始化用户身份信息(secretId, secretKey)
        COSCredentials cred = new BasicCOSCredentials(secretId, secretKey);
        // 2 设置bucket的区域, COS地域的简称请参照 https://www.qcloud.com/document/product/436/6224
        ClientConfig clientConfig = new ClientConfig(new Region(region));
        clientConfig.setHttpProtocol(HttpProtocol.https);
        clientConfig.setShutdownTimeout(10 * 60 * 1000);
        clientConfig.setEndPointSuffix(suffix);
        // 3 生成cos客户端
        COSClient cosclient = new COSClient(cred, clientConfig);
        ExecutorService threadPool = Executors.newFixedThreadPool(32);
        // 传入一个threadpool, 若不传入线程池, 默认TransferManager中会生成一个单线程的线程池。
        return new TransferManager(cosclient, threadPool);
    }

    /**
     * 无加速
     */
    public COSClient baseCosClient;

    @Bean
    public COSClient cosClient() {
        cosAccelerateCosClientClient = getCosAccelerateCosClientClient();
        return this.cosAccelerateCosClientClient;
    }

    @Bean("baseCosClient")
    public COSClient baseCosClient() {
        baseCosClient = getBaseCosClient();
        return this.baseCosClient;
    }

    @Bean
    public COSClient nwCosClient() {
        nwAccelerateCosClient = getIntranetCosClient();
        return this.nwAccelerateCosClient;
    }

    @PreDestroy
    public void shutdownCOSClient() {
        if (cosAccelerateCosClientClient != null) {
            cosAccelerateCosClientClient.shutdown();
        }
        if (nwAccelerateCosClient != null) {
            nwAccelerateCosClient.shutdown();
        }
        if (baseCosClient != null) {
            baseCosClient.shutdown();
        }
    }

    /**
     * 全球加速域名
     *
     * @return
     */
    public COSClient getCosAccelerateCosClientClient() {
        return getCosClientResult(cosSuffix);
    }

    public COSClient getBaseCosClient() {
        return getCosClientResult(baseSuffix);
    }

    /**
     * 内网全球加速域名
     *
     * @return
     */
    public COSClient getIntranetCosClient() {
        return getCosClientResult(intranetCosSuffix);
    }


    private COSClient getCosClientResult(String suffix) {
        COSCredentials cred = new BasicCOSCredentials(this.secretId, this.secretKey);
        ClientConfig clientConfig = new ClientConfig(new Region(this.region));
        // 推荐使用 https 协议
        clientConfig.setHttpProtocol(HttpProtocol.https);
        // 3 设置全球加速域名后缀
        clientConfig.setEndPointSuffix(suffix);
        // 4 生成 cos 客户端
        COSClient cosClient = new COSClient(cred, clientConfig);
        return cosClient;
    }

    public TreeMap<String, Object> buildBaseConfig(String... fileName) {

        Map<String, Object> statement = new HashMap();
        String[] allowActions = new String[]{
                // 简单上传
                "name/cos:PutObject", "name/cos:PostObject",
                // 分片上传
                "name/cos:InitiateMultipartUpload", "name/cos:ListMultipartUploads", "name/cos:ListParts", "name/cos:UploadPart", "name/cos:CompleteMultipartUpload"/*, "name/cos:CiPutObject"*/};
        statement.put("action", allowActions);
        statement.put("effect", "allow");
        List<String> resources = builResoursList(fileName);
        statement.put("resource", resources);

        Map<String, Object> condition = new HashMap<>();
        condition.put("numeric_less_than_equal", new HashMap<String, Long>() {{
            put("cos:content-length", contentLength);
        }});
        statement.put("condition", condition);

        Map<String, Object> policy = new HashMap();
        policy.put("version", "2.0");
        policy.put("statement", Arrays.asList(statement));

        // 构建config
        TreeMap<String, Object> config = new TreeMap<String, Object>();
        config.put("secretId", secretId);
        config.put("secretKey", secretKey);
        //            config.put("proxy",proxy);
        config.put("durationSeconds", Integer.valueOf(cosTokenDuration));
        config.put("bucket", bucket);
        config.put("region", region);
        config.put("policy", Jackson.toJsonPrettyString(policy));
        return config;
    }

    @NotNull
    private List<String> builResoursList(String... fileName) {
        String preFix = "qcs::cos:" + region + ":uid/" + appId + ':' + bucket + '/';
        List<String> resources = new ArrayList<>(fileName.length);
        for (String key : fileName) {
            String resource = preFix + key;
            resources.add(resource);
        }
        return resources;
    }


}
