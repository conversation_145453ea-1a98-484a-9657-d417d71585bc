//package com.lx.pl.config;
//
//import io.swagger.v3.oas.models.Components;
//import io.swagger.v3.oas.models.OpenAPI;
//import io.swagger.v3.oas.models.info.Contact;
//import io.swagger.v3.oas.models.info.Info;
//import io.swagger.v3.oas.models.security.SecurityRequirement;
//import io.swagger.v3.oas.models.security.SecurityScheme;
//import org.springdoc.core.GroupedOpenApi;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
//@Configuration
//public class SpringDocSwaggerConfig {
//    private static final String[] BASE_PACKAGES = {
//            "com.lx.pl.controller",
//            "com.lx.pl.pay.controller" // 新增的包路径
//    };
//    private static final String headerName = "Authorization";//请求头名称
//    private static final String platformHeaderName = "Platform"; // 新增的请求头名称
//
//    @Bean
//    public GroupedOpenApi usersGroup() {
//        return GroupedOpenApi.builder()
//                .group("PicLumen")
//                .addOperationCustomizer((operation, handlerMethod) -> {
//                    operation.addSecurityItem(new SecurityRequirement().addList(headerName));
//                    operation.addSecurityItem(new SecurityRequirement().addList(platformHeaderName));
//                    return operation;
//                })
//                .packagesToScan(BASE_PACKAGES)
//                .build();
//    }
//
//    @Bean
//    public OpenAPI customOpenAPI() {
//        Components components = new Components();
//        //添加右上角的统一安全认证
//        components.addSecuritySchemes(headerName,
//                new SecurityScheme()
//                        .type(SecurityScheme.Type.APIKEY)
//                        .scheme("basic")
//                        .name(headerName)
//                        .in(SecurityScheme.In.HEADER)
//                        .description("请求头")
//        );
//
//        // 添加新的Platform请求头
//        components.addSecuritySchemes(platformHeaderName,
//                new SecurityScheme()
//                        .type(SecurityScheme.Type.APIKEY)
//                        .scheme("basic")
//                        .name(platformHeaderName)
//                        .in(SecurityScheme.In.HEADER)
//                        .description("平台请求头")
//        );
//
//        return new OpenAPI()
//                .components(components)
//                .info(apiInfo());
//    }
//
//    private Info apiInfo() {
//        Contact contact = new Contact();
//        contact.setEmail("<EMAIL>");
//        contact.setName("xiang");
//        contact.setUrl("https://xiang.com");
//        return new Info()
//                .title("PicLumen-swagger文档")
//                .version("1.0")
//                .contact(contact)
//                .description("");
//    }
//}
