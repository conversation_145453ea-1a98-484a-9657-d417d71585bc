package com.lx.pl.config.pay;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * stripe 配置
 *
 * <AUTHOR>
 */
@Configuration
@Getter
@Setter
public class StripConfig {
    @Value("${stripe.client.secretKey:sk_test_51QZkWTQECfIT1EGJLbJBn0Azcc4u40CMC6b40Jkpaz3CTLbP0KHTfkc1On5Mk9IuddSbDOnNkDX33ew8mh5ZlnE800Buqfm38A}")
    private String secretKey;

    @Value("${stripe.client.pubKey:pk_test_51QZkWTQECfIT1EGJ2JEqMfk3E0iir3q0rZEo67802bAUIQbkyX5vV58vSCljru4YUP3mvKBLmeVsONe0FgalMxIS00r1Qg57O9}")
    private String pubKey;

    @Value("${stripe.client.webhookSecret_customer:whsec_d7c60b8ae78ce1786a2377f94b30178ef6b250f2d833bb0d2c07391e02e9f0fa}")
    private String webhookSecretCustomer;
    @Value("${stripe.client.webhookSecret_payment:whsec_a1b99ce4ade0bb5f8062fd259e615aca44bd51ceee8de93bdae036d6fe116aa1}")
    private String webhookSecretPayment;
    @Value("${stripe.client.webhookSecret_schedule:whsec_a1b99ce4ade0bb5f8062fd259e615aca44bd51ceee8de93bdae036d6fe116aa1}")
    private String webhookSecretSchedule;
}
