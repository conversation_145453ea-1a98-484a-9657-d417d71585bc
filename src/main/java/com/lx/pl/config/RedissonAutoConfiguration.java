package com.lx.pl.config;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.codec.JsonJacksonCodec;
import org.redisson.codec.SmileJacksonCodec;
import org.redisson.config.Config;
import org.redisson.config.SingleServerConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Configuration
@ConditionalOnClass(Config.class)
@Slf4j
public class RedissonAutoConfiguration {

    // @Value("${spring.redis.sentinel.nodes:}")
    // private String[] sentinelAddresses;
    //
    // @Value("${spring.redis.sentinel.master:}")
    // private String masterName;

    @Resource
    private RedisProperties redisProperties;

    // /**
    //  * 哨兵模式自动装配
    //  *
    //  * @return
    //  */
    // @Bean
    // @ConditionalOnProperty(name = "spring.redis.sentinel.master")
    // RedissonClient redissonSentinel() {
    //     if (log.isDebugEnabled()) {
    //         log.debug("initialize redisson in sentinel mode");
    //         log.debug("sentinel nodes {}, length {}, master {}", sentinelAddresses, sentinelAddresses.length, masterName);
    //     }
    //     Config config = new Config();
    //     if (ArrayUtil.isEmpty(sentinelAddresses)) {
    //         log.error("spring.redis.sentinel.nodes must be configured");
    //     }
    //     String redisUri = Arrays.stream(sentinelAddresses).map(addr -> ("redis://" + addr)).collect(Collectors.joining(","));
    //     if(log.isDebugEnabled()){
    //         log.debug("redis uri addresses is {}", redisUri);
    //     }
    //     SentinelServersConfig serverConfig = config.useSentinelServers().addSentinelAddress(redisUri.split(","))
    //             .setMasterName(masterName);
    //
    //     if (StrUtil.isNotBlank(password)) {
    //         serverConfig.setPassword(password);
    //     }
    //     return Redisson.create(config);
    // }

    /**
     * 单机模式自动装配
     *
     * @return
     */
    @Bean
    @ConditionalOnProperty(name = "spring.redis.host")
    RedissonClient redissonSingle() {
        if (log.isDebugEnabled()) {
            log.debug("initialize redisson in single server mode");
        }
        Config config = new Config();
        SingleServerConfig serverConfig = config.useSingleServer()
                .setAddress("redis://" + redisProperties.getHost() + ":" + redisProperties.getPort());
        if (StrUtil.isNotBlank(redisProperties.getPassword())) {
            serverConfig.setPassword(redisProperties.getPassword());
        }
        serverConfig.setDatabase(redisProperties.getDatabase());
        RedissonClient redissonClient = Redisson.create(config);
        return redissonClient;
    }
}
