package com.lx.pl.config;


import com.turo.pushy.apns.ApnsClient;
import com.turo.pushy.apns.ApnsClientBuilder;
import com.turo.pushy.apns.auth.ApnsSigningKey;
import io.netty.channel.nio.NioEventLoopGroup;
import org.apache.commons.io.IOUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

@Configuration
public class ApnsClientInitializer {


    @Resource
    private PushNotifyProperties pushNotifyProperties;


    @Bean()
    public ApnsClient apnsClient() throws IOException, NoSuchAlgorithmException, InvalidKeyException {
        // 使用InputStream来读取资源文件
        org.springframework.core.io.Resource resource = new ClassPathResource(pushNotifyProperties.getApnsP8FilePath());
        File tempFile = File.createTempFile("apns-p8-", ".p8");
        tempFile.deleteOnExit(); // 程序退出时删除临时文件

        // 将资源文件内容复制到临时文件
        try (InputStream inputStream = resource.getInputStream();
             FileOutputStream outputStream = new FileOutputStream(tempFile)) {
            IOUtils.copy(inputStream, outputStream);
        }

        // 使用临时文件
        File p8File = tempFile;


        // 创建事件循环组，设置并发线程数
        NioEventLoopGroup eventLoopGroup = new NioEventLoopGroup(4);

        // 创建 APNs 客户端
        // 或者使用 PRODUCTION_APNS_HOST
        return new ApnsClientBuilder()
                .setApnsServer(pushNotifyProperties.getApnsEnvironment()) // 或者使用 PRODUCTION_APNS_HOST
                .setEventLoopGroup(eventLoopGroup)
                .setConcurrentConnections(4)
                .setSigningKey(ApnsSigningKey.loadFromPkcs8File(p8File, pushNotifyProperties.getApnsP8TeamId(), pushNotifyProperties.getApnsP8KeyId()))
                .build();
    }





}
