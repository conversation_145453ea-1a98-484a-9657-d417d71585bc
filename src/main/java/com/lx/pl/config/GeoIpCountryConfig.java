package com.lx.pl.config;

import com.maxmind.geoip2.DatabaseReader;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;

import java.io.IOException;

@Configuration
public class GeoIpCountryConfig {

    private final ResourceLoader resourceLoaderCountry;

    public GeoIpCountryConfig(ResourceLoader resourceLoader) {
        this.resourceLoaderCountry = resourceLoader;
    }

    @Bean
    public DatabaseReader databaseReaderCountry() throws IOException {
        Resource resource = resourceLoaderCountry.getResource("classpath:GeoLite2-Country.mmdb");
        return new DatabaseReader.Builder(resource.getInputStream()).build();
    }
}