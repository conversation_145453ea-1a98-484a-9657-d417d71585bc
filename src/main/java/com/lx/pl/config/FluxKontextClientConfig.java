package com.lx.pl.config;

import com.github.lianjiatech.retrofit.spring.boot.annotation.RetrofitClient;
import com.lx.pl.client.FluxKontextApiClient;
import org.springframework.context.annotation.Configuration;

/**
 * Flux Kontext API客户端配置
 *
 * <AUTHOR>
 */
@Configuration
public class FluxKontextClientConfig {

    /**
     * 配置Flux Kontext API客户端
     * 使用@RetrofitClient注解自动配置
     */
    @RetrofitClient(baseUrl = "${flux.kontext.api.base-url}")
    public interface FluxKontextClient extends FluxKontextApiClient {
        // 继承FluxKontextApiClient接口，由Retrofit自动实现
    }
}
