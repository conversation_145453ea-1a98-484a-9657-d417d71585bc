package com.lx.pl.config;

import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Component
public class BadWordsLoader {

    // 存储文件内容的内存变量
    private static Set<String> badWordsSet = new HashSet<>();
    private static Set<String> pornBadWordsSet = new HashSet<>();

    /**
     * 在 Spring Boot 启动时加载 bad_words.txt 和 porn_bad_words.txt 文件的内容
     */
    @PostConstruct
    public void loadBadWords() {
        // 加载 bad_words.txt
        loadWordsFromFile("bad_words.txt", badWordsSet);

        // 加载 porn_bad_words.txt
        loadWordsFromFile("adult_bad_words.txt", pornBadWordsSet);
    }

    /**
     * 通用方法：从指定文件中加载敏感词到指定的集合中
     */
    private void loadWordsFromFile(String fileName, Set<String> wordSet) {
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(getClass().getClassLoader().getResourceAsStream(fileName), StandardCharsets.UTF_8))) {

            String line;
            while ((line = reader.readLine()) != null) {
                String trimmedLine = line.trim();
                if (!trimmedLine.isEmpty()) {
                    wordSet.add(trimmedLine);
                }
            }
        } catch (IOException | NullPointerException e) {
            System.err.println("Failed to load " + fileName + ": " + e.getMessage());
        }
    }


    /**
     * 提供静态方法来获取文件中的搜索词
     */
    public static Set<String> getBadWordsSet() {
        return badWordsSet; // 返回副本，避免外部修改
    }

    public static Set<String> getPornBadWordsSet() {
        return pornBadWordsSet;
    }

}
