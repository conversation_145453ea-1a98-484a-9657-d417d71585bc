package com.lx.pl.pay.stripe.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lx.pl.db.mysql.gen.entity.MyBaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Stripe 支付意图实体类
 */
@Data
@TableName("stripe_payment_intent")
public class StripePaymentIntent extends MyBaseEntity {

    /**
     * 主键 ID
     */
    @Schema(description = "主键 ID")
    @TableId(value = "id")
    private Long id;

    /**
     * 用户 ID
     */
    @Schema(description = "用户 ID")
    private Long userId;

    /**
     * 用户账号
     */
    @Schema(description = "用户账号")
    private String loginName;

    /**
     * 订阅 ID
     */
    @Schema(description = "订阅 ID")
    private String subscriptionId;

    /**
     * 支付意图 ID
     */
    @Schema(description = "支付意图 ID")
    private String paymentIntentId;

    /**
     * Stripe 价格 ID
     */
    @Schema(description = "Stripe 发票 ID")
    private String invoiceId;

    /**
     * 金额
     */
    @Schema(description = "金额")
    private Long amount;

    /**
     * 实收金额
     */
    @Schema(description = "实收金额")
    private Long amountReceived;

    /**
     * 币种
     */
    @Schema(description = "币种")
    private String currency;

    /**
     * Stripe 客户 ID
     */
    @Schema(description = "Stripe 客户 ID")
    private String customerId;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private String status;

    private Long canceledAt;

    private String cancellationReason;

    /**
     * 错误类型
     */
    @Schema(description = "错误类型")
    private String errorType;

    /**
     * 错误代码
     */
    @Schema(description = "错误代码")
    private String errorCode;

    /**
     * 拒绝代码
     */
    @Schema(description = "拒绝代码")
    private String declineCode;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息")
    private String errorMessage;

    @Schema(description = "网络建议代码")
    private String networkAdviceCode;
}
