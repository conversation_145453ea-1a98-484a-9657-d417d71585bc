package com.lx.pl.pay.stripe.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lx.pl.pay.stripe.domain.StripePaymentIntent;
import com.lx.pl.pay.stripe.domain.StripeUserCustomer;
import com.lx.pl.pay.stripe.mapper.StripePaymentIntentMapper;
import com.lx.pl.pay.stripe.service.StripePaymentIntentService;
import com.lx.pl.pay.stripe.service.StripeUserCustomerService;
import com.stripe.model.PaymentIntent;
import com.stripe.model.StripeError;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * Stripe 支付意图服务实现类
 */
@Service
public class StripePaymentIntentServiceImpl extends ServiceImpl<StripePaymentIntentMapper, StripePaymentIntent> implements StripePaymentIntentService {
    @Resource
    protected StripeUserCustomerService stripeUserCustomerService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void savePaymentIntent(PaymentIntent event) {
        StripeUserCustomer userCustomer = stripeUserCustomerService.lambdaQuery()
                .eq(StripeUserCustomer::getCustomerId, event.getCustomer()).one();
        StripePaymentIntent stripePaymentIntent = buildPaymentIntent(event, userCustomer);
        this.save(stripePaymentIntent);
    }

    // 基础的 CRUD 操作由 ServiceImpl 提供
    private StripePaymentIntent buildPaymentIntent(PaymentIntent event, StripeUserCustomer userCustomer) {
        StripePaymentIntent stripePaymentIntent = new StripePaymentIntent();
        stripePaymentIntent.setCreateTime(LocalDateTime.now());
        stripePaymentIntent.setPaymentIntentId(event.getId());
        stripePaymentIntent.setAmount(event.getAmount());
        stripePaymentIntent.setAmountReceived(event.getAmountReceived());
        stripePaymentIntent.setCurrency(event.getCurrency());
        stripePaymentIntent.setCustomerId(event.getCustomer());
        stripePaymentIntent.setInvoiceId(event.getInvoice());
        stripePaymentIntent.setStatus(event.getStatus());
        stripePaymentIntent.setCreateTime(LocalDateTime.now());
        if (userCustomer != null) {
            stripePaymentIntent.setUserId(userCustomer.getUserId());
            stripePaymentIntent.setLoginName(userCustomer.getLoginName());
        }
        StripeError lastPaymentError = event.getLastPaymentError();
        if (lastPaymentError != null) {
            stripePaymentIntent.setErrorCode(lastPaymentError.getCode());
            stripePaymentIntent.setErrorMessage(lastPaymentError.getMessage());
            stripePaymentIntent.setErrorType(lastPaymentError.getType());
            stripePaymentIntent.setDeclineCode(lastPaymentError.getDeclineCode());
            stripePaymentIntent.setNetworkAdviceCode(lastPaymentError.getNetworkAdviceCode());
        }

        return stripePaymentIntent;
    }

}
