package com.lx.pl.pay.stripe.service;


import com.stripe.Stripe;
import com.stripe.model.Event;
import com.stripe.net.Webhook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 */
@Component
public class StripeService {

    Logger log = LoggerFactory.getLogger("stripe-pay-msg");

    // 验证webhook 返回event
    public Event constructEvent(HttpServletRequest request, String webhookSecret, String secretKey) throws IOException {
        Stripe.apiKey = secretKey;
        String payload = new String(request.getInputStream().readAllBytes(), StandardCharsets.UTF_8);
        String signature = request.getHeader("Stripe-Signature");
        log.info("Stripe-Signature:{}", signature);
        Event event = null;
        if (signature == null) {
            log.warn("webook 签名为空");
            throw new RuntimeException("webook 签名为空");
        }

        try {
            event = Webhook.constructEvent(
                    payload, signature, webhookSecret
            );
        } catch (Exception e) {
            log.warn("Invalid signature: ", e);
            throw new RuntimeException("Invalid signature");
        }
        return event;
    }
}
