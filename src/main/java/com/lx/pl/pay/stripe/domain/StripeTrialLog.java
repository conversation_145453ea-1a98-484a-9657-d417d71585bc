package com.lx.pl.pay.stripe.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lx.pl.db.mysql.gen.entity.MyBaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


@Data
@TableName("stripe_trial_log")
public class StripeTrialLog extends MyBaseEntity {
    /**
     * 主键 ID
     */
    @Schema(description = "主键 ID")
    @TableId(value = "id")
    private Long id;


    @Schema(description = "用户ID")
    private Long userId; // 用户ID

    @Schema(description = "登录名")
    private String loginName; // 登录名

    @Schema(description = "订阅ID")
    private String subscriptionId; // 订阅ID

    @Schema(description = "价格ID")
    private String priceId; // 价格ID

    @Schema(description = "试用天数")
    private Integer trialDay; // 试用天数

    @Schema(description = "试用开始时间")
    private Long trialStart; // 试用开始时间

    @Schema(description = "试用结束时间")
    private Long trialEnd; // 试用结束时间

    @Schema(description = "新计划ID")
    private Integer trialLumen; // 新计划ID
}
