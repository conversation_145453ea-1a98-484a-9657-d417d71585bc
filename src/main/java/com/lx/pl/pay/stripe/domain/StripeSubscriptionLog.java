package com.lx.pl.pay.stripe.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lx.pl.db.mysql.gen.entity.MyBaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "Stripe 订阅记录日志")
@TableName("stripe_subscription_log")
public class StripeSubscriptionLog extends MyBaseEntity {

    @Schema(description = "主键 ID")
    @TableId(value = "id")
    private Long id;

    @Schema(description = "用户 ID")
    private Long userId;

    @Schema(description = "用户账号")
    private String loginName;

    @Schema(description = "订阅 ID")
    private String subscriptionId;

    @Schema(description = "订阅项 ID")
    private String subscriptionItemId;

    @Schema(description = "预定阅 ID")
    private String subscriptionScheduleId;

    @Schema(description = "客户 ID")
    private String customerId;

    @Schema(description = "Stripe price ID")
    private String priceId;

    @Schema(description = "价格类型")
    private String priceType;

    @Schema(description = "Stripe product ID")
    private String productId;

    @Schema(description = "Stripe product 名称")
    private String productName;

    @Schema(description = "订阅状态")
    private String subStatus;

    @Schema(description = "订阅周期 (month, year)")
    private String subInterval;

    @Schema(description = "订阅开始时间 (Unix 时间戳)")
    private Long startDate;

    @Schema(description = "订阅周期结束时间 (Unix 时间戳)")
    private Long currentPeriodEnd;

    @Schema(description = "订阅周期开始时间 (Unix 时间戳)")
    private Long currentPeriodStart;

    @Schema(description = "逻辑订阅周期结束时间 (Unix 时间戳)")
    private Long logicPeriodEnd;

    @Schema(description = "逻辑订阅周期开始时间 (Unix 时间戳)")
    private Long logicPeriodStart;

    private Long trialStart;

    private Long trialEnd;

    @Schema(description = "取消时间 (Unix 时间戳)")
    private Long cancelledAt;

    @Schema(description = "取消原因")
    private String cancelReason;

}
