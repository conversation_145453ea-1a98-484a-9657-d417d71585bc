package com.lx.pl.pay.stripe.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lx.pl.pay.stripe.domain.StripeScheduleRecord;
import com.lx.pl.pay.stripe.domain.StripeUserCustomer;
import com.stripe.model.SubscriptionSchedule;

/**
 * Stripe 订阅计划记录服务接口
 */
public interface StripeScheduleRecordService extends IService<StripeScheduleRecord> {
    StripeScheduleRecord saveScheduleSubscription(SubscriptionSchedule event, StripeUserCustomer userCustomer);

    // 可以添加一些自定义方法，例如：
    // StripeScheduleRecord findBySubscriptionId(String subscriptionId);
}
