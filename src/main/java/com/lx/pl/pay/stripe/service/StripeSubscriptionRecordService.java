package com.lx.pl.pay.stripe.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lx.pl.pay.stripe.domain.StripeSubscriptionRecord;
import com.stripe.model.Subscription;

/**
 * Stripe 订阅记录服务接口
 */
public interface StripeSubscriptionRecordService extends IService<StripeSubscriptionRecord> {
    StripeSubscriptionRecord saveSubscription(Subscription event);

    void fixVip();
}
