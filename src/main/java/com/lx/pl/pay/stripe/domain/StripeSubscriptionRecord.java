package com.lx.pl.pay.stripe.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lx.pl.db.mysql.gen.entity.MyBaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * Stripe 订阅记录实体类
 */
@Data
@TableName("stripe_subscription_record")
public class StripeSubscriptionRecord extends MyBaseEntity {

    /**
     * 主键 ID
     */
    @Schema(description = "主键 ID")
    @TableId(value = "id")
    private Long id;

    /**
     * 用户 ID
     */
    @Schema(description = "用户 ID")
    private Long userId;

    /**
     * 用户账号
     */
    @Schema(description = "用户账号")
    private String loginName;

    /**
     * 订阅 ID
     */
    @Schema(description = "订阅 ID")
    private String subscriptionId;

//    /**
//     * 预定阅 ID
//     */
//    @Schema(description = "预定阅 ID")
//    private String subscriptionScheduleId;

    /**
     * Stripe 客户 ID
     */
    @Schema(description = "Stripe 客户 ID")
    private String customerId;

//    @Schema(description = "Stripe 账单发票 ID")
//    private String invoiceId;

    /**
     * Stripe 价格 ID
     */
    @Schema(description = "Stripe 价格 ID")
    private String priceId;

    private String priceType;

    /**
     * Stripe 产品 ID
     */
    @Schema(description = "Stripe 产品 ID")
    private String productId;


    private String subscriptionItemId;

    /**
     * 订阅状态
     */
    @Schema(description = "订阅状态")
    private String subStatus;

    /**
     * 订阅周期（月、年）
     */
    @Schema(description = "订阅周期（月、年）")
    private String subInterval;


    /**
     * 订阅开始时间
     */
    @Schema(description = "订阅开始时间")
    private Long startDate;

    /**
     * 当前周期结束时间
     */
    @Schema(description = "当前周期结束时间")
    private Long currentPeriodEnd;

    /**
     * 当前周期开始时间
     */
    @Schema(description = "当前周期开始时间")
    private Long currentPeriodStart;

    /**
     * 逻辑周期结束时间
     */
    @Schema(description = "逻辑周期结束时间")
    private Long logicPeriodEnd;

    /**
     * 逻辑周期开始时间
     */
    @Schema(description = "逻辑周期开始时间")
    private Long logicPeriodStart;

    /**
     * 试用开始时间
     */
    private Long trialStart;

    /**
     * 试用结束时间
     */
    private Long trialEnd;

    /**
     * 取消时间
     */
    @Schema(description = "取消时间")
    private Long cancelledAt;

//    @Schema(description = "是否已经保存了logic record")
//    private Boolean saveLogic;


    @Schema(description = "取消原因")
    private String cancelReason;
}
