package com.lx.pl.pay.stripe.service.strategy.invoice;

import com.stripe.Stripe;
import com.stripe.exception.StripeException;
import com.stripe.model.*;
import com.stripe.param.*;

public class StripeService {

    public static void main(String[] args) {
        Stripe.apiKey = "sk_test_51QZkWTQECfIT1EGJLbJBn0Azcc4u40CMC6b40Jkpaz3CTLbP0KHTfkc1On5Mk9IuddSbDOnNkDX33ew8mh5ZlnE800Buqfm38A"; // 你的 Stripe 私钥
        try {
            StripeService service = new StripeService();
//            service.cancelPaymentIntent();


////            subscription.cancel();
////            update();
            service.voidInvoice();

            // 获取发票
//            Invoice invoice = Invoice.retrieve("in_1Qvx6XQECfIT1EGJax6cwqhi");
//            invoice.update(InvoiceUpdateParams.builder()
//                    .setDueDate(1743057167L)
//                    .build());
//            // 发送发票
//            invoice.sendInvoice();

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void cancelPaymentIntent() throws StripeException {

        PaymentIntent paymentIntent = PaymentIntent.retrieve("pi_3QvvXlQECfIT1EGJ1QIqh5RK");
        paymentIntent.cancel(); // 取消支付
        System.out.println("已取消 PaymentIntent: " + "pi_3QvvXlQECfIT1EGJ1QIqh5RK");
    }

    private static void update() throws StripeException {
        Subscription subscription = Subscription.retrieve("sub_1QvvCMQECfIT1EGJuUqnS1Hn");
        SubscriptionUpdateParams params = SubscriptionUpdateParams.builder()
                .addItem(
                        SubscriptionUpdateParams.Item.builder()
                                .setId("si_RpaNuqEZNMgUef")
                                .setPrice("price_1QZkZJQECfIT1EGJoOmzgQxW")  // 切换回旧的价格 ID
                                .build()
                )
                .build();

        Subscription update = subscription.update(params);
    }

    private static void voidInvoice() throws StripeException {
        Invoice resource = Invoice.retrieve("in_1QvxRXQECfIT1EGJM1ue51Wu");
        InvoiceVoidInvoiceParams params = InvoiceVoidInvoiceParams.builder()
                .build();
        Invoice invoice = resource.voidInvoice(params);
//        resource.markUncollectible();


        // 需要通过 Customer 对象来创建交易
//        Customer customer = Customer.retrieve(resource.getCustomer());
//        // 创建余额交易
//        CustomerBalanceTransactionCollectionCreateParams params2 =
//                CustomerBalanceTransactionCollectionCreateParams.builder()
//                        .setAmount(-customer.getBalance()) // 负数表示减少余额（退款）
//                        .setCurrency("usd")
//                        .setDescription("回滚订阅退款")
//                        .build();
//        CustomerBalanceTransaction transaction = customer.balanceTransactions().create(params2);

        System.out.println("已从 Stripe 余额退款: " + resource.getAmountRemaining());
    }


    public void cancelPayment(String paymentIntentId) throws Exception {
        PaymentIntent paymentIntent = PaymentIntent.retrieve(paymentIntentId);
        paymentIntent.cancel();  // 取消支付
        System.out.println("已取消 PaymentIntent: " + paymentIntentId);
    }
}