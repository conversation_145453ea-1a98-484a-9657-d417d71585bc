package com.lx.pl.pay.stripe.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lx.pl.db.mysql.gen.entity.StripeProduct;
import com.lx.pl.pay.stripe.domain.StripeScheduleRecord;
import com.lx.pl.pay.stripe.domain.StripeUserCustomer;
import com.lx.pl.pay.stripe.mapper.StripeScheduleRecordMapper;
import com.lx.pl.pay.stripe.service.StripeScheduleRecordService;
import com.lx.pl.pay.stripe.service.StripeUserCustomerService;
import com.lx.pl.service.StripeProductService;
import com.stripe.model.SubscriptionSchedule;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Stripe 订阅计划记录服务实现类
 */
@Service
public class StripeScheduleRecordServiceImpl extends ServiceImpl<StripeScheduleRecordMapper, StripeScheduleRecord> implements StripeScheduleRecordService {

    @Resource
    protected StripeUserCustomerService stripeUserCustomerService;
    @Resource
    private StripeProductService stripeProductService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public StripeScheduleRecord saveScheduleSubscription(SubscriptionSchedule event, StripeUserCustomer userCustomer) {

        StripeScheduleRecord stripeScheduleRecord = new StripeScheduleRecord();
        stripeScheduleRecord.setCreateTime(LocalDateTime.now());
        if (userCustomer != null) {
            stripeScheduleRecord.setUserId(userCustomer.getUserId());
            stripeScheduleRecord.setLoginName(userCustomer.getLoginName());
        }
        stripeScheduleRecord.setCustomerId(event.getCustomer());
        stripeScheduleRecord.setSubscriptionScheduleId(event.getId());
        stripeScheduleRecord.setScheduleStatus(event.getStatus());
        List<SubscriptionSchedule.Phase> phases = event.getPhases();
        if (phases != null && !phases.isEmpty()) {
            SubscriptionSchedule.Phase phase = phases.get(0);
            stripeScheduleRecord.setStartDate(phase.getStartDate());
            stripeScheduleRecord.setEndDate(phase.getEndDate());
            List<SubscriptionSchedule.Phase.Item> items = phase.getItems();
            if (items != null && !items.isEmpty()) {
                stripeScheduleRecord.setPriceId(items.get(0).getPrice());
                StripeProduct one = stripeProductService.lambdaQuery()
                        .eq(StripeProduct::getStripePriceId, stripeScheduleRecord.getPriceId()).one();
                if (one != null) {
                    stripeScheduleRecord.setSubInterval(one.getPriceInterval());
                }
            }
        }
        this.save(stripeScheduleRecord);
        return stripeScheduleRecord;
    }
    // 基础的 CRUD 操作由 ServiceImpl 提供
}
