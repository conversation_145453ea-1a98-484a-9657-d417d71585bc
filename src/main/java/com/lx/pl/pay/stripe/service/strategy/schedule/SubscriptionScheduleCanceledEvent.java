package com.lx.pl.pay.stripe.service.strategy.schedule;

import com.lx.pl.pay.stripe.annotation.StripeEvent;
import com.lx.pl.pay.stripe.domain.StripeScheduleRecord;
import com.lx.pl.pay.stripe.domain.StripeUserCustomer;
import com.lx.pl.pay.stripe.service.StripeScheduleLogService;
import com.lx.pl.pay.stripe.service.StripeScheduleRecordService;
import com.lx.pl.pay.stripe.service.strategy.IStripeEventHandler;
import com.stripe.model.SubscriptionSchedule;
import org.redisson.api.RLock;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;

import static com.lx.pl.pay.PayConstant.SCHEDULE_LOCK_PREFIX;

/**
 * <AUTHOR>
 */
@Component
@StripeEvent(eventType = "subscription_schedule.canceled")
public class SubscriptionScheduleCanceledEvent extends IStripeEventHandler<SubscriptionSchedule> {

    @Resource
    private StripeScheduleRecordService stripeScheduleService;
    @Resource
    private StripeScheduleLogService stripeScheduleLogService;

    @Override
    public void handleEvent(SubscriptionSchedule event, String eventId) {
        String id = event.getId();
        RLock lock = redissonClient.getLock(SCHEDULE_LOCK_PREFIX + id);
        log.info("lock subscription_schedule.canceled : {} {}", eventId, SCHEDULE_LOCK_PREFIX + id);
        try {
            lock.lock();
            applicationContext.getBean(SubscriptionScheduleCanceledEvent.class).doHandleLogic(event, eventId);
        } finally {
            if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                lock.unlock();
                log.info("unlock subscription_schedule.canceled: {} {} {}", System.currentTimeMillis(), eventId, SCHEDULE_LOCK_PREFIX + id);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void doHandleLogic(SubscriptionSchedule event, String eventId) {
        StripeUserCustomer userCustomer = stripeUserCustomerService.lambdaQuery()
                .eq(StripeUserCustomer::getCustomerId, event.getCustomer()).one();
        stripeScheduleLogService.saveLog(event, userCustomer);
        log.info("handle subscription_schedule.canceled : {} customer: {}", event.getId(), event.getCustomer());
        StripeScheduleRecord one = stripeScheduleService.lambdaQuery()
                .eq(StripeScheduleRecord::getSubscriptionScheduleId, event.getId())
                .one();
        if (one == null) {
            one = stripeScheduleService.saveScheduleSubscription(event, userCustomer);
            // 订单完成的处理逻辑
            log.info("save subscription_schedule.canceled : {} customer: {}", event.getId(), event.getCustomer());
        }
        if (one != null) {
            stripeScheduleService.lambdaUpdate()
                    .eq(StripeScheduleRecord::getId, one.getId())
                    .set(StripeScheduleRecord::getScheduleStatus, event.getStatus())
                    .set(StripeScheduleRecord::getUpdateTime, LocalDateTime.now())
                    .update();
            log.info("update subscription_schedule.canceled : {} customer: {}", event.getId(), event.getCustomer());
        }
    }
}