package com.lx.pl.pay.stripe.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lx.pl.pay.stripe.domain.StripeFailedCancelSubscriptionRecords;
import com.lx.pl.pay.stripe.mapper.StripeFailedCancelSubscriptionRecordsMapper;
import com.lx.pl.pay.stripe.service.StripeFailedCancelSubscriptionRecordsService;
import org.springframework.stereotype.Service;

/**
 * Stripe 升级订阅时取消旧订阅失败记录表
 */
@Service
public class StripeFailedCancelSubscriptionRecordsServiceImpl extends ServiceImpl<StripeFailedCancelSubscriptionRecordsMapper, StripeFailedCancelSubscriptionRecords> implements StripeFailedCancelSubscriptionRecordsService {
    // 基础的 CRUD 操作由 ServiceImpl 提供


}
