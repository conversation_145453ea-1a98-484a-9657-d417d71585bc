package com.lx.pl.pay.stripe.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lx.pl.pay.stripe.domain.StripeSubscriptionLog;
import com.lx.pl.pay.stripe.domain.StripeUserCustomer;
import com.lx.pl.pay.stripe.mapper.StripeSubscriptionLogMapper;
import com.lx.pl.pay.stripe.service.StripeSubscriptionLogService;
import com.lx.pl.pay.stripe.service.StripeUserCustomerService;
import com.stripe.model.Subscription;
import com.stripe.model.SubscriptionItem;
import com.stripe.model.SubscriptionItemCollection;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * StripeSubscriptionLog服务实现类
 */
@Service
public class StripeSubscriptionLogServiceImpl extends ServiceImpl<StripeSubscriptionLogMapper, StripeSubscriptionLog> implements StripeSubscriptionLogService {

    @Resource
    private StripeUserCustomerService stripeUserCustomerService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveSubscriptionLog(Subscription event) {
        StripeSubscriptionLog subscription = new StripeSubscriptionLog();
        subscription.setCreateTime(LocalDateTime.now());
        subscription.setSubscriptionId(event.getId());
        subscription.setSubStatus(event.getStatus());
        subscription.setCustomerId(event.getCustomer());
        SubscriptionItemCollection items = event.getItems();
        if (items != null) {
            List<SubscriptionItem> data = items.getData();
            if (data != null && !data.isEmpty()) {
                SubscriptionItem subscriptionItem = data.get(0);
                subscription.setProductId(subscriptionItem.getPrice().getProduct());
                subscription.setSubscriptionItemId(subscriptionItem.getId());
                subscription.setSubInterval(subscriptionItem.getPrice().getRecurring().getInterval());
                subscription.setPriceId(subscriptionItem.getPrice().getId());
                subscription.setPriceType(subscriptionItem.getPrice().getType());
            }
        }
        subscription.setStartDate(event.getStartDate());
        subscription.setCurrentPeriodStart(event.getCurrentPeriodStart());
        subscription.setCurrentPeriodEnd(event.getCurrentPeriodEnd());
        subscription.setLogicPeriodStart(event.getCurrentPeriodStart());
        subscription.setLogicPeriodEnd(event.getCurrentPeriodEnd());
        subscription.setTrialStart(event.getTrialStart());
        subscription.setTrialEnd(event.getTrialEnd());
        StripeUserCustomer userCustomer = stripeUserCustomerService.lambdaQuery()
                .eq(StripeUserCustomer::getCustomerId, event.getCustomer()).one();
        if (userCustomer != null) {
            subscription.setUserId(userCustomer.getUserId());
            subscription.setLoginName(userCustomer.getLoginName());
        }
        this.save(subscription);
    }
}
