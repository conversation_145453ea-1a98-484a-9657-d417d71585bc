package com.lx.pl.pay.stripe.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lx.pl.db.mysql.gen.entity.MyBaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * Stripe 订阅计划记录实体类
 */
@Data
@TableName("stripe_schedule_record")
public class StripeScheduleRecord extends MyBaseEntity {

    /**
     * 主键 ID
     */
    @Schema(description = "主键 ID")
    @TableId(value = "id")
    private Long id;

    /**
     * 用户 ID
     */
    @Schema(description = "用户 ID")
    private Long userId;

    /**
     * 用户账号
     */
    @Schema(description = "用户账号")
    private String loginName;

    /**
     * 订阅 ID
     */
    @Schema(description = "订阅 ID")
    private String subscriptionId;

    /**
     * 预定阅 ID
     */
    @Schema(description = "预定阅 ID")
    private String subscriptionScheduleId;

    /**
     * Stripe 客户 ID
     */
    @Schema(description = "Stripe 客户 ID")
    private String customerId;

    /**
     * Stripe 价格 ID
     */
    @Schema(description = "Stripe 价格 ID")
    private String priceId;

    /**
     * 计划状态
     */
    @Schema(description = "计划状态")
    private String scheduleStatus;

    /**
     * 订阅周期（月、年）
     */
    @Schema(description = "订阅周期（月、年）")
    private String subInterval;

    /**
     * schedule 开始时间
     */
    @Schema(description = "schedule 开始时间")
    private Long startDate;

    /**
     * schedule 结束时间
     */
    @Schema(description = "schedule 结束时间")
    private Long endDate;

    /**
     * 取消时间
     */
    @Schema(description = "取消时间")
    private Long cancelledAt;
}
