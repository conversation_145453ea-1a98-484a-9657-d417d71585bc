package com.lx.pl.pay.stripe.service;

import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.pay.stripe.dto.BuyItemDto;
import com.lx.pl.pay.stripe.dto.SubscriptionScheduleDTO;
import com.stripe.exception.StripeException;
import com.stripe.model.Customer;
import com.stripe.model.SubscriptionSchedule;
import com.stripe.model.checkout.Session;

public interface StripePayService {

    Session createPayment(BuyItemDto buyItemDto, User user);

    void upgradeSubscription(BuyItemDto buyItemDto, User user);

    void cancelSubscriptionSchedule(User user);

    SubscriptionSchedule findNotStartedSubscriptionSchedule(User user);

    Customer createCustomer(String email, String name) throws StripeException;

    com.stripe.model.billingportal.Session createPortal(User user, String returnUrl);

    void upgradeSubscriptionByNewProduct(BuyItemDto buyItemDto, User user);

    void cancelSubscriptionFuture(User user);

    void uncancelSubscriptionFuture(User user);

    SubscriptionScheduleDTO changeSubscriptionDelay(BuyItemDto buyItemDto, User user);

    void checkInvoiceAndPayment();
}
