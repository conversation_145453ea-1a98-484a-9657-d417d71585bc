package com.lx.pl.pay.stripe.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.thread.ThreadUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lx.pl.pay.apple.config.PayBeanConfig;
import com.lx.pl.pay.common.service.SubscriptionCurrentService;
import com.lx.pl.pay.stripe.domain.StripeSubscriptionRecord;
import com.lx.pl.pay.stripe.domain.StripeUserCustomer;
import com.lx.pl.pay.stripe.mapper.StripeSubscriptionRecordMapper;
import com.lx.pl.pay.stripe.service.StripeSubscriptionRecordService;
import com.lx.pl.pay.stripe.service.StripeUserCustomerService;
import com.stripe.Stripe;
import com.stripe.exception.StripeException;
import com.stripe.model.Subscription;
import com.stripe.model.SubscriptionItem;
import com.stripe.model.SubscriptionItemCollection;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * Stripe 订阅记录服务实现类
 */
@Service
public class StripeSubscriptionRecordServiceImpl extends ServiceImpl<StripeSubscriptionRecordMapper, StripeSubscriptionRecord> implements StripeSubscriptionRecordService {
    @Resource
    protected StripeUserCustomerService stripeUserCustomerService;
    @Value("${stripe.client.secretKey}")
    private String secretKey;
    Logger log = LoggerFactory.getLogger("stripe-pay-msg");
    @Resource
    private PayLogicPurchaseRecordServiceImpl payLogicPurchaseRecordService;
    @Resource
    private SubscriptionCurrentService subscriptionCurrentService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public StripeSubscriptionRecord saveSubscription(Subscription event) {
        StripeUserCustomer userCustomer = stripeUserCustomerService.lambdaQuery()
                .eq(StripeUserCustomer::getCustomerId, event.getCustomer()).one();
        StripeSubscriptionRecord subscription = new StripeSubscriptionRecord();
        subscription.setCreateTime(LocalDateTime.now());
        subscription.setSubscriptionId(event.getId());
        subscription.setSubStatus(event.getStatus());
        subscription.setCustomerId(event.getCustomer());
        SubscriptionItemCollection items = event.getItems();
        if (items != null) {
            List<SubscriptionItem> data = items.getData();
            if (data != null && !data.isEmpty()) {
                SubscriptionItem subscriptionItem = data.get(0);
                subscription.setProductId(subscriptionItem.getPrice().getProduct());
                subscription.setSubscriptionItemId(subscriptionItem.getId());
                subscription.setSubInterval(subscriptionItem.getPrice().getRecurring().getInterval());
                subscription.setPriceId(subscriptionItem.getPrice().getId());
                subscription.setPriceType(subscriptionItem.getPrice().getType());
            }
        }
        subscription.setStartDate(event.getStartDate());
        subscription.setCurrentPeriodStart(event.getCurrentPeriodStart());
        subscription.setCurrentPeriodEnd(event.getCurrentPeriodEnd());
        subscription.setLogicPeriodStart(event.getCurrentPeriodStart());
        subscription.setLogicPeriodEnd(event.getCurrentPeriodEnd());
        if (userCustomer != null) {
            subscription.setUserId(userCustomer.getUserId());
            subscription.setLoginName(userCustomer.getLoginName());
        }
        this.save(subscription);
        return subscription;
    }

    @Override
    public void fixVip() {
        Stripe.apiKey = secretKey;
        List<StripeSubscriptionRecord> canceldList = this.lambdaQuery()
                .eq(StripeSubscriptionRecord::getSubStatus, "canceled")
                .list();

        Set<String> subIds = new HashSet<>();
        log.info("start fixVip {}", canceldList.size());
        if (CollUtil.isNotEmpty(canceldList)) {
            for (StripeSubscriptionRecord stripeSubscriptionRecord : canceldList) {
                ThreadUtil.sleep(100);
                String subscriptionId = stripeSubscriptionRecord.getSubscriptionId();
                if (subIds.contains(subscriptionId)) {
                    log.warn("fixVip continue {}", subscriptionId);
                    continue;
                }
                subIds.add(subscriptionId);
                try {
                    Subscription subscription = Subscription.retrieve(subscriptionId);
                    if (subscription != null) {
                        String status = subscription.getStatus();
                        if ("canceled".equals(status) && subscription.getCancellationDetails() != null && subscription.getCancellationDetails().getReason().equals("payment_failed")) {
                            log.warn("start fix vip {} {}", stripeSubscriptionRecord.getLoginName(), stripeSubscriptionRecord.getSubscriptionId());
                            boolean successRecall = payLogicPurchaseRecordService.recallLogicPurchaseRecord(stripeSubscriptionRecord, subscription.getLatestInvoice(), subscription.getCancellationDetails()
                                    .getReason());
                            if (successRecall) {
                                log.warn("start recall vip {} {}", stripeSubscriptionRecord.getLoginName(), stripeSubscriptionRecord.getSubscriptionId());
                                subscriptionCurrentService.recallStripeVipTime(stripeSubscriptionRecord);
                            }
                        }
                    }
                } catch (StripeException e) {
                    log.error("fixVip error {}", subscriptionId, e);
                }
            }
        }
    }
}
