package com.lx.pl.pay.stripe.service.strategy.payment;

import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.lx.pl.pay.stripe.annotation.StripeEvent;
import com.lx.pl.pay.stripe.domain.StripePaymentIntent;
import com.lx.pl.pay.stripe.service.StripePaymentIntentService;
import com.lx.pl.pay.stripe.service.strategy.IStripeEventHandler;
import com.stripe.model.PaymentIntent;
import com.stripe.model.StripeError;
import org.redisson.api.RLock;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

import java.time.LocalDateTime;

import static com.lx.pl.pay.PayConstant.PAYMENT_LOCK_PREFIX;

/**
 * 客户的付款被卡组织拒绝或已过期
 *
 * <AUTHOR>
 */
@Component
@StripeEvent(eventType = "payment_intent.payment_failed")
public class PaymentIntentPaymentFailedEvent extends IStripeEventHandler<PaymentIntent> {
    @Resource
    private StripePaymentIntentService stripePaymentIntentService;

    @Override
    public void handleEvent(PaymentIntent event, String eventId) {
        String id = event.getId();
        RLock lock = redissonClient.getLock(PAYMENT_LOCK_PREFIX + id);
        log.info("payment_intent.payment_failed: {}", event.getId());
        try {
            lock.lock();
            applicationContext.getBean(PaymentIntentPaymentFailedEvent.class)
                    .doHandleLogic(event, id);
        } finally {
            if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                lock.unlock();
                log.info("unlock payment_intent.payment_failed: {}", event.getId());
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void doHandleLogic(PaymentIntent event, String id) {
        StripePaymentIntent one = stripePaymentIntentService.lambdaQuery()
                .eq(StripePaymentIntent::getPaymentIntentId, id)
                .one();
        if (one == null) {
            stripePaymentIntentService.savePaymentIntent(event);
            log.info("save payment_intent.created : {} customer: {}", event.getId(), event.getCustomer());
        } else {
            boolean isFinalized = PaymentUtil.isTerminalState(one);
            if (isFinalized) {
                log.info("exist payment_intent.created : {} customer: {} {}", event.getId(), event.getCustomer(), one.getStatus());
                return;
            }
            StripeError lastPaymentError = event.getLastPaymentError();
            LambdaUpdateChainWrapper<StripePaymentIntent> updateWrapper = stripePaymentIntentService.lambdaUpdate()
                    .eq(StripePaymentIntent::getPaymentIntentId, id)
                    .set(StripePaymentIntent::getUpdateTime, LocalDateTime.now())
                    .set(StripePaymentIntent::getStatus, event.getStatus());
            if (lastPaymentError != null) {
                updateWrapper
                        .set(StripePaymentIntent::getErrorType, lastPaymentError.getType())
                        .set(StripePaymentIntent::getErrorCode, event.getLastPaymentError()
                                .getCode())
                        .set(StripePaymentIntent::getDeclineCode, event.getLastPaymentError()
                                .getDeclineCode())
                        .set(StripePaymentIntent::getErrorMessage, event.getLastPaymentError()
                                .getMessage());
            }
            updateWrapper.update();
        }
    }
}