package com.lx.pl.pay.stripe.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lx.pl.db.mysql.gen.entity.StripeProduct;
import com.lx.pl.pay.stripe.domain.StripeSubscriptionRecord;
import com.lx.pl.pay.stripe.domain.StripeTrialLog;
import com.lx.pl.pay.stripe.mapper.StripeTrialLogMapper;
import com.lx.pl.pay.stripe.service.StripeTrialLogService;
import org.springframework.stereotype.Component;

@Component
public class StripeTrialLogServiceImpl extends ServiceImpl<StripeTrialLogMapper, StripeTrialLog> implements StripeTrialLogService {

    @Override
    public void saveTrailLog(StripeSubscriptionRecord event, StripeProduct stripeProduct) {
        StripeTrialLog stripeTrialLog = new StripeTrialLog();
        stripeTrialLog.setUserId(event.getUserId());
        stripeTrialLog.setLoginName(event.getLoginName());
        stripeTrialLog.setSubscriptionId(event.getSubscriptionId());
        stripeTrialLog.setPriceId(event.getPriceId());
        stripeTrialLog.setTrialDay(stripeProduct.getTrialDay());
        stripeTrialLog.setTrialStart(event.getStartDate());
        stripeTrialLog.setTrialEnd(event.getLogicPeriodEnd());
        stripeTrialLog.setTrialLumen(stripeProduct.getInitialLumen());
        this.save(stripeTrialLog);
    }
}