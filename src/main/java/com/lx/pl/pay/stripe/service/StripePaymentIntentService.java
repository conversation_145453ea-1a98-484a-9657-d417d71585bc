package com.lx.pl.pay.stripe.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lx.pl.pay.stripe.domain.StripePaymentIntent;
import com.stripe.model.PaymentIntent;

/**
 * Stripe 支付意图服务接口
 */
public interface StripePaymentIntentService extends IService<StripePaymentIntent> {
    void savePaymentIntent(PaymentIntent stripePaymentIntent);
    // 可以添加一些自定义方法，例如：
    // StripePaymentIntent findBySubscriptionId(String subscriptionId);
}
