package com.lx.pl.pay.stripe.controller;

import com.lx.pl.config.pay.StripConfig;
import com.lx.pl.pay.common.annotation.PayLogContext;
import com.lx.pl.pay.stripe.service.IPaymentService;
import com.lx.pl.pay.stripe.service.StripeService;

import com.stripe.model.Event;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/stripe/webhook")
public class StripeWebhookController {
    Logger log = LoggerFactory.getLogger("stripe-pay-msg");

    @Resource
    private IPaymentService paymentService;

    @Autowired
    private StripeService stripeService1;
    @Resource
    private StripConfig stripConfig;

    /**
     * @param request
     * @return
     * @throws IOException
     */
    @PostMapping(value = "/customer")
    @PayLogContext("stripe-webhook-")
    public ResponseEntity<String> customerCallback(HttpServletRequest request, HttpServletResponse response) throws IOException {
        try {
            Event event = stripeService1.constructEvent(request, stripConfig.getWebhookSecretCustomer(), stripConfig.getSecretKey());
            paymentService.processEvent(event);
        } catch (Exception e) {
            log.error("customerCallback error: ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Webhook Error: " + e.getMessage() + " \n" + Arrays.toString(e.getStackTrace()));
        }
        return ResponseEntity.ok("Event processed successfully.");
    }
}
