package com.lx.pl.pay.stripe.service.strategy.schedule;

import com.lx.pl.pay.stripe.annotation.StripeEvent;
import com.lx.pl.pay.stripe.domain.StripeScheduleRecord;
import com.lx.pl.pay.stripe.domain.StripeUserCustomer;
import com.lx.pl.pay.stripe.service.StripeScheduleLogService;
import com.lx.pl.pay.stripe.service.StripeScheduleRecordService;
import com.lx.pl.pay.stripe.service.strategy.IStripeEventHandler;
import com.stripe.model.SubscriptionSchedule;
import org.redisson.api.RLock;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

import static com.lx.pl.pay.PayConstant.SCHEDULE_LOCK_PREFIX;

/**
 * <AUTHOR>
 */
@Component
@StripeEvent(eventType = "subscription_schedule.created")
public class SubscriptionScheduleCreatedEvent extends IStripeEventHandler<SubscriptionSchedule> {

    @Resource
    private StripeScheduleRecordService subscriptionScheduleRecordService;
    @Resource
    private StripeScheduleLogService stripeScheduleLogService;

    @Override
    public void handleEvent(SubscriptionSchedule event, String eventId) {
        String id = event.getId();
        RLock lock = redissonClient.getLock(SCHEDULE_LOCK_PREFIX + id);
        log.info("lock subscription_schedule.created : {} {}", eventId, SCHEDULE_LOCK_PREFIX + id);
        try {
            lock.lock();
            applicationContext.getBean(SubscriptionScheduleCreatedEvent.class).doHandleLogic(event, eventId);
        } finally {
            if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                lock.unlock();
                log.info("unlock subscription_schedule.created : {} {}", eventId, SCHEDULE_LOCK_PREFIX + id);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void doHandleLogic(SubscriptionSchedule event, String eventId) {
        log.info("doHandleLogic subscription_schedule.created : {}", eventId);
        StripeUserCustomer userCustomer = stripeUserCustomerService.lambdaQuery()
                .eq(StripeUserCustomer::getCustomerId, event.getCustomer()).one();
        stripeScheduleLogService.saveLog(event, userCustomer);
        StripeScheduleRecord one = subscriptionScheduleRecordService.lambdaQuery()
                .eq(StripeScheduleRecord::getSubscriptionScheduleId, event.getId())
                .one();
        if (one == null) {
            subscriptionScheduleRecordService.saveScheduleSubscription(event, userCustomer);
            // 订单完成的处理逻辑
            log.info("save subscription_schedule.created : {} customer: {}", event.getId(), event.getCustomer());
        }
    }
}