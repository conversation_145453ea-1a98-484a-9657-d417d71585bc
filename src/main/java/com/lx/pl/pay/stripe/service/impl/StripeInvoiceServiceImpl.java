package com.lx.pl.pay.stripe.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lx.pl.pay.stripe.domain.StripeInvoice;
import com.lx.pl.pay.stripe.mapper.StripeInvoiceMapper;
import com.lx.pl.pay.stripe.service.StripeInvoiceService;
import org.springframework.stereotype.Service;

/**
 * 支付逻辑购买记录服务实现类
 */
@Service
public class StripeInvoiceServiceImpl extends ServiceImpl<StripeInvoiceMapper, StripeInvoice> implements StripeInvoiceService {
    // 基础的 CRUD 操作由 ServiceImpl 提供
}
