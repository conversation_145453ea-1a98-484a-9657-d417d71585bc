package com.lx.pl.pay.stripe.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lx.pl.pay.stripe.domain.StripeScheduleLog;
import com.lx.pl.pay.stripe.domain.StripeUserCustomer;
import com.stripe.model.SubscriptionSchedule;

/**
 * StripeScheduleLog 服务接口
 */
public interface StripeScheduleLogService extends IService<StripeScheduleLog> {
    void saveLog(SubscriptionSchedule event, StripeUserCustomer userCustomer);
}
