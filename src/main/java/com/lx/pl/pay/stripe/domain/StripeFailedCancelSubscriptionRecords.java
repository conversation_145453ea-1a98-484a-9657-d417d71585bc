package com.lx.pl.pay.stripe.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lx.pl.db.mysql.gen.entity.MyBaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@TableName("stripe_failed_cancel_subscription_records")
public class StripeFailedCancelSubscriptionRecords extends MyBaseEntity {
    /**
     * 主键 ID
     */
    @Schema(description = "主键 ID")
    @TableId(value = "id")
    private Long id;

    /**
     * 用户 ID
     */
    @Schema(description = "用户 ID")
    private Long userId;

    /**
     * 用户账号
     */
    @Schema(description = "用户账号")
    private String loginName;

    /**
     * Stripe 客户 ID
     */
    @Schema(description = "Stripe 客户 ID")
    private String customerId;
    /**
     * 取消失败的 老的订阅 ID
     */
    @Schema(description = "取消失败的 老的订阅 ID")
    private String oldSubscriptionId;

    /**
     * 已经升级成功订阅 ID
     */
    @Schema(description = "已经升级成功订阅 ID")
    private String newSubscriptionId;

    /**
     * 新产品价格 ID
     */
    @Schema(description = "新产品价格 ID")
    private String newPriceId;

}
