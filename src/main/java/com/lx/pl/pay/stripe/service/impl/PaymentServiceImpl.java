package com.lx.pl.pay.stripe.service.impl;

import cn.hutool.json.JSONUtil;
import com.lx.pl.pay.stripe.service.IPaymentService;
import com.lx.pl.pay.stripe.service.strategy.EventHandlerContext;
import com.lx.pl.pay.stripe.service.strategy.IStripeEventHandler;
import com.lx.pl.pay.common.util.PayLogContextHolder;
import com.stripe.model.Event;
import com.stripe.model.EventDataObjectDeserializer;
import com.stripe.model.HasId;
import com.stripe.model.StripeObject;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class PaymentServiceImpl implements IPaymentService {

    @Autowired
    private EventHandlerContext orderHandlerContext;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Resource
    protected RedissonClient redissonClient;

    @Override
    public void processEvent(Event event) {
        RLock lock = redissonClient.getLock("stripe_event:" + event.getId());
        try {
            lock.lock();
            Map<String, Object> webhookInfo = new HashMap<>();
            // 根据_id 查询
            log.info("start processEvent: {}", event.getId());
            Event eventExist = mongoTemplate.findById(event.getId(), Event.class, "stripe_webhook_log");
            if (eventExist != null) {
                log.info("processEvent: event exist: {}", event.getId());
                return;
            }
            // Deserialize the nested object inside the event
            EventDataObjectDeserializer dataObjectDeserializer = event.getDataObjectDeserializer();
            if (dataObjectDeserializer.getObject().isPresent()) {

                StripeObject stripeObject = dataObjectDeserializer.getObject().get();
                String type = event.getType();
                if (stripeObject != null) {
                    try {
                        webhookInfo.put("object_id", ((HasId) stripeObject).getId());
                    } catch (Exception e) {
                        log.error("processEvent: no object_id for event type: {} {}", type, stripeObject.toJson(), e);
                    }
                }
                webhookInfo.put("type", type);
                log.info("processEvent: {} {}", type, stripeObject.toJson());
                IStripeEventHandler<?> handler = orderHandlerContext.getHandler(type);

                if (handler != null) {
                    String id = event.getId();
                    handler.doHandle(stripeObject, id);
                } else {
                    log.info("processEvent: no handler for event type: {}", type);
                }
            } else {
                // Deserialization failed, probably due to an API version mismatch.
                // Refer to the Javadoc documentation on `EventDataObjectDeserializer` for
                // instructions on how to handle this case, or return an error here.
                log.error("Deserialization failed, probably due to an API version mismatch: {} {}", event.getId(), event.toJson());
                throw new RuntimeException("processEvent: deserialize error");
            }
            // 使用 MongoTemplate 保存数据
            Map<String, Object> logData = new HashMap<>();
            logData.put("uuid", PayLogContextHolder.getLogUUID());
            logData.put("event", JSONUtil.parseObj(event.toJson()));
            logData.put("type", "WEBHOOK-STRIPE");
            logData.put("createTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

            // 创建更清晰的数据结构
            webhookInfo.put("event_id", event.getId());
            webhookInfo.put("api_version", event.getApiVersion());
            if (event.getRequest() != null) {
                webhookInfo.put("request_id", event.getRequest().getId());
            }
            webhookInfo.put("livemode", event.getLivemode());
            logData.put("webhookInfo", webhookInfo);
            try {
                mongoTemplate.insert(logData, "stripe_webhook_log");
            } catch (Exception e) {
                log.error("saveToMongoLog error", e);
            }
            log.info("end processEvent: {}", event.getId());
        } finally {
            if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                lock.unlock();
            }
        }
    }
}
