package com.lx.pl.pay.stripe.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SubscriptionScheduleDTO {
    /**
     * 计划等级
     */
    @Schema(description = "计划等级（例如：standard，pro）")
    private String planLevel;

    /**
     * 价格间隔
     */
    @Schema(description = "价格间隔（例如：year，month）")
    private String priceInterval;
    /**
     * 生效时间戳（秒）
     */
    @Schema(description = "生效时间戳（秒）")
    private Long startDate;
}
