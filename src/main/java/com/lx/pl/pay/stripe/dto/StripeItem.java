package com.lx.pl.pay.stripe.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class StripeItem {
    //plan, one 不能为null

    @Schema(description = "plan, one")
    private PaymentType type;
    //standard，pro
    @Schema(description = "standard, pro")
    private String product;
    // year, month
    @Schema(description = "价格间隔 year / month")
    private String price;
    // 订阅数量
    @Schema(description = "订阅数量")
    private Long amount;
    @Schema(description = "lumen数量 100 / 1000/ 10000")
    private Integer lumen;

}