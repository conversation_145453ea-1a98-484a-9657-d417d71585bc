package com.lx.pl.pay.stripe.service.strategy.payment;

import com.lx.pl.pay.stripe.annotation.StripeEvent;
import com.lx.pl.pay.stripe.domain.StripePaymentIntent;
import com.lx.pl.pay.stripe.service.StripePaymentIntentService;
import com.lx.pl.pay.stripe.service.strategy.IStripeEventHandler;
import com.stripe.model.PaymentIntent;
import org.redisson.api.RLock;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

import static com.lx.pl.pay.PayConstant.PAYMENT_LOCK_PREFIX;

/**
 * <AUTHOR>
 */
@Component
@StripeEvent(eventType = "payment_intent.created")
public class PaymentIntentCreatedEvent extends IStripeEventHandler<PaymentIntent> {

    @Resource
    private StripePaymentIntentService stripePaymentIntentService;

    @Override
    public void handleEvent(PaymentIntent event, String eventId) {
        String id = event.getId();
        RLock lock = redissonClient.getLock(PAYMENT_LOCK_PREFIX + id);
        log.info("payment_intent.created: {}", event.getId());
        try {
            lock.lock();
            applicationContext.getBean(PaymentIntentCreatedEvent.class).doHandleLogic(event, id);
        } finally {
            if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                lock.unlock();
                log.info("unlock payment_intent.created: {}", event.getId());
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void doHandleLogic(PaymentIntent event, String id) {
        StripePaymentIntent one = stripePaymentIntentService.lambdaQuery()
                .eq(StripePaymentIntent::getPaymentIntentId, id).one();
        if (one == null) {
            stripePaymentIntentService.savePaymentIntent(event);
            // 订单完成的处理逻辑
            log.info("save payment_intent.created : {} customer: {}", event.getId(), event.getCustomer());
            return;
        }
        log.info("exist payment_intent.created : {} customer: {}", event.getId(), event.getCustomer());
    }


}