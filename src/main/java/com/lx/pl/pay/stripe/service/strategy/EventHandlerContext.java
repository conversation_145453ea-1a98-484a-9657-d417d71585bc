package com.lx.pl.pay.stripe.service.strategy;


import com.lx.pl.pay.stripe.annotation.StripeEvent;
import org.springframework.aop.framework.AopProxyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Component
public class EventHandlerContext {
    private final Map<String, IStripeEventHandler<?>> eventHandlers = new HashMap<>();

    @Autowired
    private List<IStripeEventHandler<?>> handlerBeans;

    @PostConstruct
    public void init() {
        for (IStripeEventHandler<?> handler : handlerBeans) {
            Class<?> aClass = AopProxyUtils.ultimateTargetClass(handler);
            StripeEvent annotation = aClass.getAnnotation(StripeEvent.class);
            if (annotation != null) {
                String[] eventTypes = annotation.eventType();
                if (eventTypes.length == 0) {
                    throw new RuntimeException("EventHandlerContext: No eventType found on " + handler.getClass()
                            .getName());
                }
                for (String eventType : eventTypes) {
                    if (eventHandlers.containsKey(eventType)) {
                        throw new RuntimeException("EventHandlerContext: Duplicate eventType found on " + handler.getClass()
                                .getName());
                    }
                    eventHandlers.put(eventType, handler);
                }
            } else {
                throw new RuntimeException("EventHandlerContext: No StripeEvent annotation found on " + handler.getClass()
                        .getName());
            }
        }
    }

    public IStripeEventHandler<?> getHandler(String eventType) {
        return eventHandlers.get(eventType);
    }

}
