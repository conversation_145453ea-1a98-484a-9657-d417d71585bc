package com.lx.pl.pay.stripe.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lx.pl.db.mysql.gen.entity.MyBaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 支付逻辑购买记录实体类
 */
@Data
@TableName("pay_logic_purchase_record")
public class PayLogicPurchaseRecord extends MyBaseEntity {

    /**
     * 主键 ID
     */
    @Schema(description = "主键 ID")
    @TableId(value = "id")
    private Long id;

    /**
     * 用户 ID
     */
    @Schema(description = "用户 ID")
    private Long userId;

    /**
     * 用户账号
     */
    @Schema(description = "用户账号")
    private String loginName;

    /**
     * Stripe 客户 ID
     */
    @Schema(description = "Stripe 客户 ID")
    private String customerId;

    @Schema(description = "Stripe 账单发票 ID")
    private String invoiceId;

    /**
     * Stripe 价格 ID
     */
    @Schema(description = "Stripe 价格 ID")
    private String priceId;

    @Schema(description = "订阅周期（月、年）")
    private String subscriptionId;

    /**
     * Lumen 到期时间
     */
    @Schema(description = "Lumen 到期时间")
    private Long currentPeriodEnd;

    /**
     * Lumen 获得时间
     */
    @Schema(description = "Lumen 获得时间")
    private Long currentPeriodStart;

    /**
     * 逻辑订阅每周期结束时间
     */
    @Schema(description = "逻辑订阅每周期结束时间")
    private Long logicPeriodEnd;

    /**
     * 逻辑订阅每周期开始时间
     */
    @Schema(description = "逻辑订阅每周期开始时间")
    private Long logicPeriodStart;


    /**
     * Lumen 总数
     */
    @Schema(description = "Lumen 总数")
    private Integer lumenQty;


    /**
     * 购买数量
     */
    @Schema(description = "购买数量")
    private Integer count;


    private Boolean cancel;

    private String vipPlatForm;

    private Boolean trial;

    @TableField(exist = false)
    private Integer trialDays;
}
