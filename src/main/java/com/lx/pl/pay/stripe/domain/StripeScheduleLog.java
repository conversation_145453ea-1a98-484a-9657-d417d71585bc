package com.lx.pl.pay.stripe.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lx.pl.db.mysql.gen.entity.MyBaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Schema(description = "Stripe 预定阅记录日志")
@TableName("stripe_schedule_log")
public class StripeScheduleLog extends MyBaseEntity {

    @Schema(description = "主键 ID")
    @TableId(value = "id")
    private Long id;

    @Schema(description = "用户 ID")
    private Long userId;

    @Schema(description = "用户账号")
    private String loginName;

    @Schema(description = "订阅 ID")
    private String subscriptionId;

    @Schema(description = "预定阅 ID")
    private String subscriptionScheduleId;

    @Schema(description = "客户 ID")
    private String customerId;

    @Schema(description = "Stripe price ID")
    private String priceId;

    @Schema(description = "Schedule 状态")
    private String scheduleStatus;

    @Schema(description = "订阅周期 (month, year)")
    private String subInterval;

    @Schema(description = "Schedule 开始时间 (Unix 时间戳)")
    private Long startDate;

    @Schema(description = "Schedule 结束时间 (Unix 时间戳)")
    private Long endDate;

    @Schema(description = "取消时间 (Unix 时间戳)")
    private Long cancelledAt;
}
