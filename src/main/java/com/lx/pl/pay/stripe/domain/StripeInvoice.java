package com.lx.pl.pay.stripe.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lx.pl.db.mysql.gen.entity.MyBaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Stripe Invoice Entity
 */
@Data
@TableName("stripe_invoice")
public class StripeInvoice extends MyBaseEntity {

    /**
     * 主键 ID
     */
    @Schema(description = "主键 ID")
    @TableId(value = "id")
    private Long id;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Long userId;

    /**
     * 用户账号
     */
    @Schema(description = "用户账号")
    private String loginName;

    /**
     * 订阅ID
     */
    @Schema(description = "订阅ID")
    private String subscriptionId;

    /**
     * 支付意图 ID
     */
    @Schema(description = "支付意图ID")
    private String paymentIntentId;

    /**
     * 发票 ID
     */
    @Schema(description = "发票 ID")
    private String invoiceId;

    /**
     * 支付原因
     */
    @Schema(description = "支付原因")
    private String billingReason;

    /**
     * 币种
     */
    @Schema(description = "币种")
    private String currency;

    /**
     * 客户ID
     */
    @Schema(description = "客户ID")
    private String customerId;

    /**
     * 应付金额
     */
    @Schema(description = "应付金额")
    private Long amountDue;

    /**
     * 剩余金额
     */
    @Schema(description = "剩余金额")
    private Long amountRemaining;

    /**
     * 实际支付金额
     */
    @Schema(description = "实际支付金额")
    private Long amountPaid;

    /**
     * 排除税的金额
     */
    @Schema(description = "金额(排除税)")
    private String amountExcludingTax;

    private Long total;

    private Long totalExcludingTax;
//
//    /**
//     * 单价
//     */
//    @Schema(description = "单价")
//    private BigDecimal unitAmount;
//
//    /**
//     * 价格ID
//     */
//    @Schema(description = "价格ID")
//    private String priceId;
//
//    /**
//     * 价格间隔
//     */
//    @Schema(description = "价格间隔 (如: month, year)")
//    private String priceInterval;

    /**
     * 发票状态
     */
    @Schema(description = "状态")
    private String status;


    /**
     * 托管发票链接
     */
    @Schema(description = "Stripe 托管发票链接")
    private String hostedInvoiceUrl;

    /**
     * 发票 PDF
     */
    @Schema(description = "发票 PDF")
    private String invoicePdf;

    @Schema(description = "客户国家")
    private String customerCountry;
}
