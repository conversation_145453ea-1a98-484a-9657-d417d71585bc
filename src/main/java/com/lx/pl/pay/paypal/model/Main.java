package com.lx.pl.pay.paypal.model;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStreamWriter;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Scanner;

class Main {

    public static void main(String[] args) throws IOException {
        URL url = new URL("'https://api.sandbox.paypal.com/v1/billing/subscriptions/I-BNXU9LSK1E38");
        HttpURLConnection httpConn = (HttpURLConnection) url.openConnection();
        httpConn.setRequestMethod("PATCH");

        httpConn.setRequestProperty("Authorization", "Bearer A21AALaRkzfgjXoBWthfz_t-WFXBjioR7dZINbRjH-mSbU3IhA4EkRsyATAMMNFDatIiOlSmhQ9MykMuEfnYPDAtXFspB5eyg");
        httpConn.setRequestProperty("Content-Type", "application/json");
        httpConn.setRequestProperty("Accept", "application/json");

        httpConn.setDoOutput(true);
        OutputStreamWriter writer = new OutputStreamWriter(httpConn.getOutputStream());
//        writer.write("[ { \"op\": \"replace\", \"path\": \"/plan/billing_cycles/@sequence==1/pricing_scheme/fixed_price\", \"value\": { \"currency_code\": \"USD\", \"value\": \"50.00\" } }, { \"op\": \"replace\", \"path\": \"/plan/billing_cycles/@sequence==2/pricing_scheme/tiers\", \"value\": [ { \"starting_quantity\": \"1\", \"ending_quantity\": \"1000\", \"amount\": { \"value\": \"500\", \"currency_code\": \"USD\" } }, { \"starting_quantity\": \"1001\", \"amount\": { \"value\": \"2000\", \"currency_code\": \"USD\" } } ] }, { \"op\": \"replace\", \"path\": \"/plan/payment_preferences/auto_bill_outstanding\", \"value\": true }, { \"op\": \"replace\", \"path\": \"/plan/payment_preferences/payment_failure_threshold\", \"value\": 1 }, { \"op\": \"replace\", \"path\": \"/plan/taxes/percentage\", \"value\": \"10\" } ]");
        /**
         * [
         *   "{\n  \"op\": \"replace\",\n  \"path\": \"start_time\",\n  \"value\": \"2025-04-14T12:11:37Z\"\n}"
         * ]
         */
        writer.write("[{\"op\":\"replace\",\"path\":\"/start_time\",\"value\":\"2025-04-14T12:11:37Z\"}]");
        writer.flush();
        writer.close();
        httpConn.getOutputStream().close();

        InputStream responseStream = httpConn.getResponseCode() / 100 == 2
                ? httpConn.getInputStream()
                : httpConn.getErrorStream();
        Scanner s = new Scanner(responseStream).useDelimiter("\\A");
        String response = s.hasNext() ? s.next() : "";
        System.out.println(response);
    }
}
