package com.lx.pl.pay.paypal.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.paypal.api.payments.Address;
import com.paypal.api.payments.Links;
import com.paypal.base.rest.PayPalResource;
import lombok.*;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class PaypalPaymentCaptureModel extends CustomPaypalResource {
    private PaypalCheckoutOrderModel.PurchaseUnit purchaseUnit;
    private PaypalCheckoutOrderModel.Amount amount;
    private Payer payer;
    private PaymentSource paymentSource;
    private String createTime;
    private boolean finalCapture;
    private List<Links> links;
    private String customerId;
    private PaymentCaptureDetails.SellerReceivableBreakdown sellerReceivableBreakdown;
    private String id;
    private String status;
    private String intent;


    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    @EqualsAndHashCode(callSuper = true)
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class PaymentSource extends PayPalResource {
        private Paypal paypal;

        // Getter and Setter
        public Paypal getPaypal() {
            return paypal;
        }

        public void setPaypal(Paypal paypal) {
            this.paypal = paypal;
        }

        @Data
        @EqualsAndHashCode(callSuper = true)
        @JsonIgnoreProperties(ignoreUnknown = true)
        @Builder
        @AllArgsConstructor
        @NoArgsConstructor
        public static class Paypal extends PayPalResource {
            private String emailAddress;
            private String accountId;
            private String accountStatus;
            private Name name;
            private Address address;
            private boolean appSwitchEligibility;
            private ExperienceContext experienceContext;
        }

        @Data
        @EqualsAndHashCode(callSuper = true)
        @JsonIgnoreProperties(ignoreUnknown = true)
        @Builder
        @AllArgsConstructor
        @NoArgsConstructor
        public static class ExperienceContext extends PayPalResource {
            private String brandName;
            private String locale;
            private String logoUrl;
            private String landingPage;
            private String shippingPreference;
            private String returnUrl;
            private String cancelUrl;
            private String paymentMethodPreference;
            private String paymentMethodAllowed;
            private String userAction;
        }

    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Payer extends PayPalResource {
        private Name name;
        private String emailAddress;
        private String payerId;
        private Address address;
    }


}