package com.lx.pl.pay.paypal.service.strategy;

import cn.hutool.core.util.StrUtil;
import com.lx.pl.pay.paypal.annotation.PaypalEvent;
import com.lx.pl.pay.paypal.model.PaypalPaymentSaleModel;
import com.lx.pl.pay.paypal.model.domain.PayPalSubPaymentRecord;
import com.lx.pl.pay.paypal.model.event.PaypalPaymentSaleEvent;
import com.lx.pl.pay.paypal.service.IPaypalEventHandler;
import org.redisson.api.RLock;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 *
 */
@Component
@PaypalEvent(eventType = {"PAYMENT.SALE.COMPLETED"})
public class PaypalPaymentSaleHandler extends IPaypalEventHandler<PaypalPaymentSaleEvent> {

    @Override
    public void handleEvent(PaypalPaymentSaleEvent data) {
        PaypalPaymentSaleModel saleModel = data.getModel();
        String billingAgreementId = saleModel.getBillingAgreementId();
        String id = saleModel.getId();
        String lockId = billingAgreementId == null ? id : billingAgreementId;
//        PayPalLogicSubscription payPalLogicSubscription = applicationContext.getBean(PayPalLogicSubscriptionService.class).queryBySubscriptionId(billingAgreementId);
//        if (payPalLogicSubscription != null) {
//            lockId= payPalLogicSubscription.getUserId().toString();
//        }
//        customId == null ? subscription.getId() : entries.getStr("userId"))
        log.info("lock key {}" ,PAYPAL_ACTION_LOCK_PREFIX + lockId);
        RLock lock = redissonClient.getLock(PAYPAL_ACTION_LOCK_PREFIX + lockId);
        String loginName = null;
        try {
            lock.lock();
            loginName = applicationContext.getBean(PaypalPaymentSaleHandler.class)
                    .doHandleEvent(data);
            if (StrUtil.isNotBlank(loginName)) {
                vipService.resettingPersonalLumens(loginName);
            }
        } finally {
            unlockAndRefreshVip(lock, loginName);
        }
    }


    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public String doHandleEvent(PaypalPaymentSaleEvent data) {
        PaypalPaymentSaleModel saleModel = data.getModel();
        PayPalSubPaymentRecord payPalSubPaymentRecord = payPalSubPaymentRecordService.saveRecordIfNeed(saleModel);
        if (payPalSubPaymentRecord != null) {
            // 计算会员逻辑
            log.info("start calculateVipByPayment");
            return payPalLogicSubscriptionService.calculateVipByPayment(payPalSubPaymentRecord);
        }

        return null;
    }
}
