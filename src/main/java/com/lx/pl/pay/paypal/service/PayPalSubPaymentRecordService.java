package com.lx.pl.pay.paypal.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lx.pl.pay.paypal.model.PaypalPaymentSaleModel;
import com.lx.pl.pay.paypal.model.domain.PayPalSubPaymentRecord;

public interface PayPalSubPaymentRecordService extends IService<PayPalSubPaymentRecord> {
    PayPalSubPaymentRecord saveRecordIfNeed(PaypalPaymentSaleModel saleModel);

    PayPalSubPaymentRecord queryByPaymentId(String paymentId);

    void updateRefundId(String paymentId, String refundId);
}
