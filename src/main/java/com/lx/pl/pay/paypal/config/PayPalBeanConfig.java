package com.lx.pl.pay.paypal.config;

import com.lx.pl.pay.paypal.model.CustomerApiContext;
import com.paypal.base.rest.APIContext;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
public class PayPalBeanConfig {
    @Value("${pay.paypal.client_id}")
    private String PAYPAL_CLIENT_ID;

    @Value("${pay.paypal.client_secret}")
    private String PAYPAL_CLIENT_SECRET;

    @Value("${pay.paypal.mode}")
    private String PAYPAL_MODE;

    @Value("${pay.paypal.webhook_id}")
    private String WEBHOOK_ID;


    @Bean
    public APIContext apiContext() {
        CustomerApiContext apiContext = new CustomerApiContext(PAYPAL_CLIENT_ID, PAYPAL_CLIENT_SECRET, PAYPAL_MODE);
        return apiContext;
    }

    public String getWebhookId() {
        return WEBHOOK_ID;
    }
}
