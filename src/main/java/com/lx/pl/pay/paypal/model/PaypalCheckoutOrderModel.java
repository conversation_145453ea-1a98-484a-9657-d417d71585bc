package com.lx.pl.pay.paypal.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.paypal.api.payments.Address;
import com.paypal.api.payments.Links;
import com.paypal.api.payments.Payee;
import com.paypal.base.rest.*;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class PaypalCheckoutOrderModel extends CustomPaypalResource {
    private String createTime;
    private List<PurchaseUnit> purchaseUnits;
    private Amount amount;
    private List<Links> links;
    private String id;
    private PaypalPaymentCaptureModel.PaymentSource paymentSource;
    private String intent;
    private PaypalPaymentCaptureModel.Payer payer;
    private String status;
    private String token;

    @EqualsAndHashCode(callSuper = true)
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class PurchaseUnit extends PayPalResource {
        private String referenceId;
        private String description;
        private Amount amount;
        private Items[] items;
        private Payee payee;
        private Shipping shipping;
        private String customId;

    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Items extends PayPalResource {
        private String name;
        private String description;
        private String quantity;
        private String sku;
        private String imageUrl = "https://uploads.piclumen.com/community/20250304/19/09de7a43-3fcc-4ee4-8f16-43fb2b28ec95.webp";
        private UnitAmount unitAmount;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class UnitAmount extends PayPalResource {
        private String currencyCode;
        private String value;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Breakdown extends PayPalResource {
        private ItemTotal itemTotal;
        private Discount discount;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ItemTotal extends PayPalResource {
        private String currencyCode;
        private String value;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Discount extends PayPalResource {
        private String currencyCode;
        private String value;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Amount extends PayPalResource {
        private String currencyCode;
        private String value;
        private Breakdown breakdown;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Shipping extends PayPalResource {
        private Name name;
        private Address address;
    }

    public static PaypalCheckoutOrderModel createOrder(APIContext apiContext, PaypalCheckoutOrderModel paypalCheckoutOrderModel) throws PayPalRESTException {
        if (paypalCheckoutOrderModel.getPaymentSource() == null) {
            throw new IllegalArgumentException("paymentSource cannot be null");
        }
        if (paypalCheckoutOrderModel.getIntent() == null) {
            throw new IllegalArgumentException("intent cannot be null");
        }
        if (paypalCheckoutOrderModel.getPurchaseUnits() == null) {
            throw new IllegalArgumentException("purchaseUnits cannot be null");
        }
        String pattern = "v2/checkout/orders";
        return executeWithRetry(apiContext, HttpMethod.POST, pattern, paypalCheckoutOrderModel.toJSON(), PaypalCheckoutOrderModel.class);
    }


    public static PaypalCheckoutOrderModel detail(APIContext apiContext, String orderId) throws PayPalRESTException {

        if (orderId == null) {
            throw new IllegalArgumentException("paymentId cannot be null");
        }
        String pattern = "v2/checkout/orders/{0}";
        Object[] parameters = new Object[]{orderId};
        String resourcePath = RESTUtil.formatURIPath(pattern, parameters);
        PaypalCheckoutOrderModel paypalSub = executeWithRetry(apiContext, HttpMethod.GET, resourcePath, "{}", PaypalCheckoutOrderModel.class);
        return paypalSub;
    }

    public static PaypalCheckoutOrderModel capture(APIContext apiContext, String paymentId) throws PayPalRESTException {

        if (paymentId == null) {
            throw new IllegalArgumentException("paymentId cannot be null");
        }
        String pattern = "v2/checkout/orders/{0}/capture";
        Object[] parameters = new Object[]{paymentId};
        String resourcePath = RESTUtil.formatURIPath(pattern, parameters);
        PaypalCheckoutOrderModel paypalSub = executeWithRetry(apiContext, HttpMethod.POST, resourcePath, "{}", PaypalCheckoutOrderModel.class);
        return paypalSub;
    }


}