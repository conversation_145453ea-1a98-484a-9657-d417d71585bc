package com.lx.pl.pay.paypal.model.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lx.pl.db.mysql.gen.entity.MyBaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("paypal_product")
public class PayPalProduct extends MyBaseEntity {

    /**
     * 主键 ID
     */
    @Schema(description = "主键 ID")
    @TableId(value = "id")
    private Long id;

    /**
     * paypal 生成的 ID
     */
    @Schema(description = "paypal 生成的 ID")
    private String paypalPlanId;

    private String productId;

    /**
     * lumen 数量/month
     */
    @Schema(description = "订阅： 每月lumen数量， one time 一次性发放数量")
    private Integer lumen;

    private Integer initialLumen;

    /**
     * 计划等级
     */
    @Schema(description = "计划等级（例如：standard，pro）")
    private String planLevel;

    /**
     * 产品类型
     */
    @Schema(description = "产品类型（例如：plan，one）")
    private String productType;

    /**
     * 价格间隔
     */
    @Schema(description = "价格间隔（例如：year，month）")
    private String priceInterval;

    @Schema(description = "是否为首购订阅价格")
    private Integer firstBuySub;

    @Schema(description = "原价")
    private String sourcePrice;

    @Schema(description = "优惠后价格")
    private String price;

//    @Schema(description = "折扣")
//    private Integer off;

    /**
     * vip 等級
     */
    @Schema(description = "vip 等級")
    private Integer vipLevel;

    /**
     * 商品描述信息
     */
    @Schema(description = "商品描述信息")
    private String mark;

    /**
     * 状态（0：禁用，1：启用）
     */
    private Boolean status;
}
