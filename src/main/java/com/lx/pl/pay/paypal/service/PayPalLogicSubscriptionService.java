package com.lx.pl.pay.paypal.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lx.pl.pay.paypal.model.PaypalSubscriptionModel;
import com.lx.pl.pay.paypal.model.domain.PayPalLogicSubscription;
import com.lx.pl.pay.paypal.model.domain.PayPalSubPaymentRecord;

public interface PayPalLogicSubscriptionService extends IService<PayPalLogicSubscription> {
    PayPalLogicSubscription queryBySubscriptionId(String subscriptionId);

    PayPalLogicSubscription createSubscription(PaypalSubscriptionModel model, String customerId);

    PayPalLogicSubscription activateSubscription(PaypalSubscriptionModel model, PayPalSubPaymentRecord payment);

    PayPalLogicSubscription updateSubscription(PaypalSubscriptionModel model);

    String calculateVipByPayment(PayPalSubPaymentRecord payPalSubPaymentRecord);

    String suspendedSubscription(PaypalSubscriptionModel model);

    String reActivedSubscription(PaypalSubscriptionModel model);

    PaypalSubscriptionModel queryPaypalSubDetail(String subscriptionId);
}