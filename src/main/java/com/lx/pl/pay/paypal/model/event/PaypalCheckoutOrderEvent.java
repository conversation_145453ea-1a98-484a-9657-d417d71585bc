package com.lx.pl.pay.paypal.model.event;

import com.lx.pl.pay.paypal.model.PaypalCheckoutOrderModel;
import com.lx.pl.pay.paypal.model.PaypalSubscriptionModel;
import com.paypal.base.rest.JSONFormatter;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class PaypalCheckoutOrderEvent extends PaypalEventModel {

    private PaypalCheckoutOrderModel model;

    public PaypalCheckoutOrderModel getModel() {
        if (model == null) {
            model = JSONFormatter.fromJSON(this.getResource(), PaypalCheckoutOrderModel.class);
        }
        return model;
    }
}
