package com.lx.pl.pay.paypal.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class BuyItemVo {
//    @Schema(description = "购买产品的类型")
//    private ProductItem productItem;
    @Schema(description = "购买产品的类型列表")
    private List<ProductItem> items;
    @Schema(description = "购买成功后跳转的url")
    private String successUrl;
    @Schema(description = "购买失败后跳转的url")
    private String cancelUrl;
}



