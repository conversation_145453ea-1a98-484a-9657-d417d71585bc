package com.lx.pl.pay.paypal.model.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lx.pl.db.mysql.gen.entity.MyBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * PayPal订单支付记录item实体类
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("paypal_order_payment_item")
public class PayPalOrderPaymentItem extends MyBaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 登录用户名
     */
    private String loginName;

    /**
     * 支付交易ID
     */
    private String orderId;

    /**
     * 产品ID
     */
    private String productId;

    /**
     * 数量
     */
    private Integer qty;

    /**
     * 支付状态
     */
    private String status;

    /**
     * 支付金额
     */
    private String amount;

    /**
     * 货币类型
     */
    private String currency;
}
