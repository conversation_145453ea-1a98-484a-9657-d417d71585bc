package com.lx.pl.pay.paypal.model.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.lx.pl.db.mysql.gen.entity.MyBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@TableName("paypal_link")
public class PaypalLink extends MyBaseEntity {
    private static final long serialVersionUID = 3415972908178720490L;
    private Long id;

    private Long referId;

    private String href;
    /**
     *
     */
    private String rel;

    /**
     *
     */
    private String method;
}
