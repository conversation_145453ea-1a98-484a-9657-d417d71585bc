package com.lx.pl.pay.paypal.service.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.enums.PayPalErrorCode;
import com.lx.pl.exception.PayPalException;
import com.lx.pl.pay.paypal.mapper.PayPalOrderPaymentRecordMapper;
import com.lx.pl.pay.paypal.model.PaypalCheckoutOrderModel;
import com.lx.pl.pay.paypal.model.domain.PayPalOrderPaymentItem;
import com.lx.pl.pay.paypal.model.domain.PayPalOrderPaymentRecord;
import com.lx.pl.pay.paypal.service.PayPalOrderPaymentItemService;
import com.lx.pl.pay.paypal.service.PayPalOrderPaymentRecordService;
import com.lx.pl.service.UserService;
import com.paypal.api.payments.Payee;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Service
public class PayPalOrderPaymentRecordServiceImpl extends ServiceImpl<PayPalOrderPaymentRecordMapper, PayPalOrderPaymentRecord> implements PayPalOrderPaymentRecordService {
    protected Logger log = LoggerFactory.getLogger("paypal-pay-msg");

    @Autowired
    private UserService userService;
    @Autowired
    private PayPalOrderPaymentItemService payPalOrderPaymentItemService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<PayPalOrderPaymentRecord> createPayment(PaypalCheckoutOrderModel data) {
        String paymentId = data.getId();
        List<PayPalOrderPaymentRecord> existRecords = this.findByPaymentId(paymentId);
        if (existRecords != null && !existRecords.isEmpty()) {
            log.info("paymentId:{} already exist", paymentId);
            return null;
        }

        List<PaypalCheckoutOrderModel.PurchaseUnit> purchaseUnits = data.getPurchaseUnits();
        if (purchaseUnits == null || purchaseUnits.isEmpty()) {
            log.info("paymentId:{} purchaseUnits is empty", paymentId);
            throw new PayPalException(PayPalErrorCode.INVALID_REQUEST_PARAMETERS);
        }
        List<PayPalOrderPaymentRecord> saveList = new ArrayList<>();

        String customerId = purchaseUnits.get(0).getCustomId();
        if (customerId == null) {
            log.info("customerId is empty");
            return null;
        }
        JSONObject entries = JSONUtil.parseObj(customerId);
        User userById = userService.getUserById(Long.valueOf(entries.getStr("userId")));
        if (userById == null) {
            log.info("user not found");
            return null;
        }
        List<PayPalOrderPaymentItem> payPalOrderPaymentItems = new ArrayList<>();
        for (PaypalCheckoutOrderModel.PurchaseUnit purchaseUnit1 : purchaseUnits) {
            PayPalOrderPaymentRecord existRecord = new PayPalOrderPaymentRecord();
            existRecord.setOrderId(paymentId);
            existRecord.setStatus(data.getStatus());
            String referenceId = purchaseUnit1.getReferenceId();

            existRecord.setUserId(userById.getId());
            existRecord.setLoginName(userById.getLoginName());
            existRecord.setReferenceId(referenceId);
            PaypalCheckoutOrderModel.Amount amount = purchaseUnit1.getAmount();
            Payee payee = purchaseUnit1.getPayee();
            existRecord.setAmount(amount.getValue());
            PaypalCheckoutOrderModel.Breakdown breakdown = amount.getBreakdown();
            if (breakdown != null) {
                PaypalCheckoutOrderModel.Discount discount = breakdown.getDiscount();
                if (discount != null)
                    existRecord.setDiscountAmount(discount.getValue());
                else {
                    existRecord.setDiscountAmount("0");
                }
            } else {
                existRecord.setDiscountAmount("0");
            }

            existRecord.setCurrency(amount.getCurrencyCode());
            existRecord.setPayeeEmail(payee.getEmail());
            existRecord.setPayeeId(payee.getMerchantId());
            existRecord.setCreateTime(LocalDateTime.now());
            existRecord.setPayCreateTime(data.getCreateTime());
            saveList.add(existRecord);
            PaypalCheckoutOrderModel.Items[] items = purchaseUnit1.getItems();
            for (PaypalCheckoutOrderModel.Items item : items) {
                PayPalOrderPaymentItem payPalOrderPaymentItem = new PayPalOrderPaymentItem();
                payPalOrderPaymentItem.setUserId(userById.getId());
                payPalOrderPaymentItem.setLoginName(userById.getLoginName());
                payPalOrderPaymentItem.setOrderId(paymentId);
                payPalOrderPaymentItem.setProductId(item.getSku());
                payPalOrderPaymentItem.setQty(Integer.valueOf(item.getQuantity()));
                payPalOrderPaymentItem.setAmount(item.getUnitAmount().getValue());
                payPalOrderPaymentItem.setCurrency(item.getUnitAmount().getCurrencyCode());
                payPalOrderPaymentItem.setCreateTime(LocalDateTime.now());
                payPalOrderPaymentItem.setStatus(data.getStatus());
                payPalOrderPaymentItems.add(payPalOrderPaymentItem);
            }
        }
        this.saveBatch(saveList);
        payPalOrderPaymentItemService.saveBatch(payPalOrderPaymentItems);
        log.info("paymentId:{} record created", paymentId);
        return saveList;
    }


    @Override
    public List<PayPalOrderPaymentRecord> findByPaymentId(String paymentId) {
        return this.lambdaQuery()
                .eq(PayPalOrderPaymentRecord::getOrderId, paymentId)
                .list();
    }
}