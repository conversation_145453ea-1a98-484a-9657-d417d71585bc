package com.lx.pl.pay.paypal.model.event;

import com.lx.pl.pay.paypal.model.PaypalPaymentSaleModel;
import com.paypal.base.rest.JSONFormatter;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class PaypalPaymentSaleEvent extends PaypalEventModel {

    private PaypalPaymentSaleModel model;

    public PaypalPaymentSaleModel getModel() {
        if (model == null) {
            model = JSONFormatter.fromJSON(this.getResource(), PaypalPaymentSaleModel.class);
        }
        return model;
    }
}
