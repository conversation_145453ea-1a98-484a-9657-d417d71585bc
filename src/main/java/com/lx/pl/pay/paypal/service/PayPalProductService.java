package com.lx.pl.pay.paypal.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lx.pl.pay.paypal.model.domain.PayPalProduct;
import com.lx.pl.pay.paypal.model.vo.ProductItem;

import java.util.List;

public interface PayPalProductService extends IService<PayPalProduct> {

    // 根据 planLevel, productType, priceInterval 查询 StripeProduct 对象
    PayPalProduct getPaypalProductByLevelTypeInterval(String planLevel, String productType, String priceInterval);

    PayPalProduct getPaypalProductByBuyItemDto(ProductItem stripeItem);

    PayPalProduct getPaypalProductByPlanId(String planId);

    List<PayPalProduct> getPaypalProductListByLumenList(List<Integer> lumenList);
}