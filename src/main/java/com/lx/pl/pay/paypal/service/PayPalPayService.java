package com.lx.pl.pay.paypal.service;

import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.pay.paypal.model.vo.BuyItemVo;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/2/27 18:22
 * @desciption:
 */
public interface PayPalPayService {
    Map<String, String> createPayment(BuyItemVo buyItemDto, User user);

    Map<String, String> upgradeDowngrade(BuyItemVo buyItemDto, String type, String opType, User user);

    void cancelSubscription(User user);

    Map<String, Object> queryValidNotHandleSubscriptions(User user);
}
