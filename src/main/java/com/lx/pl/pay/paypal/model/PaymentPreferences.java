package com.lx.pl.pay.paypal.model;

import com.paypal.base.rest.PayPalResource;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
public class PaymentPreferences extends PayPalResource {
    private boolean autoBillOutstanding;
    private String setupFeeFailureAction;
    private int paymentFailureThreshold;
    private SetupFee setupFee;

    // Getters and setters
}