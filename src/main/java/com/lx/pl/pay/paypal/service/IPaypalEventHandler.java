package com.lx.pl.pay.paypal.service;

import cn.hutool.core.util.StrUtil;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.pay.common.domain.SubscriptionCurrent;
import com.lx.pl.pay.common.service.SubscriptionCurrentService;
import com.lx.pl.pay.paypal.model.domain.PaypalEventLog;
import com.lx.pl.pay.paypal.model.event.PaypalEventModel;
import com.lx.pl.service.RedisService;
import com.lx.pl.service.UserService;
import com.lx.pl.service.VipService;
import com.paypal.base.rest.APIContext;
import com.paypal.base.rest.JSONFormatter;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.data.mongodb.core.MongoTemplate;

import javax.annotation.Resource;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.Arrays;

import static com.lx.pl.pay.common.service.impl.SubscriptionCurrentServiceImpl.CACHE_KEY_PREFIX;

/**
 * 订单状态处理
 *
 * <AUTHOR>
 */
public abstract class IPaypalEventHandler<T extends PaypalEventModel> implements ApplicationContextAware {
    protected Logger log = LoggerFactory.getLogger("paypal-pay-msg");
    @Resource
    protected RedissonClient redissonClient;
    @Resource
    protected SubscriptionCurrentService subscriptionCurrentService;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Resource
    protected PayPalLogicOrderService payPalLogicOrderService;
    @Resource
    protected PayPalLogicSubscriptionService payPalLogicSubscriptionService;
    @Resource
    protected PayPalPlanService payPalPlanService;
    @Resource
    protected PayPalRefundRecordService payPalRefundRecordService;
    @Resource
    protected PayPalSubPaymentRecordService payPalSubPaymentRecordService;
    @Resource
    protected PayPalSubReviseLogService payPalSubReviseLogService;
    @Resource
    protected PayPalOrderPaymentRecordService payPalOrderPaymentRecordService;
    @Resource
    protected PaypalEventLogService paypalEventLogService;
    @Resource
    protected PaypalLinkService paymentLinkService;
    @Resource
    protected RedisService redisService;
    @Resource
    protected UserService userService;
    @Resource
    protected VipService vipService;
    protected ApplicationContext applicationContext;
    @Resource
    protected APIContext apiContext;

    protected static final String PAYPAL_USER_LOCK_PREFIX = "paypal:user:";
    protected static final String PAYPAL_ACTION_LOCK_PREFIX = "paypal:action:";

    public abstract void handleEvent(T data);
    protected void clearUserCache(Long userId) {
        String cacheKey = CACHE_KEY_PREFIX + userId;
        redisService.delete(cacheKey);
    }
    protected void updateUserVipStatus(Long userId) {
        clearUserCache(userId);
        SubscriptionCurrent subscription = subscriptionCurrentService.getLogicValidHighSubscriptionsFromDb(userId);
        userService.updateUserVipInfo(subscription, userId);
    }

    protected void unlockAndRefreshVip(RLock lock, String loginName) {
        if (lock.isHeldByCurrentThread() && lock.isLocked()) {
            lock.unlock();
        }
        try {
            if (StrUtil.isNotBlank(loginName)) {
                User user = userService.getByLoginName(loginName);
                if (user != null) {
                    updateUserVipStatus(user.getId());
                }
            }
        } catch (Exception e) {
            log.error("更新用户VIP等级3 失败: {}", e.getMessage(), e);
        }
    }

    public void doHandle(PaypalEventModel data, PaypalEventLog paypalEventLog) {
        T t = JSONFormatter.fromJSON(data.toJSON(), getGenericType());
        try {
            this.handleEvent(t);
            paypalEventLogService.updateStatus(paypalEventLog.getId(), "success");
        } catch (Exception e) {
            paypalEventLogService.updateStatus( paypalEventLog.getId(), "fail_process", Arrays.toString(e.getStackTrace()));
            log.error("handleEvent error: {}", e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    public Class<T> getGenericType() {
        Type superClass = getClass().getGenericSuperclass();
        if (superClass instanceof ParameterizedType) {
            ParameterizedType parameterizedType = (ParameterizedType) superClass;
            Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();
            if (actualTypeArguments.length > 0 && actualTypeArguments[0] instanceof Class) {
                return (Class<T>) actualTypeArguments[0];
            }
        }
        return null; // 或者抛出异常
    }


    @Override
    public void setApplicationContext(@NotNull ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }
}
