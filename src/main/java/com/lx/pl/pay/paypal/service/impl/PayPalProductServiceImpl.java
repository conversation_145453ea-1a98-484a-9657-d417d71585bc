package com.lx.pl.pay.paypal.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lx.pl.enums.PayPalErrorCode;
import com.lx.pl.exception.PayPalException;
import com.lx.pl.pay.paypal.mapper.PayPalProductMapper;
import com.lx.pl.pay.paypal.model.domain.PayPalProduct;
import com.lx.pl.pay.paypal.model.vo.ProductItem;
import com.lx.pl.pay.paypal.service.PayPalProductService;
import com.lx.pl.pay.stripe.dto.PaymentType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class PayPalProductServiceImpl extends ServiceImpl<PayPalProductMapper, PayPalProduct> implements PayPalProductService {

    private static final Logger logger = LoggerFactory.getLogger(PayPalProductServiceImpl.class);

    // 根据 planLevel, productType, priceInterval 查询 PayPalProduct 对象
    @Override
    public PayPalProduct getPaypalProductByLevelTypeInterval(String planLevel, String productType, String priceInterval) {
        logger.info("Fetching PaypalProduct by planLevel: {}, productType: {}, priceInterval: {}", planLevel, productType, priceInterval);
        LambdaQueryWrapper<PayPalProduct> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PayPalProduct::getPlanLevel, planLevel)
                .eq(PayPalProduct::getProductType, productType)
                .eq(PayPalProduct::getStatus, true)
                .eq(PayPalProduct::getPriceInterval, priceInterval);

        return this.getOne(queryWrapper);
    }


    @Override
    public PayPalProduct getPaypalProductByBuyItemDto(ProductItem stripeItem) {
        logger.debug("Fetching paypalProduct by Item: {}", stripeItem);
        // 获取支付方式类型
        PaymentType paymentType = stripeItem.getType();
        switch (paymentType) {
            case PLAN:
                PayPalProduct payPalProductByLevelTypeInterval = getPaypalProductByLevelTypeInterval(stripeItem.getProduct(), paymentType.getType(), stripeItem.getPriceInterval());
                if (payPalProductByLevelTypeInterval == null) {
                    logger.error("PayPalProduct not found for planLevel: {}, productType: {}, priceInterval: {}", stripeItem.getProduct(), paymentType.getType(), stripeItem.getLumen());
                    throw new PayPalException(PayPalErrorCode.NO_PAYPAL_PRODUCT_FOUND);
                }
                return payPalProductByLevelTypeInterval;
            case ONE:
                // 处理一次性支付类型的产品
                LambdaQueryWrapper<PayPalProduct> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(PayPalProduct::getProductType, paymentType.getType());
                queryWrapper.eq(PayPalProduct::getLumen, stripeItem.getLumen());
                PayPalProduct one = this.getOne(queryWrapper);
                if (one == null) {
                    logger.error("PayPalProduct not found for productType: {}", paymentType.getType());
                    throw new PayPalException(PayPalErrorCode.NO_PAYPAL_PRODUCT_FOUND);
                }
                return one;
            default:
                logger.error("Unsupported payment type: {}", paymentType);
                throw new PayPalException(PayPalErrorCode.UNSUPPORTED_PAYMENT_TYPE);
        }
    }

    @Override
    public PayPalProduct getPaypalProductByPlanId(String planId) {
        logger.debug("Fetching paypalProduct by planId: {}", planId);
        return this.lambdaQuery().eq(PayPalProduct::getPaypalPlanId, planId)
                .eq(PayPalProduct::getStatus, true)
                .one();
    }

    @Override
    public List<PayPalProduct> getPaypalProductListByLumenList(List<Integer> lumenList) {
        logger.debug("Fetching paypalProduct by lumenList: {}", lumenList);
        if (lumenList != null && !lumenList.isEmpty()) {
            return this.lambdaQuery().in(PayPalProduct::getLumen, lumenList)
                    .eq(PayPalProduct::getProductType, PaymentType.ONE.getType())
                    .eq(PayPalProduct::getStatus, true)
                    .list();
        }
        return List.of();
    }
}
