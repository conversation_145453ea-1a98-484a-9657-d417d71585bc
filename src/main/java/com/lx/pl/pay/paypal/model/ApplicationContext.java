package com.lx.pl.pay.paypal.model;

import com.paypal.base.rest.PayPalResource;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;


@EqualsAndHashCode(callSuper = true)
@Data
@Builder
public class ApplicationContext extends PayPalResource {
    private String brandName; // 品牌名称
    private String locale; // 区域设置
    private String shippingPreference; // 配送偏好
    private String userAction; // 用户操作

    /**
     * The URL where the customer is redirected after the customer approves the payment.
     */
    private String returnUrl;

    /**
     * The URL where the customer is redirected after the customer cancels the payment.
     */
    private String cancelUrl;

    // Getters and Setters
}