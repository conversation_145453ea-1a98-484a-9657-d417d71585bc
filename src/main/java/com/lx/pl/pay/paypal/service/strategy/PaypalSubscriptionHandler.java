package com.lx.pl.pay.paypal.service.strategy;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.lx.pl.pay.paypal.annotation.PaypalEvent;
import com.lx.pl.pay.paypal.enums.PaypalSubscriptonStatusEnum;
import com.lx.pl.pay.paypal.model.PaypalSubscriptionModel;
import com.lx.pl.pay.paypal.model.domain.PayPalLogicSubscription;
import com.lx.pl.pay.paypal.model.event.PaypalSubscriptionEvent;
import com.lx.pl.pay.paypal.service.IPaypalEventHandler;
import org.redisson.api.RLock;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * BILLING.SUBSCRIPTION.CREATED	订阅创建成功（用户完成订阅购买）
 * BILLING.SUBSCRIPTION.ACTIVATED	订阅被激活（可能是试用期开始）
 * BILLING.SUBSCRIPTION.UPDATED	订阅信息更新（例如用户升级/降级计划）
 */
@Component
@PaypalEvent(eventType = {"BILLING.SUBSCRIPTION.CREATED", "BILLING.SUBSCRIPTION.ACTIVATED", "BILLING.SUBSCRIPTION.UPDATED"})
public class PaypalSubscriptionHandler extends IPaypalEventHandler<PaypalSubscriptionEvent> {
    @Override
    public void handleEvent(PaypalSubscriptionEvent data) {
        PaypalSubscriptionModel subscription = data.getModel();
//        String customId = subscription.getCustomId();
//        JSONObject entries = JSONUtil.parseObj(customId);
//
//        RLock lock = redissonClient.getLock(PAYPAL_USER_LOCK_PREFIX + (customId == null ? subscription.getId() : entries.getStr("userId")));
        RLock lock = redissonClient.getLock(PAYPAL_ACTION_LOCK_PREFIX + subscription.getId());
        log.info("lock key {}" ,PAYPAL_ACTION_LOCK_PREFIX + subscription.getId());
        String loginName = null;
        try {
            lock.lock();
            loginName = applicationContext.getBean(PaypalSubscriptionHandler.class).doHandleEvent(data);
            if (StrUtil.isNotBlank(loginName)) {
                vipService.resettingPersonalLumens(loginName);
            }
        } finally {
            unlockAndRefreshVip(lock, loginName);
        }
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public String doHandleEvent(PaypalSubscriptionEvent data) {
        String eventType = data.getEventType();
        switch (eventType) {
            case "BILLING.SUBSCRIPTION.CREATED":
                String customId = data.getModel().getCustomId();
                JSONObject entries = JSONUtil.parseObj(customId);
                // 创建
                PayPalLogicSubscription subscription = payPalLogicSubscriptionService.createSubscription(data.getModel(), entries.getStr("userId"));
                if (subscription != null) {
                    return subscription.getLoginName();
                }
                break;
            case "BILLING.SUBSCRIPTION.ACTIVATED":
                // 激活
                String subscriptionId = data.getModel().getId();
                log.info("subscriptionId: {} 激活", subscriptionId);
                if (subscriptionId == null) {
                    log.info("subscriptionId: {} 不存在", subscriptionId);
                    return null;
                }
                // boolean
                PaypalSubscriptionModel subscriptionModel = payPalLogicSubscriptionService.queryPaypalSubDetail(subscriptionId);
                if (subscriptionModel == null || !subscriptionModel.getStatus().equals(PaypalSubscriptonStatusEnum.ACTIVE.getStatus())) {
                    log.info("subscriptionId: {} 状态异常 {}", subscriptionId, subscriptionModel);
                    return null;
                }
                log.info("subscriptionModel query result: {} 激活", subscriptionModel.toJSON());
                PayPalLogicSubscription payPalLogicSubscription = payPalLogicSubscriptionService.activateSubscription(subscriptionModel, null);
                if (payPalLogicSubscription != null) {
                    return payPalLogicSubscription.getLoginName();
                }
                break;
            case "BILLING.SUBSCRIPTION.UPDATED":
                // 更新
                PayPalLogicSubscription updateSubscription = payPalLogicSubscriptionService.updateSubscription(data.getModel());
                if (updateSubscription != null) {
                    return updateSubscription.getLoginName();
                }
                break;
            default:
                break;
        }
        return null;
    }

}
