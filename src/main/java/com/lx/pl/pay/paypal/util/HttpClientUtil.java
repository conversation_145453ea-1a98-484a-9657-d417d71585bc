//package com.lx.pl.pay.paypal.util;
//
//
//import cn.hutool.core.collection.CollUtil;
//import org.apache.commons.lang3.StringUtils;
//import org.apache.hc.client5.http.ClientProtocolException;
//import org.apache.hc.client5.http.classic.methods.HttpGet;
//import org.apache.hc.client5.http.classic.methods.HttpPost;
//import org.apache.hc.client5.http.config.RequestConfig;
//import org.apache.hc.client5.http.entity.UrlEncodedFormEntity;
//import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
//import org.apache.hc.client5.http.impl.classic.HttpClients;
//import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager;
//import org.apache.hc.client5.http.socket.ConnectionSocketFactory;
//import org.apache.hc.client5.http.socket.PlainConnectionSocketFactory;
//import org.apache.hc.client5.http.ssl.NoopHostnameVerifier;
//import org.apache.hc.client5.http.ssl.SSLConnectionSocketFactory;
//import org.apache.hc.core5.http.*;
//import org.apache.hc.core5.http.config.Registry;
//import org.apache.hc.core5.http.config.RegistryBuilder;
//import org.apache.hc.core5.http.io.HttpClientResponseHandler;
//import org.apache.hc.core5.http.io.entity.EntityUtils;
//import org.apache.hc.core5.http.io.entity.StringEntity;
//import org.apache.hc.core5.http.message.BasicNameValuePair;
//import org.apache.hc.core5.net.URIBuilder;
//import org.apache.hc.core5.util.Timeout;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//import javax.net.ssl.SSLContext;
//import javax.net.ssl.TrustManager;
//import javax.net.ssl.X509TrustManager;
//import java.io.IOException;
//import java.net.URI;
//import java.net.URISyntaxException;
//import java.nio.charset.StandardCharsets;
//import java.security.KeyManagementException;
//import java.security.NoSuchAlgorithmException;
//import java.security.SecureRandom;
//import java.security.cert.X509Certificate;
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Map;
//
///**
// * <AUTHOR> Http请求封装工具类(使用Http5.X版本)
// * 支持HTTP/2、新的异步HTTP接口、重构reactor io模式，改进基于reactor 的NIO、
// * 不论服务端是阻塞还是异步的实现，httpclient5均能支持服务端的过滤。例如横切协议（cross-cutting protocol）的握手，和用户认证授权
// * 支持reactive流的API、
// * @version 1.0
// * @date 2023/9/11 11:32
// */
//public class HttpClientUtil {
//
//    public static final Logger logger = LoggerFactory.getLogger("paypal-msg");
//
//    /**
//     * 总最大连接数
//     */
//    private static final int MAX_TOTAL_CONNECTIONS = 600;
//    /**
//     * 每个http主机的最大连接数
//     */
//    private static final int MAX_CONNECTIONS_PER_ROUTE = 300;
//    /**
//     * 请求超时时间（以毫秒为单位）
//     */
//    private static final int CONNECTION_REQUEST_TIMEOUT = 10000;
//    /**
//     * 等待数据的超时时间(就是请求超时时间)（以毫秒为单位）
//     */
//    private static final int RESPONSE_TIMEOUT = 10000;
//    /**
//     * 空闲连接清理间隔(以毫秒为单位)
//     */
//    private static final int IDLE_CONNECTION_CLEAR_INTERVAL = 30000;
//
//    //客户端 http请求对象
//    private static CloseableHttpClient httpClient;
//    //http连接池配置
//    private static PoolingHttpClientConnectionManager connectionManager;
//    //请求配置
//    private static RequestConfig requestConfig;
//
//    static {
//        //注册访问协议相关的Socket工厂
//        Registry<ConnectionSocketFactory> registry = RegistryBuilder.<ConnectionSocketFactory>create()
//                .register("http", PlainConnectionSocketFactory.INSTANCE)
//                .register("https", trustHttpsCertificates())
//                .build();
//        connectionManager = new PoolingHttpClientConnectionManager(registry);
//        connectionManager.setMaxTotal(MAX_TOTAL_CONNECTIONS);
//        connectionManager.setDefaultMaxPerRoute(MAX_CONNECTIONS_PER_ROUTE);
//        requestConfig = RequestConfig.custom()
//                .setConnectionRequestTimeout(Timeout.ofMilliseconds(CONNECTION_REQUEST_TIMEOUT))
//                .setResponseTimeout(Timeout.ofMilliseconds(RESPONSE_TIMEOUT))
//                .build();
//        httpClient = HttpClients.custom()
//                .setConnectionManager(connectionManager)
//                .setDefaultRequestConfig(requestConfig)
//                //每隔多久清理一次空闲连接,正在使用的连接不会被清理
//                .evictIdleConnections(Timeout.ofMilliseconds(IDLE_CONNECTION_CLEAR_INTERVAL))
//                .build();
//    }
//
//    /**
//     * Https证书管理
//     *
//     * @return 可识别证书集合
//     */
//    private static ConnectionSocketFactory trustHttpsCertificates() {
//        TrustManager[] trustAllCertificates = new TrustManager[]{
//                new X509TrustManager() {
//                    @Override
//                    public void checkClientTrusted(X509Certificate[] x509Certificates, String s) {
//                    }
//
//                    @Override
//                    public void checkServerTrusted(X509Certificate[] x509Certificates, String s) {
//                    }
//
//                    @Override
//                    public X509Certificate[] getAcceptedIssuers() {
//                        return new X509Certificate[0];
//                    }
//                }
//        };
//        SSLContext sslContext = null;
//        try {
//            sslContext = SSLContext.getInstance("TLS");
//            sslContext.init(null, trustAllCertificates, new SecureRandom());
//        } catch (NoSuchAlgorithmException e) {
//            e.printStackTrace();
//        } catch (KeyManagementException e) {
//            e.printStackTrace();
//        }
//        return new SSLConnectionSocketFactory(sslContext, NoopHostnameVerifier.INSTANCE);
//    }
//
//    /**
//     * 重写response handler,确保客户端自动释放资源
//     *
//     * @return
//     */
//    private static HttpClientResponseHandler<String> getResponseHandler() {
//        return new HttpClientResponseHandler<String>() {
//            @Override
//            public String handleResponse(ClassicHttpResponse response) throws IOException {
//                int status = response.getCode();
//                HttpEntity entity = response.getEntity();
//                String responseBodyStr = null;
//                try {
//                    responseBodyStr = entity != null ? EntityUtils.toString(entity) : null;
//                } catch (ParseException e) {
//                    throw new ClientProtocolException("Http entity parse exception: " + e);
//                }
//                if (status >= HttpStatus.SC_SUCCESS && status < HttpStatus.SC_REDIRECTION) {
//                    return responseBodyStr;
//                } else {
//                    throw new ClientProtocolException("Unexpected response status: " + status + "Response body: " + responseBodyStr);
//                }
//            }
//        };
//    }
//
//    /**
//     * Get请求
//     *
//     * @param url     请求地址
//     * @param headers 请求头
//     * @param params  请求参数
//     * @return
//     */
//    public static String get(String url, Map<String, Object> headers, Map<String, Object> params) throws PaymentCoreException {
//        logger.info("Httpclient5 GET request url={} headers={} params={}", url, headers, params);
//        String responseStr = null;
//        HttpGet httpGet = new HttpGet(url);
//        //设置header
//        if (headers != null) {
//            for (Map.Entry<String, Object> entry : headers.entrySet()) {
//                httpGet.addHeader(entry.getKey(), entry.getValue());
//            }
//        }
//        if (params != null && params.size() > 0) {
//            // 表单参数
//            List<NameValuePair> nvps = new ArrayList<>();
//            // GET 请求参数，如果中文出现乱码需要加上URLEncoder.encode
//            for (Map.Entry<String, Object> entry : params.entrySet()) {
//                nvps.add(new BasicNameValuePair(entry.getKey(), String.valueOf(entry.getValue())));
//            }
//            // 增加到请求 URL 中
//            try {
//                URI uri = new URIBuilder(new URI(url)).addParameters(nvps).build();
//                httpGet.setUri(uri);
//            } catch (URISyntaxException e) {
//                throw new PaymentCoreException("URISyntaxException: " + e.getMessage());
//            }
//        }
//        try {
//            responseStr = httpClient.execute(httpGet, getResponseHandler());
//        } catch (IOException e) {
//            throw new PaymentCoreException("IOException url=: " + url + ">>>exception= " + e.getMessage());
//        }
//        logger.info("Httpclient5 GET response: {}", responseStr);
//        return responseStr;
//    }
//
//    /**
//     * Post请求
//     *
//     * @param url     请求地址
//     * @param headers 请求头
//     * @param body    请求参数（JSON字符串）
//     * @return
//     */
//    public static String post(String url, Map<String, Object> headers, String body) throws PaymentCoreException {
//        logger.info("Httpclient5 POST request url={} headers={} params={}", url, headers, body);
//        String responseStr = null;
//        HttpPost httpPost = new HttpPost(url);
//        //设置header
//        if (headers != null) {
//            for (Map.Entry<String, Object> entry : headers.entrySet()) {
//                httpPost.addHeader(entry.getKey(), entry.getValue());
//            }
//        }
//        //设置请求参数
//        if (!StringUtils.isEmpty(body)) {
//            StringEntity entity = new StringEntity(body, StandardCharsets.UTF_8);
//            httpPost.setEntity(entity);
//        }
//        //执行请求
//        try {
//            responseStr = httpClient.execute(httpPost, getResponseHandler());
//        } catch (IOException e) {
//            throw new PaymentCoreException("IOException url=: " + url + ">>>exception= " + e.getMessage());
//        }
//        logger.info("Httpclient5 POST response: {}", responseStr);
//        return responseStr;
//    }
//
//    /**
//     * Post请求，无请求头
//     *
//     * @param url    请求地址
//     * @param params 请求参数
//     * @return
//     */
//    public static String post(String url, Map<String, Object> params) throws PaymentCoreException {
//        logger.info("Httpclient5 POST params request url={} params={}", url, params);
//        String responseStr = null;
//        HttpPost httpPost = new HttpPost(url);
//        httpPost.setEntity(getFormEntity(params));
//        try {
//            responseStr = httpClient.execute(httpPost, getResponseHandler());
//        } catch (IOException e) {
//            throw new PaymentCoreException("IOException url=: " + url + ">>>exception= " + e.getMessage());
//        }
//        logger.info("Httpclient5 POST params response: {}", responseStr);
//        return responseStr;
//    }
//
//    /**
//     * OAuth2.0授权验证请求
//     *
//     * @param url              请求地址
//     * @param authorizationStr 请求认证签名
//     * @return
//     */
//    public static String postOAuth2(String url, String authorizationStr) throws PaymentCoreException {
//        logger.info("Httpclient5 POST oAuth request url={} authorizationStr={}", url, authorizationStr);
//        String responseStr = null;
//        HttpPost httpPost = new HttpPost(url);
//        httpPost.setHeader("Authorization", "Basic " + authorizationStr);
//        httpPost.setEntity(new StringEntity("grant_type=client_credentials", ContentType.APPLICATION_FORM_URLENCODED));
//        try {
//            responseStr = httpClient.execute(httpPost, getResponseHandler());
//        } catch (IOException e) {
//            throw new PaymentCoreException("IOException url=: " + url + ">>>exception= " + e.getMessage());
//        }
//        logger.info("Httpclient5 POST oAuth response: {}", responseStr);
//        return responseStr;
//    }
//
//    /**
//     * 封装Post请求的form表单
//     *
//     * @param paramMap
//     * @return UrlEncodedFormEntity
//     */
//    private static UrlEncodedFormEntity getFormEntity(Map<String, Object> paramMap) {
//        //参数定义
//        List<NameValuePair> params = new ArrayList<>();
//        if (!CollUtil.isEmpty(paramMap)) {
//            paramMap.forEach((k, v) -> {
//                params.add(new BasicNameValuePair(k, String.valueOf(v)));
//            });
//            //创建提交的表单对象
//            return new UrlEncodedFormEntity(params, StandardCharsets.UTF_8);
//        }
//        return new UrlEncodedFormEntity(params);
//    }
//
//    /**
//     * 表单请求
//     *
//     * @param url    请求地址
//     * @param params 请求参数
//     * @return
//     */
//    public static String postForm(String url, Map<String, Object> params) throws PaymentCoreException {
//        logger.info("Httpclient5 POST form request url={} params={}", url, params);
//        String responseStr = null;
//        HttpPost httpPost = new HttpPost(url);
//        httpPost.setHeader("Content-Type", "application/x-www-form-urlencoded");
//        httpPost.setEntity(getFormEntity(params));
//        try {
//            responseStr = httpClient.execute(httpPost, getResponseHandler());
//        } catch (IOException e) {
//            throw new PaymentCoreException("IOException url=: " + url + ">>>exception= " + e.getMessage());
//        }
//        logger.info("Httpclient5 POST form response: {}", responseStr);
//        return responseStr;
//    }
//}
