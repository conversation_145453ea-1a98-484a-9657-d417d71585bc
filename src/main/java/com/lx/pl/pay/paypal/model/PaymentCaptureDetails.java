package com.lx.pl.pay.paypal.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.paypal.base.rest.*;
import lombok.*;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PaymentCaptureDetails extends CustomPaypalResource {

    private String id;
    private Amount amount;
    private Boolean finalCapture;
    private SellerProtection sellerProtection;
    private SellerReceivableBreakdown sellerReceivableBreakdown;
    private String customId;
    private String status;
    private SupplementaryData supplementaryData;
    private Payee payee;
    private String createTime;
    private String updateTime;
    private List<Link> links;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Amount extends PayPalResource {
        private String currencyCode;
        private String value;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class SellerProtection extends PayPalResource {
        private String status;
        private List<String> disputeCategories;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class SellerReceivableBreakdown extends PayPalResource {
        private GrossAmount grossAmount;
        private PayPalFee paypalFee;
        private NetAmount netAmount;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class GrossAmount extends PayPalResource {
        private String currencyCode;
        private String value;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class PayPalFee extends PayPalResource {
        private String currencyCode;
        private String value;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class NetAmount extends PayPalResource {
        private String currencyCode;
        private String value;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class SupplementaryData extends PayPalResource {
        private RelatedIds relatedIds;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class RelatedIds extends PayPalResource {
        // 如果需要扩展相关 ID 的字段，可以在这里添加
        private String orderId;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Payee extends PayPalResource {
        private String emailAddress;
        private String merchantId;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Link extends PayPalResource {
        private String href;
        private String rel;
        private String method;
    }

    /**
     * /v2/payments/captures/8E245489BE455314R
     */
    public static PaymentCaptureDetails detail(APIContext apiContext, String paymentId) throws PayPalRESTException {
        String pattern = "v2/payments/captures/{0}";

        Object[] parameters = new Object[]{paymentId};
        String resourcePath = RESTUtil.formatURIPath(pattern, parameters);
        return executeWithRetry(apiContext, HttpMethod.GET, resourcePath, "", PaymentCaptureDetails.class);
    }
}