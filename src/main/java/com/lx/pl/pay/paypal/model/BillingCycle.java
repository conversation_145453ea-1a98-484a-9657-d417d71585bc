package com.lx.pl.pay.paypal.model;

import com.paypal.base.rest.PayPalResource;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
public class BillingCycle extends PayPalResource {
    private Frequency frequency;
    private String tenureType;
    private int sequence;
    private int totalCycles;
    private PricingScheme pricingScheme;

    // Getters and setters
}