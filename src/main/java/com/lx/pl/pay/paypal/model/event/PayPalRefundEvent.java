package com.lx.pl.pay.paypal.model.event;

import com.lx.pl.pay.paypal.model.PaypalCheckoutOrderModel;
import com.lx.pl.pay.paypal.model.PaypalRefundModel;
import com.paypal.api.payments.Refund;
import com.paypal.base.rest.JSONFormatter;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class PayPalRefundEvent extends PaypalEventModel {

    private PaypalRefundModel model;

    public PaypalRefundModel getModel() {
        if (model == null) {
            model = JSONFormatter.fromJSON(this.getResource(), PaypalRefundModel.class);
        }
        return model;
    }
}
