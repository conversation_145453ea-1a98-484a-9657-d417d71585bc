package com.lx.pl.pay.paypal.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lx.pl.pay.paypal.mapper.PayPalPlanMapper;
import com.lx.pl.pay.paypal.model.domain.PayPalPlan;
import com.lx.pl.pay.paypal.service.PayPalPlanService;
import org.springframework.stereotype.Service;

@Service
public class PayPalPlanServiceImpl extends ServiceImpl<PayPalPlanMapper, PayPalPlan> implements PayPalPlanService {
    @Override
    public PayPalPlan queryByPlanId(String planId) {
        PayPalPlan payPalPlan = this.lambdaQuery().eq(PayPalPlan::getPlanId, planId).one();
        return payPalPlan;
    }

    @Override
    public boolean isUpgrade(String planId, String newPlanId) {
        //升级判断
        return false;
    }
}