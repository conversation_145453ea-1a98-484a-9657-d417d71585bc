package com.lx.pl.pay.paypal.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lx.pl.pay.paypal.mapper.PayPalOrderPaymentItemMapper;
import com.lx.pl.pay.paypal.model.domain.PayPalOrderPaymentItem;
import com.lx.pl.pay.paypal.service.PayPalOrderPaymentItemService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * PayPal订单支付记录item服务实现类
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Service
public class PayPalOrderPaymentItemServiceImpl extends ServiceImpl<PayPalOrderPaymentItemMapper, PayPalOrderPaymentItem> 
        implements PayPalOrderPaymentItemService {

    private static final Logger logger = LoggerFactory.getLogger(PayPalOrderPaymentItemServiceImpl.class);

    @Override
    public List<PayPalOrderPaymentItem> findByOrderId(String orderId) {
        logger.debug("Finding PayPal order payment items by orderId: {}", orderId);
        return this.lambdaQuery()
                .eq(PayPalOrderPaymentItem::getOrderId, orderId)
                .list();
    }

    @Override
    public List<PayPalOrderPaymentItem> findByUserId(Long userId) {
        logger.debug("Finding PayPal order payment items by userId: {}", userId);
        return this.lambdaQuery()
                .eq(PayPalOrderPaymentItem::getUserId, userId)
                .orderByDesc(PayPalOrderPaymentItem::getCreateTime)
                .list();
    }

    @Override
    public List<PayPalOrderPaymentItem> findByLoginName(String loginName) {
        logger.debug("Finding PayPal order payment items by loginName: {}", loginName);
        return this.lambdaQuery()
                .eq(PayPalOrderPaymentItem::getLoginName, loginName)
                .orderByDesc(PayPalOrderPaymentItem::getCreateTime)
                .list();
    }

    @Override
    public List<PayPalOrderPaymentItem> findByProductId(String productId) {
        logger.debug("Finding PayPal order payment items by productId: {}", productId);
        return this.lambdaQuery()
                .eq(PayPalOrderPaymentItem::getProductId, productId)
                .orderByDesc(PayPalOrderPaymentItem::getCreateTime)
                .list();
    }

    @Override
    public List<PayPalOrderPaymentItem> findByStatus(String status) {
        logger.debug("Finding PayPal order payment items by status: {}", status);
        return this.lambdaQuery()
                .eq(PayPalOrderPaymentItem::getStatus, status)
                .orderByDesc(PayPalOrderPaymentItem::getCreateTime)
                .list();
    }

    @Override
    public boolean updateStatusByPaymentId(String paymentId, String status) {
        logger.info("Updating PayPal order payment item status by paymentId: {}, new status: {}", paymentId, status);
        return this.lambdaUpdate()
                .eq(PayPalOrderPaymentItem::getOrderId, paymentId)
                .set(PayPalOrderPaymentItem::getStatus, status)
                .update();
    }
}
