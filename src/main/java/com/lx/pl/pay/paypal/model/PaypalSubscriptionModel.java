package com.lx.pl.pay.paypal.model;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.lx.pl.pay.paypal.model.domain.PayPalLogicSubscription;
import com.paypal.api.payments.Links;
import com.paypal.base.rest.APIContext;
import com.paypal.base.rest.HttpMethod;
import com.paypal.base.rest.PayPalRESTException;
import com.paypal.base.rest.RESTUtil;
import lombok.*;

import java.io.UnsupportedEncodingException;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLDecoder;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PaypalSubscriptionModel extends CustomPaypalResource {
    private String id;

    private String statusChangeNote;
    /**
     * @see com.lx.pl.pay.paypal.enums.PaypalSubscriptonStatusEnum
     */
    private String status;

    private String planId; // 订阅计划 ID

    private String customId;

    /**
     * Default: "Current time"
     * The date and time when the subscription started, in Internet date and time format.
     */
    private String startTime; // 订阅开始时间

    private Integer quantity;

    private Subscriber subscriber; // 订阅者信息

    private BillingInfo billingInfo;

    private ApplicationContext applicationContext; // 应用上下文

    private List<Links> links;

    private String token;

    private String statusUpdateTime;

    private PayPalPlanModel plan;

    private String reason;

    // Getters and Setters

//    public static PaypalSubscriptionModel cancel(APIContext apiContext, PaypalSubscriptionModel subscriptionModel) throws PayPalRESTException {
//        if (subscriptionModel.getId() == null) {
//            throw new IllegalArgumentException("Subscription ID cannot be null");
//        }
//        if (subscriptionModel.getReason() == null) {
//            throw new IllegalArgumentException("reason cannot be null");
//        }
//        String pattern = "v1/billing/subscriptions/{0}/cancel";
//        Object[] parameters = new Object[]{subscriptionModel.getId()};
//        String resourcePath = RESTUtil.formatURIPath(pattern, parameters);
//        PaypalSubscriptionModel paypalSubscriptionModel = configureAndExecute(apiContext, HttpMethod.POST, resourcePath, subscriptionModel.toJSON(), PaypalSubscriptionModel.class);
//        return paypalSubscriptionModel;
//    }

    public static PaypalSubscriptionModel create(APIContext apiContext, PaypalSubscriptionModel subscription) throws PayPalRESTException, MalformedURLException, UnsupportedEncodingException {
        String pattern = "v1/billing/subscriptions";
//        String resourcePath = RESTUtil.formatURIPath(pattern, parameters);
        PaypalSubscriptionModel paypalSubscriptionModel = executeWithRetry(apiContext, HttpMethod.POST, pattern, subscription.toJSON(), PaypalSubscriptionModel.class);
        for (Links links : paypalSubscriptionModel.getLinks()) {
            if ("approval_url".equals(links.getRel())) {
                URL url = new URL(links.getHref());
                paypalSubscriptionModel.setToken(splitQuery(url).get("token"));
                break;
            }
        }
        return paypalSubscriptionModel;
    }

    public static PaypalSubscriptionModel detail(APIContext apiContext, String subscriptionId) throws PayPalRESTException {
        String pattern = "v1/billing/subscriptions/{0}";

        Object[] parameters = new Object[]{subscriptionId};
        String resourcePath = RESTUtil.formatURIPath(pattern, parameters);
        return executeWithRetry(apiContext, HttpMethod.GET, resourcePath, "", PaypalSubscriptionModel.class);
    }


    public static PaypalSubscriptionModel revise(APIContext apiContext, PaypalSubscriptionModel subscriptionModel) throws PayPalRESTException {
        String pattern = "v1/billing/subscriptions/{0}/revise";

        Object[] parameters = new Object[]{subscriptionModel.getId()};
        String resourcePath = RESTUtil.formatURIPath(pattern, parameters);
        return executeWithRetry(apiContext, HttpMethod.POST, resourcePath, subscriptionModel.toJSON(), PaypalSubscriptionModel.class);
    }

    public static PaypalSubscriptionModel cancel(APIContext apiContext, PaypalSubscriptionModel subscriptionModel) throws PayPalRESTException {

        String pattern = "v1/billing/subscriptions/{0}/cancel";
        Object[] parameters = new Object[]{subscriptionModel.getId()};
        String resourcePath = RESTUtil.formatURIPath(pattern, parameters);
        return executeWithRetry(apiContext, HttpMethod.POST, resourcePath, subscriptionModel.toJSON(), PaypalSubscriptionModel.class);
    }


//    public static PaypalSubscriptionModel update(APIContext apiContext, String[] payload, String subscriptionId) throws PayPalRESTException {
//        String pattern = "v1/billing/subscriptions/{0}";
//        Object[] parameters = new Object[]{subscriptionId};
//        String resourcePath = RESTUtil.formatURIPath(pattern, parameters);
//        System.out.println(JSONFormatter.toJSON(payload));
//        PaypalSubscriptionModel paypalSubscriptionModel = configureAndExecute(apiContext, HttpMethod.PATCH, resourcePath, "[{\"op\":\"replace\",\"path\":\"/start_time\",\"value\":\"2025-04-14T12:33:37Z\"}]", PaypalSubscriptionModel.class);
//        return paypalSubscriptionModel;
//    }

    private static Map<String, String> splitQuery(URL url) throws UnsupportedEncodingException {
        Map<String, String> queryPairs = new HashMap<String, String>();
        String query = url.getQuery();
        String[] pairs = query.split("&");
        for (String pair : pairs) {
            int idx = pair.indexOf("=");
            queryPairs.put(URLDecoder.decode(pair.substring(0, idx), "UTF-8"), URLDecoder.decode(pair.substring(idx + 1), "UTF-8"));
        }
        return queryPairs;
    }

    public PayPalLogicSubscription convert() {
        PayPalLogicSubscription payPalLogicSubscription = new PayPalLogicSubscription();
        payPalLogicSubscription = new PayPalLogicSubscription();
        payPalLogicSubscription.setCreateTime(LocalDateTime.now());
        payPalLogicSubscription.setSubscriptionId(this.getId());
        if (customId != null) {
            JSONObject entries = JSONUtil.parseObj(customId);
            payPalLogicSubscription.setUserId(Long.valueOf(entries.getStr("userId")));
        }

//        payPalLogicSubscription.setLoginName(user.getLoginName());
        payPalLogicSubscription.setPlanId(this.getPlanId());
        payPalLogicSubscription.setStatus(this.getStatus());
        payPalLogicSubscription.setStartTime(this.getStartTime());
        payPalLogicSubscription.setQuantity(this.getQuantity());
        String statusUpdateTime1 = this.getStatusUpdateTime();
        if (statusUpdateTime1 != null) {
            payPalLogicSubscription.setStatusUpdateTimeSec(Instant.parse(statusUpdateTime1).getEpochSecond());
        }
        // UTC 2025-02-21T06:29:48Z 转时间戳秒
        String startTime1 = this.getStartTime();
        if (startTime1 != null) {
            payPalLogicSubscription.setStartUtcSec(Instant.parse(startTime1).getEpochSecond());
        }
        payPalLogicSubscription.setSubStartSec(Instant.now().getEpochSecond());


        Subscriber subscriber = this.getSubscriber();
        if (subscriber != null) {
            payPalLogicSubscription.setSubscriberPayerId(subscriber.getPayerId());
            payPalLogicSubscription.setSubscriberEmail(subscriber.getEmailAddress());
        }
        payPalLogicSubscription.setInvalid(false);
        BillingInfo billingInfo = this.getBillingInfo();
        if (billingInfo != null) {
            payPalLogicSubscription.setNextBillingTime(billingInfo.getNextBillingTime());
            String nextBillingTime = billingInfo.getNextBillingTime();
            if (nextBillingTime != null) {
                payPalLogicSubscription.setNextBillingSec(Instant.parse(nextBillingTime).getEpochSecond());
            }

            BillingInfo.LastPayment lastPayment = billingInfo.getLastPayment();
            if (lastPayment != null) {
                payPalLogicSubscription.setLastPaymentTime(lastPayment.getTime());
                BillingInfo.Amount amount = lastPayment.getAmount();
                if (amount != null) {
                    payPalLogicSubscription.setLastPaymentValue(amount.getValue());
                }
            }
            BillingInfo.OutstandingBalance outstandingBalance = billingInfo.getOutstandingBalance();
            if (outstandingBalance != null) {
                payPalLogicSubscription.setOutstandingValue(outstandingBalance.getValue());
            }
        }
        return payPalLogicSubscription;
    }
}

