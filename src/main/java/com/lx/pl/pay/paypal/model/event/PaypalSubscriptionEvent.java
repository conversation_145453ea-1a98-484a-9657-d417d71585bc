package com.lx.pl.pay.paypal.model.event;

import com.lx.pl.pay.paypal.model.PaypalSubscriptionModel;
import com.paypal.base.rest.JSONFormatter;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class PaypalSubscriptionEvent extends PaypalEventModel {

    private PaypalSubscriptionModel model;

    public PaypalSubscriptionModel getModel() {
        if (model == null) {
            model = JSONFormatter.fromJSON(this.getResource(), PaypalSubscriptionModel.class);
        }
        return model;
    }
}
