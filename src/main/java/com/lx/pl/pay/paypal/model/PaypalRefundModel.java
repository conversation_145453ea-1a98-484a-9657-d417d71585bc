package com.lx.pl.pay.paypal.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.lx.pl.pay.paypal.model.domain.PayPalRefundRecord;
import com.paypal.api.payments.Links;
import com.paypal.base.rest.*;
import lombok.*;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@AllArgsConstructor
@NoArgsConstructor
public class PaypalRefundModel extends CustomPaypalResource {
    private String saleId;
    private String description;
    private String custom;

    private PaypalCheckoutOrderModel.Amount amount;
    private String id;
    private String status;
    private StatusDetail statusDetail;
    private String invoiceId;
    private String customId;
    private String acquirerReferenceNumber;
    private String noteToPayer;


    private String createTime;
    private String parentPayment;

    private SellerPayableBreakdown sellerPayableBreakdown;

    private List<Links> links;

//    private RefundFromReceivedAmount refundFromReceivedAmount;
//    private RefundFromTransactionFee refundFromTransactionFee;
//    private TotalRefundedAmount totalRefundedAmount;
//    private RefundToPayer refundToPayer;

    @EqualsAndHashCode(callSuper = true)
    @Data
    @Builder
    @JsonIgnoreProperties(ignoreUnknown = true)
    @AllArgsConstructor
    @NoArgsConstructor
    public static class StatusDetail extends PayPalResource {
        private String reason;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    @Builder
    @JsonIgnoreProperties(ignoreUnknown = true)
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RefundFromReceivedAmount extends PayPalResource {
        private String value;
    }


    @EqualsAndHashCode(callSuper = true)
    @Data
    @Builder
    @JsonIgnoreProperties(ignoreUnknown = true)
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RefundFromTransactionFee extends PayPalResource {
        private String value;
    }


    @EqualsAndHashCode(callSuper = true)
    @Data
    @Builder
    @JsonIgnoreProperties(ignoreUnknown = true)
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TotalRefundedAmount extends PayPalResource {
        private String value;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    @Builder
    @JsonIgnoreProperties(ignoreUnknown = true)
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RefundToPayer extends PayPalResource {
        private String value;
    }


    /**
     * {
     * "amount": {
     * "total": "2.34",
     * "currency": "USD"
     * },
     * "using": "INVOICE_ID",
     * "payer_info": {
     * "email": "<EMAIL>"
     * }
     * }
     *
     * @param apiContext
     */
    public static PaypalRefundModel refund(APIContext apiContext, PaypalRefundModel paypalRefundModel) throws PayPalRESTException {
        String pattern = "v2/payments/captures/{0}/refund";
        
        Object[] parameters = new Object[]{paypalRefundModel.getId()};
        String resourcePath = RESTUtil.formatURIPath(pattern, parameters);
        PaypalRefundModel rtn = executeWithRetry(apiContext, HttpMethod.POST, resourcePath, paypalRefundModel.toJSON(), PaypalRefundModel.class);
        return rtn;
    }

    public PayPalRefundRecord convert() {
        PayPalRefundRecord record = new PayPalRefundRecord();
        record.setRefundId(this.getId());
        record.setStatus(this.getStatus());
        StatusDetail statusDetail1 = this.getStatusDetail();
        if (statusDetail1 != null) {
            record.setRefundReasonCode(statusDetail1.getReason());
        }
        PaypalCheckoutOrderModel.Amount amount1 = this.getAmount();
        if (amount1 != null) {
            record.setCurrency(amount1.getCurrencyCode());
            record.setTotal(amount1.getValue());
        }
        SellerPayableBreakdown sellerPayableBreakdown1 = this.getSellerPayableBreakdown();
        if (sellerPayableBreakdown1 != null) {
            record.setNetAmount(sellerPayableBreakdown1.getNetAmount() == null ? null : sellerPayableBreakdown1.getNetAmount().getValue());
            record.setPaypalFee(sellerPayableBreakdown1.getPaypalFee() == null ? null : sellerPayableBreakdown1.getPaypalFee().getValue());
            record.setGrossAmount(sellerPayableBreakdown1.getGrossAmount() == null ? null : sellerPayableBreakdown1.getGrossAmount().getValue());
            record.setTotalRefundedAmount(sellerPayableBreakdown1.getTotalRefundedAmount() == null ? null : sellerPayableBreakdown1.getTotalRefundedAmount().getValue());
        }
        return record;
    }
}