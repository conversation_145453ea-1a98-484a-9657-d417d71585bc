package com.lx.pl.pay.paypal.model.event;

import com.lx.pl.pay.paypal.model.PaymentCaptureDetails;
import com.paypal.base.rest.JSONFormatter;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class PaypalPaymentCaptureEvent extends PaypalEventModel {

    private PaymentCaptureDetails model;

    public PaymentCaptureDetails getModel() {
        if (model == null) {
            model = JSONFormatter.fromJSON(this.getResource(), PaymentCaptureDetails.class);
        }
        return model;
    }
}
