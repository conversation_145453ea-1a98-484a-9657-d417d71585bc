package com.lx.pl.pay.paypal.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lx.pl.pay.paypal.mapper.PayPalSubReviseLogMapper;
import com.lx.pl.pay.paypal.model.domain.PayPalSubReviseLog;
import com.lx.pl.pay.paypal.service.PayPalSubReviseLogService;
import org.springframework.stereotype.Service;

@Service
public class PayPalSubReviseLogServiceImpl extends ServiceImpl<PayPalSubReviseLogMapper, PayPalSubReviseLog> implements PayPalSubReviseLogService {
    @Override
    public PayPalSubReviseLog queryNextActiveSubscription(String subscriptionId, String type, Long userId, String planId) {
        return this.lambdaQuery()
                .eq(PayPalSubReviseLog::getSubscriptionId, subscriptionId)
                .eq(type != null, PayPalSubReviseLog::getType, type)
                .eq(PayPalSubReviseLog::getSrcPlanId, planId)
                .eq(PayPalSubReviseLog::getUserId, userId)
                .ge(PayPalSubReviseLog::getNextBillingTimeSec, System.currentTimeMillis() / 1000)
                .one();
    }
}