package com.lx.pl.pay.paypal.model;

import com.paypal.base.rest.APIContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.UUID;

public class CustomerApiContext extends APIContext {

    Logger log = LoggerFactory.getLogger("paypal-pay-msg");


    public CustomerApiContext(String clientID, String clientSecret, String mode) {
        super(clientID, clientSecret, mode);
    }

    @Override
    public String getRequestId() {
        UUID uuid = UUID.randomUUID();
        log.info("PayPal-Request-Id: {}", uuid.toString());
        return uuid.toString();
    }
}
