package com.lx.pl.pay.paypal.service;

import com.lx.pl.pay.paypal.config.PayPalBeanConfig;
import com.lx.pl.pay.paypal.model.domain.PaypalEventLog;
import com.lx.pl.pay.paypal.model.event.PaypalEventModel;
import com.lx.pl.pay.paypal.util.HeaderParser;
import com.paypal.base.rest.JSONFormatter;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.PublicKey;
import java.security.Signature;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.util.Base64;
import java.util.Map;
import java.util.zip.CRC32;


@Component
public class PaypalService {
    Logger log = LoggerFactory.getLogger("paypal-pay-msg");
    @Resource
    private PayPalBeanConfig payPalBeanConfig;
    @Resource
    private PaypalEventHandlerContext paypalEventHandlerContext;
    @Resource
    protected PaypalEventLogService paypalEventLogService;
    @Resource
    protected RedissonClient redissonClient;

    @Value("${pay.paypal.cert}")
    private String cacheDir;
    protected static final String PAYPAL_WEBHOOK_LOCK_PREFIX = "paypal:webhook:";


    public void constructEvent(HttpServletRequest request, byte[] payload) throws Exception {
        Map<String, String> headersMap = HeaderParser.parseHeaders(request);
        boolean isVerified = verifySignature(payload, headersMap);
        String eventString = new String(payload, StandardCharsets.UTF_8);
        PaypalEventModel event = JSONFormatter.fromJSON(eventString, PaypalEventModel.class);
        RLock lock = redissonClient.getLock(PAYPAL_WEBHOOK_LOCK_PREFIX + event.getId());
        try {
            lock.lock();
            String webhookId = event.getId();
            event.setVerified(isVerified);
            PaypalEventLog paypalEventLog = saveWebhookEventIfNeed(event);
            if (paypalEventLog == null) {
                log.warn("webhookId: {} already exist", webhookId);
                return;
            }
            if (!isVerified) {
                log.error("Invalid signature");
                throw new Exception("Invalid signature");
            }
            processEvent(event, paypalEventLog);
        } catch (Exception e) {
            log.error("saveWebhookEventIfNeed error: {}", e.getMessage(), e);
            throw new Exception(e);
        } finally {
            if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                lock.unlock();
            }
        }
    }

    private PaypalEventLog saveWebhookEventIfNeed(PaypalEventModel data) {
        return paypalEventLogService.saveWebhookEvent(data);
    }

    private void processEvent(PaypalEventModel event, PaypalEventLog paypalEventLog) {
        String eventType = event.getEventType();
        IPaypalEventHandler<?> handler = paypalEventHandlerContext.getHandler(eventType);
        if (handler == null) {
            log.error("No handler for event type: {} {}", eventType, event.toJSON());
            return;
        }

        handler.doHandle(event, paypalEventLog);
    }

    private boolean verifySignature(byte[] event, Map<String, String> headers) throws Exception {
        String transmissionId = headers.get("paypal-transmission-id");
        String timeStamp = headers.get("paypal-transmission-time");
        String certUrl = headers.get("paypal-cert-url");
        String transmissionSig = headers.get("paypal-transmission-sig");

        // Calculate CRC32 of raw event data
        long crc = calculateCRC32(event);

        // Construct the message to verify
        String message = transmissionId + "|" + timeStamp + "|" + payPalBeanConfig.getWebhookId() + "|" + crc;
        System.out.println("Original signed message: " + message);

        // Download and cache the certificate
        String certPem = downloadAndCache(certUrl);

        // Extract public key from PEM certificate
        PublicKey publicKey = extractPublicKeyFromPem(certPem);

        // Decode the base64-encoded signature
        byte[] signatureBytes = Base64.getDecoder().decode(transmissionSig);

        // Verify the signature
        Signature verifier = Signature.getInstance("SHA256withRSA");
        verifier.initVerify(publicKey);
        verifier.update(message.getBytes(StandardCharsets.UTF_8));
        return verifier.verify(signatureBytes);
    }

    public static long calculateCRC32(byte[] data) {
        CRC32 crc32 = new CRC32();
        crc32.update(data);
        return crc32.getValue();
    }

    private String downloadAndCache(String url) throws IOException {
        Path filePath = Paths.get(cacheDir, url.replaceAll("\\W+", "-"));

        // Check if cached file exists
        if (Files.exists(filePath)) {
            return Files.readString(filePath);
        }

        // Download the file if not cached
        OkHttpClient client = new OkHttpClient();
        Request request = new Request.Builder().url(url).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Failed to download certificate: " + response);
            }
            String certPem = response.body().string();
            Files.writeString(filePath, certPem);
            return certPem;
        }
    }

    private static PublicKey extractPublicKeyFromPem(String pem) throws Exception {
        CertificateFactory certFactory = CertificateFactory.getInstance("X.509");
        ByteArrayInputStream inputStream = new ByteArrayInputStream(pem.getBytes(StandardCharsets.UTF_8));
        X509Certificate cert = (X509Certificate) certFactory.generateCertificate(inputStream);
        return cert.getPublicKey();
    }

}
