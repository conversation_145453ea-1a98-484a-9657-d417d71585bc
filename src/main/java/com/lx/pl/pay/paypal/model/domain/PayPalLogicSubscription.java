package com.lx.pl.pay.paypal.model.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.lx.pl.db.mysql.gen.entity.MyBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("paypal_logic_subscription")
public class PayPalLogicSubscription extends MyBaseEntity {

    private static final long serialVersionUID = -3853114899148041420L;
    private Long id;

    private Long userId;  // 用户ID

    private String loginName;  // 用户登录名

    private String status;  // 订阅状态

    private Integer quantity;

    private String subscriptionId;  // 订阅ID

    private String planId;  // 计划ID

    private String paymentId;  // 计划名称

    private Long startUtcSec;  // 订阅开始时间（UTC秒）

    private Long subStartSec;  // 订阅开始时间（UTC秒）

    private Long nextBillingSec;  // 下次扣款时间（UTC秒）

    private String startTime;  // 订阅开始时间（字符串格式）

    private String nextBillingTime;  // 下次扣款时间（字符串格式）

    private String lastPaymentTime;  // 上次付款时间

    private String lastPaymentValue;  // 上次付款金额

    private String outstandingValue; // 待扣金额

    private Long statusUpdateTimeSec;  // 状态更新时间（UTC秒）

    private String subscriberEmail;  // 订阅者PayPal邮箱

    private String subscriberPayerId;  // 订阅者PayPal付款ID

    private Boolean invalid; // 是否无效

    public PayPalLogicSubscription() {
        setCreateTime(LocalDateTime.now());
    }
}
