package com.lx.pl.pay.paypal.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.csp.sentinel.util.StringUtil;
import com.google.common.collect.Lists;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.db.mysql.gen.entity.VipStandards;
import com.lx.pl.exception.BadRequestException;
import com.lx.pl.exception.PayPalException;
import com.lx.pl.pay.apple.VipPlatform;
import com.lx.pl.pay.common.domain.SubscriptionCurrent;
import com.lx.pl.pay.common.service.SubscriptionCurrentService;
import com.lx.pl.pay.paypal.enums.PaypalSubscriptonStatusEnum;
import com.lx.pl.pay.paypal.model.*;
import com.lx.pl.pay.paypal.model.domain.PayPalLogicSubscription;
import com.lx.pl.pay.paypal.model.domain.PayPalProduct;
import com.lx.pl.pay.paypal.model.domain.PayPalSubReviseLog;
import com.lx.pl.pay.paypal.model.domain.PaypalUpgradeLog;
import com.lx.pl.pay.paypal.model.vo.BuyItemVo;
import com.lx.pl.pay.paypal.model.vo.ProductItem;
import com.lx.pl.pay.paypal.service.*;
import com.lx.pl.pay.stripe.dto.PaymentType;
import com.lx.pl.service.UserService;
import com.lx.pl.service.VipService;
import com.paypal.api.payments.Links;
import com.paypal.base.rest.APIContext;
import com.paypal.base.rest.PayPalRESTException;
import org.apache.commons.lang3.tuple.Triple;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.lx.pl.enums.PayPalErrorCode.*;
import static com.lx.pl.pay.paypal.constant.PayPalConstant.FIRST_BUY_SUB_OFF;
import static com.lx.pl.pay.paypal.constant.PayPalConstant.LUMEN_VIP_OFF;

@Service
public class PayPalPayServiceImpl implements PayPalPayService {

    protected Logger log = LoggerFactory.getLogger("paypal-pay-msg");

    @Resource
    private UserService userService;
    @Resource
    private APIContext apiContext;
    @Resource
    private PayPalProductService payPalProductService;
    @Resource
    private PayPalSubReviseLogServiceImpl payPalSubReviseLogService;
    @Resource
    private SubscriptionCurrentService subscriptionCurrentService;
    @Autowired
    private PaypalUpgradeLogService paypalUpgradeLogService;
    @Resource
    private PayPalLogicSubscriptionService payPalLogicSubscriptionService;
    @Resource
    private PayPalOrderPaymentRecordService payPalOrderPaymentRecordService;
    @Resource
    private VipService vipService;


    @Override
    public Map<String, String> createPayment(BuyItemVo buyItemDto, User user) {

        checkArgument(buyItemDto);
        //目前只支持同时购买一种
//        ProductItem productItem = buyItemDto.getProductItem();
        // 获取支付类型
//        PaymentType paymentType = productItem.getType();
        PaymentType paymentType = buyItemDto.getItems().get(0).getType();
        switch (paymentType) {
            case PLAN:
                return handlePlanPayment(buyItemDto, user);
            case ONE:
                return handleOneTimePayment(buyItemDto, user);
            default:
                log.error("Unsupported payment type: {}", paymentType);
                throw new PayPalException(UNSUPPORTED_PAYMENT_TYPE);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, String> upgradeDowngrade(BuyItemVo buyItemDto, String type, String opType, User user) {

        // 判断是否跨平台支付
        crossPlatformPaymentCheck(user);
        ProductItem productItem = buyItemDto.getItems().get(0);
        //判断Item中的type必须为plan，否者报错
        if (!PaymentType.PLAN.equals(productItem.getType())) {
            log.error("Invalid payment type: {}", productItem.getType());
            throw new PayPalException(UNSUPPORTED_PAYMENT_TYPE);
        }

        // Step 1: 查询客户的激活订阅
        PaypalSubscriptionModel oldSubscription = findActiveSubscription(user);

        if (oldSubscription == null) {
            log.error("No active subscription found for customer: {}", user.getLoginName());
            throw new PayPalException(ACTIVE_SUBSCRIPTION_REQUIRED);
        }
        if (!PaypalSubscriptonStatusEnum.ACTIVE.getStatus().equals(oldSubscription.getStatus())) {
            log.error("Invalid subscription status: {}", oldSubscription.getStatus());
            throw new PayPalException(ACTIVE_SUBSCRIPTION_REQUIRED);
        }

        // 获取订阅项 只支持1个订阅项
        String planId = oldSubscription.getPlanId();

        PayPalProduct oldStripeProduct = payPalProductService.getPaypalProductByPlanId(planId);
        if (oldStripeProduct == null) {
            log.error("No paypal product found for priceId: {}, user:{}", planId, user.getLoginName());
            throw new PayPalException(NO_PAYPAL_PRODUCT_FOUND);
        }
        // Step 2: 校验能否升级订阅
        PayPalProduct newStripeProduct = payPalProductService.getPaypalProductByBuyItemDto(productItem);

        Map<String, Object> stringObjectMap = this.queryValidNotHandleSubscriptions(user);
        if (stringObjectMap != null && !stringObjectMap.isEmpty()) {
            throw new PayPalException(REVISE_SUBSCRIPTION_EXIST);
        }
        switch (type) {
            case "upgrade":
                boolean canUpgrade = canUpgradeSubscription(oldStripeProduct, newStripeProduct, opType);
                if (!canUpgrade) {
                    throw new PayPalException(CANNOT_UPGRADE_SUBSCRIPTION);
                }
                return doPaypalUpgrade(oldSubscription, newStripeProduct, opType, buyItemDto, user);
            case "downgrade":
                boolean canDowngrade = canDowngradeSubscription(oldStripeProduct, newStripeProduct);
                if (!canDowngrade) {
                    throw new PayPalException(CANNOT_DOWNGRADE_SUBSCRIPTION);
                }
                return doPaypalDowngrade(oldSubscription, newStripeProduct, opType, buyItemDto, user);
            default:
                log.error("Invalid upgrade type: {}", type);
                throw new PayPalException(UNKNOWN_UPDATE_TYPE);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelSubscription(User user) {
        PaypalSubscriptionModel oldSubscription = findActiveSubscription(user);
        if (oldSubscription == null) {
            log.error("No active subscription found for user: {}", user.getLoginName());
            throw new PayPalException(ACTIVE_SUBSCRIPTION_REQUIRED);
        }
        String status = oldSubscription.getStatus();
        if (status.equals(PaypalSubscriptonStatusEnum.CANCELLED.getStatus())) {
            subscriptionCurrentService.updateAutoRenewStatus(oldSubscription.getId(), 0);
            return;
        }
        try {
            PaypalSubscriptionModel.cancel(apiContext, oldSubscription);
            subscriptionCurrentService.updateAutoRenewStatus(oldSubscription.getId(), 0);
        } catch (PayPalRESTException e) {
            log.error("Failed to cancel subscription for user: {}", user.getLoginName(), e);
            throw new PayPalException(CANCEL_SUBSCRIPTION_FAILED);
        }
    }

    @Override
    public Map<String, Object> queryValidNotHandleSubscriptions(User user) {
        SubscriptionCurrent sub = subscriptionCurrentService.getValidHighSubscriptionsFromDb(user.getId(), VipPlatform.PAYPAL.getPlatformName());
        Map<String, Object> rtn = new HashMap<>();
        if (sub != null && sub.getAutoRenewStatus() != null && sub.getAutoRenewStatus() == 0) {
            return rtn;
        }
        if (sub == null) {
            return rtn;
        }
        log.info("userId queryValidNotHandleSubscriptions:{}", sub);
        PayPalProduct oldOne = payPalProductService.lambdaQuery().eq(PayPalProduct::getPlanLevel, sub.getPlanLevel())
                .eq(PayPalProduct::getPriceInterval, sub.getPriceInterval())
                .one();
        log.info(" old planLevel {} {}", oldOne.getPlanLevel(), oldOne.getPriceInterval());
//        PayPalSubReviseLog payPalSubReviseLog = null;
        if (sub.getSubscriptionId() != null) {
            try {
//                payPalSubReviseLog = payPalSubReviseLogService.queryNextActiveSubscription(sub.getSubscriptionId(), null, user.getId(), oldOne.getPaypalPlanId());

                PaypalSubscriptionModel detail = PaypalSubscriptionModel.detail(apiContext, sub.getSubscriptionId());
                PayPalLogicSubscription convert = detail.convert();
                String newPlanId = convert.getPlanId();
                if (!newPlanId.equals(oldOne.getPaypalPlanId())) {
                    PayPalProduct paypalProductByPlanId = payPalProductService.getPaypalProductByPlanId(newPlanId);
                    rtn.put("planLevel", paypalProductByPlanId.getPlanLevel());
                    rtn.put("priceInterval", paypalProductByPlanId.getPriceInterval());
                    rtn.put("nextBillingTimeSec", convert.getNextBillingSec());
//                    payPalSubReviseLogService.lambdaUpdate().eq(PayPalSubReviseLog::getId, payPalSubReviseLog.getId())
//                            .set(PayPalSubReviseLog::getStatus, "success")
//                            .update();
                    log.info("queryValidNotHandleSubscriptions future:{}", rtn);
                    return rtn;

                }
            } catch (PayPalRESTException e) {
                throw new PayPalException(CALL_PAYPAL_API_ERROR);
            }
        }
        return rtn;
//        if (payPalSubReviseLog == null) {
//            log.info("No valid revise subscription found for user: {}", user.getLoginName());
//
//        } else {
//
//        }
//        return rtn;
    }

    private Map<String, String> doPaypalDowngrade(PaypalSubscriptionModel oldSubscription, PayPalProduct newStripeProduct, String opType, BuyItemVo buyItemDto, User user) {
        switch (opType) {
            case "immediate":
                throw new PayPalException(CANNOT_DOWNGRADE_SUBSCRIPTION);
            case "next_billing_period":
                // 1. 更新旧的订阅
                String paypalPlanId = newStripeProduct.getPaypalPlanId();
                return updateSubscriptionFuture(oldSubscription, buyItemDto, paypalPlanId, "downgrade", user);
        }
        return null;
    }

    @NotNull
    private Map<String, String> updateSubscriptionFuture(PaypalSubscriptionModel oldSubscription, BuyItemVo buyItemDto, String paypalPlanId, String type, User user) {
        try {
            PaypalSubscriptionModel paypalSubscriptionModel = PaypalSubscriptionModel.builder().id(oldSubscription.getId()).planId(paypalPlanId)
                    .applicationContext(ApplicationContext.builder().returnUrl(buyItemDto.getSuccessUrl()).cancelUrl(buyItemDto.getCancelUrl()).build()).build();
//                            .plan(PayPalPlanModel.builder()
//                            .paymentPreferences(PaymentPreferences
//                                    .builder().setupFeeFailureAction("CANCEL").autoBillOutstanding(true).paymentFailureThreshold(2)
//                                    .setupFee(SetupFee.builder().currencyCode("USD").value("5.22").build()).build()).build())

            PaypalSubscriptionModel revise = PaypalSubscriptionModel.revise(apiContext, paypalSubscriptionModel);
            PayPalSubReviseLog payPalSubReviseLog = new PayPalSubReviseLog();
            payPalSubReviseLog.setSubscriptionId(oldSubscription.getId());
            payPalSubReviseLog.setUserId(user.getId());
            payPalSubReviseLog.setLoginName(user.getLoginName());
            payPalSubReviseLog.setSubscriptionId(oldSubscription.getId());
            payPalSubReviseLog.setSrcPlanId(oldSubscription.getPlanId());
            payPalSubReviseLog.setNewPlanId(paypalPlanId);
            payPalSubReviseLog.setType(type);
            payPalSubReviseLog.setCreateTime(LocalDateTime.now());
            PayPalLogicSubscription convert = oldSubscription.convert();
            Long nextBillingSec = convert.getNextBillingSec();
            payPalSubReviseLog.setNextBillingTimeSec(nextBillingSec);
            payPalSubReviseLogService.save(payPalSubReviseLog);

            String approvalUrl = extractApprovalUrl(revise);
            log.info("PaypalSubscriptionModel revise: {}", revise);

            // 返回结果
            Map<String, String> response = new HashMap<>();
            response.put("approvalUrl", approvalUrl);
            return response;
        } catch (PayPalRESTException e) {
            log.error("Failed to renew subscription", e);
            throw new PayPalException(DOWNGRADE_FAILED);
        }
    }


    private Map<String, String> doPaypalUpgrade(PaypalSubscriptionModel oldSubscription, PayPalProduct newPaypalProduct, String opType, BuyItemVo buyItemDto, User user) {
        switch (opType) {
            case "immediate":
                PayPalLogicSubscription payPalLogicSubscription = payPalLogicSubscriptionService.queryBySubscriptionId(oldSubscription.getId());
                if (payPalLogicSubscription == null || payPalLogicSubscription.getPaymentId() == null) {
                    throw new PayPalException(SUBSCRIPTION_NOT_FOUND);
                }
                
                PaypalSubscriptionModel subscriptionModel = buildSubscriptionModel(newPaypalProduct, user, buyItemDto.getSuccessUrl(), buyItemDto.getCancelUrl());
                log.debug("调用PayPal API创建 Upgrade 订阅，planId: {}", newPaypalProduct.getPaypalPlanId());
                PaypalSubscriptionModel createdSubscription = null;
                try {
                    createdSubscription = PaypalSubscriptionModel.create(apiContext, subscriptionModel);
                } catch (Exception e) {
                    throw new PayPalException(CALL_PAYPAL_API_ERROR);
                }

                String token = createdSubscription.getToken();
                String approvalUrl = extractApprovalUrl(createdSubscription);

                if (approvalUrl == null) {
                    log.error("创建Upgrade订阅失败，缺少token或approval URL。planId: {}", newPaypalProduct.getPaypalPlanId());
                    throw new PayPalException(SUBSCRIPTION_CREATION_FAILED);
                }
                PaypalUpgradeLog log = new PaypalUpgradeLog();
                log.setSrcSubscriptionId(oldSubscription.getId());
                log.setSrcSubStatus(oldSubscription.getStatus());
                log.setUserId(user.getId());
                log.setLoginName(user.getLoginName());
                log.setNewSubscriptionId(createdSubscription.getId());
                log.setNewSubStatus(createdSubscription.getStatus());
                log.setSrcPlanId(oldSubscription.getPlanId());
                log.setNewPlanId(newPaypalProduct.getPaypalPlanId());
                log.setHasRefund(false);
                log.setVipRevert(false);
                log.setRefundId(null);
                log.setRefundAmount(null);
                log.setCreateBy(user.getLoginName());
                log.setCreateTime(LocalDateTime.now());
                paypalUpgradeLogService.save(log);
                // 返回结果
                Map<String, String> response = new HashMap<>();
                response.put("approvalUrl", approvalUrl);
                return response;
            case "next_billing_period":
                // 1. 更新旧的订阅
                String paypalPlanId = newPaypalProduct.getPaypalPlanId();
                return updateSubscriptionFuture(oldSubscription, buyItemDto, paypalPlanId, "upgrade", user);
            default:
                throw new PayPalException(UNKNOWN_UPDATE_TYPE);

        }
    }

    private PaypalSubscriptionModel findActiveSubscription(User user) {
        SubscriptionCurrent subscriptionCurrentList = subscriptionCurrentService.getValidHighSubscriptionsFromDb(user.getId(), VipPlatform.PAYPAL.getPlatformName());
        if (subscriptionCurrentList == null || "basic".equals(subscriptionCurrentList.getPlanLevel())) {
            return null;
        }
        String subscriptionId = subscriptionCurrentList.getSubscriptionId();
        try {
            return PaypalSubscriptionModel.detail(apiContext, subscriptionId);
        } catch (PayPalRESTException e) {
            log.error("Failed to get subscription detail", e);
            throw new PayPalException(SUBSCRIPTION_NOT_FOUND);
        }
    }

    private boolean canDowngradeSubscription(PayPalProduct oldStripeProduct, PayPalProduct newStripeProduct) {
//        提取 old 和 new 的相关字段
        String oldPlanLevel = oldStripeProduct.getPlanLevel();
        String oldPriceInterval = oldStripeProduct.getPriceInterval();
        String newPlanLevel = newStripeProduct.getPlanLevel();
        String newPriceInterval = newStripeProduct.getPriceInterval();
        /**
         * year_pro降级month_pro
         * year_pro降级year_stand
         * year_pro降级month_stand
         * year_stand降级month_pro
         * year_stand降级month_stand
         * month_pro降级month_stand
         */
        if (oldPlanLevel.equals("pro") && newPlanLevel.equals("pro") && oldPriceInterval.equals("year") && newPriceInterval.equals("month")) {
            return true; // year_pro 降级 month_pro
        }
        if (oldPlanLevel.equals("pro") && newPlanLevel.equals("standard") && oldPriceInterval.equals("year") && newPriceInterval.equals("year")) {
            return true; // year_pro 降级 year_stand
        }
        if (oldPlanLevel.equals("pro") && newPlanLevel.equals("standard") && oldPriceInterval.equals("year") && newPriceInterval.equals("month")) {
            return true; // year_pro 降级 month_stand
        }
        if (oldPlanLevel.equals("standard") && newPlanLevel.equals("pro") && oldPriceInterval.equals("year") && newPriceInterval.equals("month")) {
            return true; // year_stand 降级 month_pro
        }
        if (oldPlanLevel.equals("standard") && newPlanLevel.equals("standard") && oldPriceInterval.equals("year") && newPriceInterval.equals("month")) {
            return true; // year_stand 降级 month_stand
        }
        if (oldPlanLevel.equals("pro") && newPlanLevel.equals("standard") && oldPriceInterval.equals("month") && newPriceInterval.equals("month")) {
            return true; // month_pro 降级 month_stand
        }

        // 不符合规则
        log.error("不支持的计划级别或价格间隔组合");
        return false;
    }

    /**
     * @Description: 判断旧的订阅是否可以升级为新的订阅
     * @Param: [oldStripeProduct, newStripeProduct]
     * @return: boolean 如果可以升级返回true，否则返回false
     * @Author: senlin_he
     * @Date: 2024/12/27
     */
    public Boolean canUpgradeSubscription(PayPalProduct oldStripeProduct, PayPalProduct newStripeProduct, String opType) {
        // 提取 old 和 new 的相关字段
        String oldPlanLevel = oldStripeProduct.getPlanLevel();
        String oldPriceInterval = oldStripeProduct.getPriceInterval();
        String newPlanLevel = newStripeProduct.getPlanLevel();
        String newPriceInterval = newStripeProduct.getPriceInterval();

        switch (opType) {
            case "immediate":
                // 允许的情况
                if (oldPlanLevel.equals("standard") && newPlanLevel.equals("standard") && oldPriceInterval.equals("month") && newPriceInterval.equals("year")) {
                    return true; // 从 standard month 到 standard year
                }
                if (oldPlanLevel.equals("standard") && newPlanLevel.equals("pro") && oldPriceInterval.equals("month") && newPriceInterval.equals("month")) {
                    return true;
                    // 从 standard month 到 pro month
                }
                if (oldPlanLevel.equals("standard") && newPlanLevel.equals("pro") && oldPriceInterval.equals("month") && newPriceInterval.equals("year")) {
                    return true;
                    // 从 standard month 到 pro year
                }
                if (oldPlanLevel.equals("standard") && newPlanLevel.equals("pro") && oldPriceInterval.equals("year") && newPriceInterval.equals("year")) {
                    return true;
                    // 从 standard year 到 pro year
                }
                if (oldPlanLevel.equals("pro") && newPlanLevel.equals("pro") && oldPriceInterval.equals("month") && newPriceInterval.equals("year")) {
                    return true;
                    // 从 pro month 到 pro year
                }
                break;
            case "next_billing_period":
                /**
                 * month_stand升级month_pro
                 * month_stand升级year_stand
                 * month_stand升级year_pro
                 * month_pro升级year_stand
                 * month_pro升级year_pro
                 * year_stand升级year_pro
                 */
                if (oldPlanLevel.equals("standard") && newPlanLevel.equals("pro") && oldPriceInterval.equals("month") && newPriceInterval.equals("month")) {
                    return true; // month_stand 升级 month_pro
                }
                if (oldPlanLevel.equals("standard") && newPlanLevel.equals("standard") && oldPriceInterval.equals("month") && newPriceInterval.equals("year")) {
                    return true; // month_stand 升级 year_stand
                }
                if (oldPlanLevel.equals("standard") && newPlanLevel.equals("pro") && oldPriceInterval.equals("month") && newPriceInterval.equals("year")) {
                    return true; // month_stand 升级 year_pro
                }
                if (oldPlanLevel.equals("pro") && newPlanLevel.equals("standard") && oldPriceInterval.equals("month") && newPriceInterval.equals("year")) {
                    return true; // month_pro 升级 year_stand
                }
                if (oldPlanLevel.equals("pro") && newPlanLevel.equals("pro") && oldPriceInterval.equals("month") && newPriceInterval.equals("year")) {
                    return true; // month_pro 升级 year_pro
                }
                if (oldPlanLevel.equals("standard") && newPlanLevel.equals("pro") && oldPriceInterval.equals("year") && newPriceInterval.equals("year")) {
                    return true; // year_stand 升级 year_pro
                }
                break;

            default:
                log.error("Unsupported operation type: {}", opType);
                return false;
        }

//        if (oldPlanLevel.equals("pro") && newPlanLevel.equals("standard") &&
//                oldPriceInterval.equals("month") && newPriceInterval.equals("year")) {
//            return true; // 从 pro month 到 standard year
//        }

        // 不符合规则
        log.error("不支持的计划级别或价格间隔组合");
        return false;
    }

    private void checkArgument(BuyItemVo buyItemDto) {
        if (buyItemDto == null || buyItemDto.getItems() == null) {
            log.error("Invalid BuyItemDto: {}", buyItemDto);
            throw new PayPalException(INVALID_REQUEST_PARAMETERS);
        }
        if ("one".equalsIgnoreCase(buyItemDto.getItems().get(0).getType().name()) && buyItemDto.getItems().get(0).getLumen() == null) {
            log.error("Invalid BuyItemDto: {}", buyItemDto);
            throw new PayPalException(INVALID_REQUEST_PARAMETERS);
        }
    }

    // 处理订阅类型的支付（plan）
    private Map<String, String> handlePlanPayment(BuyItemVo buyItemDto, User user) {
        crossPlatformPaymentCheck(user);
        List<ProductItem> items = buyItemDto.getItems();
        if (items == null || items.size() != 1) {
            log.error("Invalid BuyItemDto size: {}", buyItemDto);
            throw new PayPalException(INVALID_REQUEST_PARAMETERS);
        }
        ProductItem productItem = items.get(0);
        validateProduct(productItem);
        // 确保用户只有一个有效的订阅链接，删除db中的支付订单
        PaypalSubscriptionModel activeSubscription = findActiveSubscription(user);
        if (activeSubscription != null) {
            log.error("User {} already has an active subscription", user.getLoginName());
            throw new PayPalException(SUBSCRIPTION_CREATION_FAILED);
        }

        log.info("创建PayPal订阅开始，BuyItemDto: {}, user:{}", buyItemDto, user);

        try {
            PayPalProduct payPalProduct = payPalProductService.getPaypalProductByBuyItemDto(productItem);

            // Step 1: 构建订阅请求对象
            PaypalSubscriptionModel subscriptionModel = buildSubscriptionModel(payPalProduct, user, buyItemDto.getSuccessUrl(), buyItemDto.getCancelUrl());
            // Step 2: 调用 PayPal API 创建订阅
            log.debug("调用PayPal API创建订阅，planId: {}", subscriptionModel.toJSON());
            addDiscountPriceIfNeed(user, payPalProduct, subscriptionModel);
            PaypalSubscriptionModel createdSubscription = PaypalSubscriptionModel.create(apiContext, subscriptionModel);

            payPalLogicSubscriptionService.createSubscription(PaypalSubscriptionModel.detail(apiContext, createdSubscription.getId()), user.getId().toString());
            // Step 3: 提取返回的 token 和 approval_url
            String token = createdSubscription.getToken();
            String approvalUrl = extractApprovalUrl(createdSubscription);

            if (approvalUrl == null) {
                log.error("创建订阅失败，缺少token或approval URL。planId: {}", payPalProduct.getPaypalPlanId());
                throw new PayPalException(SUBSCRIPTION_CREATION_FAILED);
            }

            // 返回结果
            Map<String, String> response = new HashMap<>();
            response.put("approvalUrl", approvalUrl);
            log.info("创建PayPal订阅成功，planId: {}, userId: {}", payPalProduct.getPaypalPlanId(), user.getId());
            return response;

        } catch (IllegalArgumentException e) {
            log.error("参数校验失败: ", e);
            throw new PayPalException(INVALID_REQUEST_PARAMETERS, e);
        } catch (PayPalRESTException e) {
            log.error("PayPal API调用异常: {}", e.getMessage(), e);
            throw new PayPalException(CALL_PAYPAL_API_ERROR, e);
        } catch (Exception e) {
            log.error("创建订阅时发生未知异常: {}", e.getMessage(), e);
            throw new PayPalException(UNKNOWN_ERROR, e);
        }
    }

    private void addDiscountPriceIfNeed(User user, PayPalProduct payPalProduct, PaypalSubscriptionModel subscriptionModel) {
        Boolean canTrailAndFirstBuy = vipService.canTrail(user.getId());
        PayPalPlanModel plan = null;
        Integer firstBuySub = payPalProduct.getFirstBuySub();
        if (canTrailAndFirstBuy && firstBuySub != null && firstBuySub > 0) {
            BigDecimal priceBase = new BigDecimal(payPalProduct.getPrice());
            BigDecimal percent = (new BigDecimal(100).subtract(new BigDecimal(firstBuySub))).divide(new BigDecimal(100), 2, RoundingMode.HALF_DOWN)  ;
            BigDecimal price = priceBase.multiply(percent).setScale(2, RoundingMode.HALF_DOWN);
            plan = PayPalPlanModel.builder().billingCycles(Lists.newArrayList(BillingCycle.builder()
                            .sequence(1)
                            .pricingScheme(PricingScheme.builder()
                                    .fixedPrice(FixedPrice.builder()
                                            .currencyCode("USD")
                                            .value(price.toString())
                                            .build())
                                    .build())
                            .build()))
                    .build();
            String customId = subscriptionModel.getCustomId();
            JSONObject jsonObject = JSONUtil.parseObj(customId);
            jsonObject.set(FIRST_BUY_SUB_OFF, firstBuySub);
            subscriptionModel.setCustomId(jsonObject.toString());
        }
        if (plan != null) {
            log.info("addDiscountPriceIfNeed plan: {}", plan.toJSON());
            subscriptionModel.setPlan(plan);
        }
    }

    private void checkOneTimeParam(List<ProductItem> productItems) {
        if (CollUtil.isEmpty(productItems)) {
            for (ProductItem stripeItem : productItems) {
                if (stripeItem.getLumen() == null || stripeItem.getLumen() <= 0) {
                    throw new BadRequestException("Invalid amount");
                }
            }
        }
    }

    // 处理一次性支付（one）
    private Map<String, String> handleOneTimePayment(BuyItemVo buyItemDto, User user) {
        checkOneTimeParam(buyItemDto.getItems());
        try {
            // Step 1: 构建订阅请求对象
            PaypalCheckoutOrderModel paypalCheckoutOrderModel = buildCheckoutOrderModel(user, buyItemDto);
            // Step 2: 调用 PayPal API 创建订阅
            log.info("调用PayPal API创建一次性购买，productIds: {}", paypalCheckoutOrderModel.toJSON());
            PaypalCheckoutOrderModel createdOrder = PaypalCheckoutOrderModel.createOrder(apiContext, paypalCheckoutOrderModel);
            log.info("创建PayPal购买lumen成功，{}", createdOrder);
//            payPalOrderPaymentRecordService.createPayment(createdOrder);
            // Step 3: 提取返回的 token 和 approval_url
            String token = createdOrder.getToken();
            String approvalUrl = extractApprovalUrl(createdOrder);

            if (approvalUrl == null) {
                log.error("创建订阅失败，缺少token或approval URL。planId");
                throw new PayPalException(SUBSCRIPTION_CREATION_FAILED);
            }

            // 返回结果
            Map<String, String> response = new HashMap<>();
            response.put("token", token);
            response.put("approvalUrl", approvalUrl);
            log.info("创建PayPal购买lumen成功， userId: {}", user.getId());
            return response;

        } catch (IllegalArgumentException e) {
            log.error("参数校验失败: ", e);
            throw new PayPalException(INVALID_REQUEST_PARAMETERS, e);
        } catch (PayPalRESTException e) {
            log.error("PayPal API调用异常: ", e);
            throw new PayPalException(CALL_PAYPAL_API_ERROR, e);
        } catch (Exception e) {
            log.error("创建订阅时发生未知异常: ", e);
            throw new PayPalException(UNKNOWN_ERROR, e);
        }
    }

    // 验证产品信息是否有效
    private void validateProduct(ProductItem stripeItem) {
        if (StringUtil.isBlank(stripeItem.getProduct()) || StringUtil.isBlank(stripeItem.getPriceInterval())) {
            throw new PayPalException(NO_PAYPAL_PRODUCT_FOUND);
        }
    }

    private PaypalSubscriptionModel buildSubscriptionModel(PayPalProduct payPalProduct, User user, String successUrl, String cancelUrl) {
        JSONObject jsonObject = JSONUtil.createObj();
        jsonObject.set("userId", user.getId());
        return PaypalSubscriptionModel.builder()
                .planId(payPalProduct.getPaypalPlanId())
                .customId(jsonObject.toString())
                .quantity(1)
                .applicationContext(ApplicationContext.builder()
                        .brandName("Piclumen").locale("en-US")
                        .shippingPreference("NO_SHIPPING")
                        .userAction("SUBSCRIBE_NOW").returnUrl(successUrl)
                        .cancelUrl(cancelUrl).build()).build();

    }

    private PaypalCheckoutOrderModel buildCheckoutOrderModel(User user, BuyItemVo buyItemDto) {
        List<Integer> lumenList = new ArrayList<>();
        String successUrl = buyItemDto.getSuccessUrl();
        String cancelUrl = buyItemDto.getCancelUrl();

        Map<Integer, ProductItem> lumenItemMap = new HashMap<>();
        for (ProductItem item : buyItemDto.getItems()) {
            lumenItemMap.put(item.getLumen(), item);
            lumenList.add(item.getLumen());
        }

        List<PayPalProduct> payPalProducts = payPalProductService.getPaypalProductListByLumenList(lumenList);
        // 构建支付来源 (PaymentSource)
        PaypalPaymentCaptureModel.PaymentSource paymentSource = PaypalPaymentCaptureModel.PaymentSource.builder()
                .paypal(PaypalPaymentCaptureModel.PaymentSource.Paypal.builder()
                        .experienceContext(PaypalPaymentCaptureModel.PaymentSource.ExperienceContext.builder()
                                .paymentMethodPreference("IMMEDIATE_PAYMENT_REQUIRED")
                                .landingPage("LOGIN")
                                .shippingPreference("NO_SHIPPING")
                                .userAction("PAY_NOW")
                                .returnUrl(successUrl)
                                .cancelUrl(cancelUrl)
                                .build())
                        .build())
                .build();
        JSONObject jsonObject = JSONUtil.createObj();
        jsonObject.set("userId", user.getId());
        // 构建购买单元 (PurchaseUnit)
//        Map<String, Pair<String, String>> priceAndDescription = calculatePrice(payPalProducts, user.getId(), lumenItemMap);
        Triple<BigDecimal, BigDecimal, List<PaypalCheckoutOrderModel.Items>> bigDecimalListTriple = calculatePrice(payPalProducts, user.getId(), lumenItemMap, jsonObject);
        List<PaypalCheckoutOrderModel.PurchaseUnit> purchaseUnits = new ArrayList<>();
        PaypalCheckoutOrderModel.Discount discount = null;
        if (bigDecimalListTriple.getMiddle() != null && bigDecimalListTriple.getMiddle().compareTo(BigDecimal.ZERO) > 0) {
            discount = PaypalCheckoutOrderModel.Discount.builder()
                    .currencyCode("USD")
                    .value(bigDecimalListTriple.getMiddle().toString())
                    .build();
        }
        PaypalCheckoutOrderModel.PurchaseUnit purchaseUnit = PaypalCheckoutOrderModel.PurchaseUnit.builder()
                .customId(jsonObject.toString())
                // 商品的 reference_id
                .referenceId("lumen")
                .amount(PaypalCheckoutOrderModel.Amount.builder()
                        // 货币代码
                        .currencyCode("USD")
                        // 支付金额
                        .value(bigDecimalListTriple.getLeft().subtract(bigDecimalListTriple.getMiddle()).toString())
                        .breakdown(PaypalCheckoutOrderModel.Breakdown.builder()
                                .itemTotal(PaypalCheckoutOrderModel.ItemTotal.builder()
                                        .currencyCode("USD")
                                        .value(bigDecimalListTriple.getLeft().toString())
                                        .build())
                                .discount(discount)
                                .build())
                        .build())
                .items(bigDecimalListTriple.getRight().toArray(new PaypalCheckoutOrderModel.Items[0]))
                .description("Purchase Lumen Package")
                .build();
        purchaseUnits.add(purchaseUnit);
        // 构建订单模型 (PaypalCheckoutOrderModel)
        return PaypalCheckoutOrderModel.builder()
                // 设置支付意图
                .intent("CAPTURE")
                // 设置支付来源
                .paymentSource(paymentSource)
                // 设置购买单元
                .purchaseUnits(purchaseUnits).build();
    }

    private Triple<BigDecimal, BigDecimal, List<PaypalCheckoutOrderModel.Items>> calculatePrice(List<PayPalProduct> payPalProducts, Long userId, Map<Integer, ProductItem> lumenItemMap,
                                                                                                JSONObject jsonObject) {
        SubscriptionCurrent logicValidHighSubscriptionsFromDb = subscriptionCurrentService.getLogicValidHighSubscriptionsFromDb(userId);
        String planLevel = logicValidHighSubscriptionsFromDb.getPlanLevel();
        List<VipStandards> vipStandardsList = vipService.getVipStandardsList(null);
        VipStandards vipStandard = vipStandardsList.stream().filter(vipStandards -> vipStandards.getVipType().equals(planLevel)).findFirst().orElse(null);

        List<PaypalCheckoutOrderModel.Items> items = new ArrayList<>();

        BigDecimal discountPrice = BigDecimal.ZERO;
        BigDecimal total = BigDecimal.ZERO;
        for (PayPalProduct payPalProduct : payPalProducts) {
            String price = payPalProduct.getPrice();
            BigDecimal priceBig = new BigDecimal(price);
            ProductItem productItem = lumenItemMap.get(payPalProduct.getLumen());
            Integer qty = productItem.getQty();
            BigDecimal priceTotal = priceBig.multiply(new BigDecimal(qty));
            total = total.add(priceTotal);

            Integer lumenDiscount = vipStandard != null ? vipStandard.getLumenDiscount() : null;
            if (lumenDiscount != null && lumenDiscount > 0) {
                BigDecimal discountPercent = new BigDecimal(100 - lumenDiscount).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);

                BigDecimal priceAfterDiscount = priceTotal.multiply(discountPercent);
                discountPrice = discountPrice.add(priceTotal.subtract(priceAfterDiscount));
                jsonObject.set(LUMEN_VIP_OFF, lumenDiscount);
            }

            PaypalCheckoutOrderModel.Items build = PaypalCheckoutOrderModel.Items.builder()
                    .name(payPalProduct.getProductId())
                    .description("Purchase " + payPalProduct.getLumen() + " lumen")
                    .quantity(String.valueOf(qty))
                    .sku(payPalProduct.getProductId())
                    .unitAmount(PaypalCheckoutOrderModel.UnitAmount.builder()
                            .currencyCode("USD")
                            .value(priceBig.toString())
                            .build())
                    .build();
            items.add(build);

        }
        return Triple.of(total, discountPrice.setScale(2, RoundingMode.HALF_DOWN), items);
    }

    private String extractApprovalUrl(PaypalSubscriptionModel subscription) {
        return subscription.getLinks().stream().filter(link -> "approve".equals(link.getRel())).findFirst().map(Links::getHref).orElse(null);
    }

    private String extractApprovalUrl(PaypalCheckoutOrderModel subscription) {
        return subscription.getLinks().stream().filter(link -> "payer-action".equals(link.getRel())).findFirst().map(Links::getHref).orElse(null);
    }

    public void crossPlatformPaymentCheck(User user) {
        Boolean canPay = subscriptionCurrentService.canPay(user.getId(), VipPlatform.PAYPAL.getPlatformName());
        if (!canPay) {
            log.error("User {} Cannot make cross-platform payments，at this time{}", user.getLoginName(), System.currentTimeMillis());
            throw new PayPalException(CROSS_PLATFORM_ERROR);
        }
    }


}
