package com.lx.pl.pay.paypal.controller;

import com.lx.pl.annotation.Authorization;
import com.lx.pl.annotation.CurrentUser;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.dto.generic.R;
import com.lx.pl.pay.paypal.model.vo.BuyItemVo;
import com.lx.pl.pay.paypal.service.PayPalPayService;
import com.lx.pl.pay.paypal.service.PaypalUpgradeLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;


@RestController
@RequestMapping("/api/paypal")
@Tag(name = "paypal业务接口")
public class PayPalController {

    @Resource
    private PayPalPayService payPalpayservice;
    @Resource
    private PaypalUpgradeLogService paypalUpgradeLogService;
    @Resource
    private RedissonClient redissonClient;

    @Operation(summary = "购买或订阅产品")
    @PostMapping("/create-payment")
    @Authorization
    public R<Map<String, String>> createPayment(@RequestBody @Parameter(description = "订阅相关参数对象") BuyItemVo buyItemDto, @CurrentUser @Parameter(hidden = true) User user) {
        RLock lock = redissonClient.getLock("paypal:user:" + user.getId());
        try {
            lock.lock();
            Map<String, String> result = payPalpayservice.createPayment(buyItemDto, user);
            return R.success(result);
        } finally {
            if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                lock.unlock();
            }
        }
    }

    /**
     * 升级/降级订阅
     */
    @Operation(summary = "升级/降级订阅")
    @PostMapping("upgrade-downgrade")
    @Authorization
    public R<Map<String, String>> upgradeDowngrade(@RequestBody @Parameter(description = "订阅相关参数对象") BuyItemVo buyItemDto, @Parameter(description = "升级/降级类型 upgrade/downgrade", required = true) @RequestParam(required = true) String type, @Parameter(description = "立即/未来: immediate/next_billing_period", required = true) @RequestParam(required = true) String opType, @CurrentUser @Parameter(hidden = true) User user) {
        RLock lock = redissonClient.getLock("paypal:user:" + user.getId());
        try {
            lock.lock();
            Map<String, String> rtn = payPalpayservice.upgradeDowngrade(buyItemDto, type, opType, user);
            return R.success(rtn);
        } finally {
            if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                lock.unlock();
            }
        }

    }

    /**
     * 取消订阅
     */
    @Operation(summary = "取消订阅")
    @PostMapping("cancel")
    @Authorization
    public R<String> cancelSubscription(@CurrentUser @Parameter(hidden = true) User user) {
        RLock lock = redissonClient.getLock("paypal:user:" + user.getId());
        try {
            lock.lock();
            payPalpayservice.cancelSubscription(user);
            return R.success();
        } finally {
            if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                lock.unlock();
            }
        }
    }

//    @GetMapping("/test")
//    public void test() {
//        paypalUpgradeLogService.handleCancelSubscription();
//    }

    /**
     * 查询即将生效的订阅
     */
    @Operation(summary = "查询即将生效的订阅")
    @GetMapping("query-valid-not-handle-subscriptions")
    @Authorization
    public R<Map<String, Object>> queryValidNotHandleSubscriptions(@CurrentUser @Parameter(hidden = true) User user) {
        Map<String, Object> rtn = payPalpayservice.queryValidNotHandleSubscriptions(user);
        return R.success(rtn);
    }


}
