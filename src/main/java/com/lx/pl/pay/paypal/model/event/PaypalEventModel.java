package com.lx.pl.pay.paypal.model.event;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.paypal.api.payments.Links;
import com.paypal.base.rest.JSONFormatter;
import com.paypal.base.rest.PayPalResource;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class PaypalEventModel extends PayPalResource {

    private String id;

    private String createTime;

    private String resourceType;

    private String resourceVersion;

    private String eventType;

    private String summary;

    private String status;

    private Object resource;

    private String eventVersion;

    private List<Links> links;

    private Boolean verified;

    public String getResource() {
        if (resource == null) {
            return null;
        }
        return JSONFormatter.toJSON(resource);
    }
}
