package com.lx.pl.pay.paypal.model.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.lx.pl.db.mysql.gen.entity.MyBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("paypal_sub_payment_record")
public class PayPalSubPaymentRecord extends MyBaseEntity {

    private Long id;
    private Long userId;  // 用户ID

    private String loginName;  // 用户登录名

    private String subscriptionId;  // 订阅ID

    private Long paypalLogicSubId;  // PayPal 订阅逻辑ID

    private String paymentId;  // 付款ID

    private String state;  // 付款状态

    private String total;  // 总金额

    private String subtotal;  // 小计金额

    private String currency;  // 货币

    private String fee;  // 手续费

    private String payCreateTime;

    private String refundId;  // 退款ID（如果有）
}
