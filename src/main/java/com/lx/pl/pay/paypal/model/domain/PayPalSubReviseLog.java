package com.lx.pl.pay.paypal.model.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.lx.pl.db.mysql.gen.entity.MyBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("paypal_sub_revise_log")
public class PayPalSubReviseLog extends MyBaseEntity {

    private static final long serialVersionUID = 7095517139428321087L;
    private Long id;
    private Long userId;  // 用户ID

    private String loginName;  // 用户登录名

    private String status;  // 状态

    private String subscriptionId;  // 订阅ID

    private String srcPlanId;  // 原始计划ID

    private String newPlanId;  // 新计划ID

    private Long nextBillingTimeSec;

    private String type;  // 操作类型（upgrade/downgrade）
}
