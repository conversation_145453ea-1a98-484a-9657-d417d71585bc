package com.lx.pl.pay.paypal.model.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.lx.pl.db.mysql.gen.entity.MyBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@TableName("paypal_upgrade_log")
@Data
public class PaypalUpgradeLog extends MyBaseEntity {

    private static final long serialVersionUID = -2786969068218416423L;
    private Long id;

    private Long userId;

    private String loginName;

    private String srcSubscriptionId;
    private String srcSubStatus;

    private String newSubscriptionId;
    private String newSubStatus;
    private Long newSubActiveTime;

    private String srcPlanId;

    private String newPlanId;

    private Boolean vipRevert;


    private Boolean hasRefund;

    private String refundAmount;

    private String refundId;

}
