package com.lx.pl.pay.paypal.util;

import javax.servlet.http.HttpServletRequest;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

public class HeaderParser {

    /**
     * 将 HttpServletRequest 的所有请求头解析为 Map
     *
     * @param request HttpServletRequest 对象
     * @return 包含所有请求头的 Map
     */
    public static Map<String, String> parseHeaders(HttpServletRequest request) {
        Map<String, String> headerMap = new HashMap<>();

        // 获取所有的请求头名称
        Enumeration<String> headerNames = request.getHeaderNames();
        if (headerNames != null) {
            while (headerNames.hasMoreElements()) {
                String headerName = headerNames.nextElement();
                // 获取对应的请求头值
                String headerValue = request.getHeader(headerName);
                headerMap.put(headerName.toLowerCase(), headerValue); // 转为小写以统一格式
            }
        }

        return headerMap;
    }
}