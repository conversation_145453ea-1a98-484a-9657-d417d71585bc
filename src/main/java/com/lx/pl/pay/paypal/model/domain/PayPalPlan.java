package com.lx.pl.pay.paypal.model.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lx.pl.db.mysql.gen.entity.MyBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("paypal_plan")
public class PayPalPlan extends MyBaseEntity {

    private Long id;
    private String planId;   // PayPal 计划ID

    private String productId;  // 产品ID

    private String name;  // 计划名称

    private String description;  // 计划描述

    private String status;  // 计划状态

    private String planLevel;  // 计划等级（standard/pro）

    private Integer lumen;  // 流明数值（仅适用于特定产品类型）

    private String productType;  // 产品类型（plan/lumen）

    private String priceInterval;  // 价格间隔（按月/按年）

    private Integer vipLevel;  // VIP等级（1/2/3/4）

    private String currency;  // 货币类型
}
