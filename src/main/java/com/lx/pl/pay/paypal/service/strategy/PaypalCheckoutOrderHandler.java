package com.lx.pl.pay.paypal.service.strategy;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.lx.pl.pay.paypal.annotation.PaypalEvent;
import com.lx.pl.pay.paypal.model.PaypalCheckoutOrderModel;
import com.lx.pl.pay.paypal.model.domain.PayPalOrderPaymentRecord;
import com.lx.pl.pay.paypal.model.event.PaypalCheckoutOrderEvent;
import com.lx.pl.pay.paypal.service.IPaypalEventHandler;
import com.paypal.api.payments.Error;
import com.paypal.api.payments.ErrorDetails;
import com.paypal.base.rest.PayPalRESTException;
import org.redisson.api.RLock;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 *
 */
@Component
@PaypalEvent(eventType = {"CHECKOUT.ORDER.APPROVED"})
public class PaypalCheckoutOrderHandler extends IPaypalEventHandler<PaypalCheckoutOrderEvent> {
    @Override
    public void handleEvent(PaypalCheckoutOrderEvent data) {
        PaypalCheckoutOrderModel checkoutOrderModel = data.getModel();
        List<PaypalCheckoutOrderModel.PurchaseUnit> purchaseUnits = checkoutOrderModel.getPurchaseUnits();
        String customerId = checkoutOrderModel.getPurchaseUnits().get(0).getCustomId();
        // str to json
        if (customerId != null) {
            JSONObject entries = JSONUtil.parseObj(customerId);
            customerId = entries.getStr("userId");
        }
        RLock lock = redissonClient.getLock(PAYPAL_USER_LOCK_PREFIX + customerId);
        String loginName = null;
        try {
            lock.lock();
            loginName = applicationContext.getBean(PaypalCheckoutOrderHandler.class).doHandleEvent(checkoutOrderModel);
            if (StrUtil.isNotBlank(loginName)) {
                vipService.resettingPersonalLumens(loginName);
            }
        } finally {
            unlockAndRefreshVip(lock, loginName);
        }
    }


    @Transactional(rollbackFor = Exception.class)
    public String doHandleEvent(PaypalCheckoutOrderModel data) {
        List<PayPalOrderPaymentRecord> captureModelList = payPalOrderPaymentRecordService.createPayment(data);
        if (captureModelList == null || captureModelList.isEmpty()) {
            log.info("createPayment failed");
            return null;
        }
        String loginName = captureModelList.get(0).getLoginName();
        String paymentId = captureModelList.get(0).getOrderId();
        try {
            PaypalCheckoutOrderModel detail = PaypalCheckoutOrderModel.detail(apiContext, paymentId);
            log.info("paypal checkout order detail,{}", detail);
            if ("COMPLETED".equalsIgnoreCase(detail.getStatus())) {
                return loginName;
            }
            PaypalCheckoutOrderModel capture = PaypalCheckoutOrderModel.capture(apiContext, paymentId);
            log.info("paypal checkout order capture success,{}", capture);
        } catch (PayPalRESTException e) {
            int responsecode = e.getResponsecode();
            if (responsecode == 422) {
                Error details = e.getDetails();
                if (details != null) {
                    List<ErrorDetails> details1 = details.getDetails();
                    for (ErrorDetails errorDetails : details1) {
                        String issue = errorDetails.getIssue();
                        if ("INSTRUMENT_DECLINED".equalsIgnoreCase(issue)) {
                            log.info("paypal checkout order capture failed,{}", issue);
                            return loginName;
                        }
                    }
                }
            }
            throw new RuntimeException(e);
        }
        return loginName;
    }

}
