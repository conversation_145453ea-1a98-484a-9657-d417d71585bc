package com.lx.pl.pay.paypal.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lx.pl.pay.paypal.model.PaypalRefundModel;
import com.lx.pl.pay.paypal.model.domain.PayPalLogicSubscription;
import com.lx.pl.pay.paypal.model.domain.PayPalRefundRecord;

public interface PayPalRefundRecordService extends IService<PayPalRefundRecord> {
    PayPalRefundRecord calculateAndDoRefundAmount(PayPalLogicSubscription validSubscription, long timeSeconds) throws Exception;

    PayPalRefundRecord findByRefundId(String refundId);

    void updateRecord(String refundId, PaypalRefundModel model, Long id);

    PayPalRefundRecord findByPaymentId(String paymentId);
}
