package com.lx.pl.pay.paypal.service;


import com.lx.pl.pay.paypal.annotation.PaypalEvent;
import org.springframework.aop.framework.AopProxyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Component
public class PaypalEventHandlerContext {
    private final Map<String, IPaypalEventHandler<?>> eventHandlers = new HashMap<>();

    @Autowired
    private List<? extends IPaypalEventHandler<?>> handlerBeans;

    @PostConstruct
    public void init() {
        for (IPaypalEventHandler<?> handler : handlerBeans) {
            Class<?> aClass = AopProxyUtils.ultimateTargetClass(handler);
            PaypalEvent annotation = aClass.getAnnotation(PaypalEvent.class);
            if (annotation != null) {
                String[] eventTypes = annotation.eventType();
                if (eventTypes.length == 0) {
                    throw new RuntimeException("EventHandlerContext: No eventType found on " + handler.getClass()
                            .getName());
                }
                for (String eventType : eventTypes) {
                    if (eventHandlers.containsKey(eventType)) {
                        throw new RuntimeException("EventHandlerContext: Duplicate eventType found on " + handler.getClass()
                                .getName());
                    }
                    eventHandlers.put(eventType, handler);
                }
            }
        }
    }

    public IPaypalEventHandler<?> getHandler(String eventType) {
        return eventHandlers.get(eventType);
    }

}
