package com.lx.pl.pay.paypal.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lx.pl.pay.paypal.model.domain.PaypalEventLog;
import com.lx.pl.pay.paypal.model.event.PaypalEventModel;

public interface PaypalEventLogService extends IService<PaypalEventLog> {
    PaypalEventLog saveWebhookEvent(PaypalEventModel data);

    void updateStatus(Long logId, String processStatus);

    void updateStatus(Long logId, String processStatus, String errorMessage);
}
