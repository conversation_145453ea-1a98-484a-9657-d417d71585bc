package com.lx.pl.pay.paypal.model;

import com.paypal.api.payments.Links;
import com.paypal.base.rest.APIContext;
import com.paypal.base.rest.HttpMethod;
import com.paypal.base.rest.PayPalRESTException;
import com.paypal.base.rest.RESTUtil;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
public class PayPalPlanModel extends CustomPaypalResource {
    private String id;
    private String productId;
    private String name;
    private String description;
    private String status;
    private List<BillingCycle> billingCycles;
    private Boolean quantitySupported;
    private PaymentPreferences paymentPreferences;
    private Taxes taxes;
    private List<Links> links;
    private String token;


    public static PlanHistory list(APIContext apiContext, int pageSize, int page) throws PayPalRESTException {
        String resourcePath = "v1/billing/plans?page_size={0}&page={1}";
        
        Object[] parameters = new Object[]{pageSize, page};
        resourcePath = RESTUtil.formatURIPath(resourcePath, parameters);
        String payLoad = "";
        return executeWithRetry(apiContext, HttpMethod.GET, resourcePath, payLoad, PlanHistory.class);
    }

    /**
     * get the plan
     */

    public static PayPalPlanModel get(APIContext apiContext, String planId) throws PayPalRESTException {
        if (planId == null) {
            throw new IllegalArgumentException("planId cannot be null");
        }
        
        String resourcePath = "v1/billing/plans/{0}";
        Object[] parameters = new Object[]{planId};
        resourcePath = RESTUtil.formatURIPath(resourcePath, parameters);
        String payLoad = "";
        return executeWithRetry(apiContext, HttpMethod.GET, resourcePath, payLoad, PayPalPlanModel.class);
    }


    public static PayPalPlanModel create(APIContext apiContext, PayPalPlanModel plan) throws PayPalRESTException {
        String resourcePath = "v1/billing/plans";
        String payLoad = plan.toJSON();
        return executeWithRetry(apiContext, HttpMethod.POST, resourcePath, payLoad, PayPalPlanModel.class);
    }

    // create product
    public static Object createProduct(APIContext apiContext, String payLoad) throws PayPalRESTException {
        String resourcePath = "v1/catalogs/products";
//        String payLoad = product.toJSON();
        return executeWithRetry(apiContext, HttpMethod.POST, resourcePath, payLoad, Object.class);
    }
}










