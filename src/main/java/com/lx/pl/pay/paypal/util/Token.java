//package com.lx.pl.pay.paypal.util;
//
//import org.json.JSONObject;
//
//import java.util.HashMap;
//import java.util.Map;
//
//public class Token {
//
//    /**
//     * oauth2生成token请求地址
//     */
//    private final String V1_GET_TOKEN_URL = "/v1/oauth2/token";
//    /**
//     * 创建订单请求地址
//     */
//    private final String V2_CHECK_ORDER_URL = "/v2/checkout/orders";
//    /**
//     * 创建订阅计划请求地址
//     */
//    private final String V1_BILLING_PLANS_URL = "/v1/billing/plans";
//    /**
//     * 创建订阅请求地址
//     */
//    private final String V1_BILLING_SUBSCRIPRION_URL = "/v1/billing/subscriptions";
//    /**
//     * 创建订阅商品请求地址
//     */
//    private final String V1_CATALOGS_PRODUCT_URL = "/v1/catalogs/products";
//    /**
//     * 构造请求头
//     *
//     * @param requestId   请求唯一Id，可以不用，自己实现调用日志归档
//     * @param accessToken OAuth accessToken
//     * @return
//     */
//    public Map<String, Object> createRequestHeaders(String requestId, String accessToken) {
//        Map<String, Object> headerMap = new HashMap<>();
//        headerMap.put("Authorization", "Bearer " + accessToken);
//        headerMap.put(ConstantUtil.CONTENT_TYPE, ConstantUtil.APPLICATION_JSON_REQUEST);
//        if (!StringUtils.isEmpty(requestId)) {
//            headerMap.put("PayPal-Request-Id", requestId);
//        }
//        return headerMap;
//    }
//
//    /**
//     * 获取Token对象
//     *
//     * @param appName 应用唯一标识
//     * @return
//     * @throws PaymentCoreException
//     */
//    @Override
//    public synchronized TokenContext getAuthInstance(String appName) throws PaymentCoreException {
//        TokenContext tokenContext = TOKEN_CONTEXT_MAP.get(appName);
//        if (tokenContext != null && tokenContext.expiresIn() > 0) {
//            return tokenContext;
//        }
//        //获取access token
//        PayPalConfigEntity paymentConfig = paymentConfigFactory.getPaymentConfig(ConstantUtil.PAY_PAL_PAYMENT_CONFIG_IMPL, appName);
//        JSONObject response = this.getAccessToken(paymentConfig);
//        tokenContext = new TokenContext();
//        tokenContext.setAccessToken(response.getString("access_token"));
//        tokenContext.setExpire((System.currentTimeMillis() / 1000) + response.getLongValue("expires_in"));
//        tokenContext.setTokenType(response.getString("token_type"));
//        tokenContext.setAppId(response.getString("app_id"));
//        tokenContext.setDomainUrl(paymentConfig.getDomainUrl());
//        TOKEN_CONTEXT_MAP.put(appName, tokenContext);
//        return tokenContext;
//    }
//
//    /**
//     * 请求token API 获取accessToken
//     * {
//     * "scope": "https://uri.paypal.com/services/applications/webhooks",
//     * "access_token": "xxxxxxxxxxxxxxxxxx",
//     * "token_type": "Bearer",
//     * "app_id": "APP-xxxxxxxxx",
//     * "expires_in": 32400,
//     * "nonce": "2023-09-12T12:43:35Z6Yw0-JDPHF1FA84QudGxxxxxxxTKT5cus"
//     * }
//     *
//     * @param paymentConfig 配置信息
//     * @return JSONObject
//     * @throws PaymentCoreException
//     */
//    private JSONObject getAccessToken(PayPalConfigEntity paymentConfig) throws PaymentCoreException {
//        String base64Str = EncryptionUtil.base64Encode(paymentConfig.getSecretId() + ":" + paymentConfig.getSecretKey());
//        String str = HttpClientUtil.postOAuth2(paymentConfig.getDomainUrl() + V1_GET_TOKEN_URL, base64Str);
//        if (StringUtils.isEmpty(str)) {
//            throw new PaymentCoreException("[PayPal] get access token fail, Pls check log");
//        }
//        return JSONObject.parseObject(str);
//    }
//
//}
