package com.lx.pl.pay.paypal.model.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.lx.pl.db.mysql.gen.entity.MyBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("paypal_logic_order")
public class PayPalLogicOrder extends MyBaseEntity {
    private static final long serialVersionUID = -9109609612280189403L;
    private Long id;

    private String userId;  // 用户ID

    private String loginName;  // 用户登录名

    private String status;  // 订单状态

    private String referenceId;  // 订单关联ID

    private String orderId;  // 订单ID

    private String amount;  // 订单金额

    private String currency;  // 订单货币

    private String payerId;  // 付款人ID

    private String payerEmail;  // 付款人邮箱

    private String payerCountry;  // 付款人国家
}
