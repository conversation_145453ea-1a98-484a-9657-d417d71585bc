package com.lx.pl.pay.paypal.model.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.lx.pl.db.mysql.gen.entity.MyBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("paypal_order_payment_record")
public class PayPalOrderPaymentRecord extends MyBaseEntity {

    private Long id;
    private Long userId;  // 用户ID
    private String payCreateTime;
    private String loginName;  // 用户登录名

    private String orderId;  // 支付ID

    private String referenceId; // 支付参考ID

    private String status;  // 支付状态

    private String amount;  // 支付金额

    private String discountAmount; // 折扣金额

    private String currency;  // 支付货币

    private String payeeId;  // 收款人ID

    private String payeeEmail;
//
    private String fee;  // 手续费

    private String netAmount;  // 净收款金额

    private String refundId;  // 退款ID（如果有）
}
