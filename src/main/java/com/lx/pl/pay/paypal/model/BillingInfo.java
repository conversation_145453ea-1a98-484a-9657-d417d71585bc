package com.lx.pl.pay.paypal.model;

import com.paypal.base.rest.PayPalResource;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


@EqualsAndHashCode(callSuper = true)
@Data
public class BillingInfo extends PayPalResource {
    /**
     * 未结余额
     */
    private OutstandingBalance outstandingBalance;

    /**
     * 计费周期执行信息
     */
    private List<CycleExecution> cycleExecutions;

    /**
     * 上次支付信息
     */
    private LastPayment lastPayment;

    /**
     * 下次账单时间
     */
    private String nextBillingTime;

    /**
     * 失败支付次数
     */
    private int failedPaymentsCount;

    // Getters and Setters

    /**
     * 未结余额信息
     */
    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class OutstandingBalance extends PayPalResource {
        private String currencyCode;
        private String value;

        // Getters and Setters
    }

    /**
     * 计费周期执行信息
     */
    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class CycleExecution extends PayPalResource {
        private String tenureType;
        private int sequence;
        private int cyclesCompleted;
        private int cyclesRemaining;
        private int currentPricingSchemeVersion;
        private int totalCycles;

        // Getters and Setters
    }

    /**
     * 上次支付信息
     */
    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class LastPayment extends PayPalResource {
        private Amount amount;
        private String time;

        // Getters and Setters
    }


    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class Amount extends PayPalResource {
        private String currencyCode;
        private String value;

        // Getters and Setters
    }

}