package com.lx.pl.pay.paypal.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lx.pl.pay.paypal.mapper.PaypalEventLogMapper;
import com.lx.pl.pay.paypal.model.domain.PaypalEventLog;
import com.lx.pl.pay.paypal.model.event.PaypalEventModel;
import com.lx.pl.pay.paypal.service.PaypalEventLogService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@Service
public class PaypalEventLogServiceImpl extends ServiceImpl<PaypalEventLogMapper, PaypalEventLog> implements PaypalEventLogService {
    @Override
    @Transactional(rollbackFor = Exception.class)
    public PaypalEventLog saveWebhookEvent(PaypalEventModel data) {
        String webhookId = data.getId();
        boolean exists = this.lambdaQuery()
                .eq(PaypalEventLog::getWebhookId, webhookId)
                .eq(PaypalEventLog::getProcessStatus, "success")
                .exists();
        if (exists) {
            return null;
        }

        PaypalEventLog paypalEventLog = new PaypalEventLog();
        paypalEventLog.setWebhookId(webhookId);
        paypalEventLog.setVerified(data.getVerified());
        paypalEventLog.setWebhookCreateTime(data.getCreateTime());
        paypalEventLog.setResourceType(data.getResourceType());
        paypalEventLog.setResourceVersion(data.getResourceVersion());
        paypalEventLog.setEventType(data.getEventType());
        paypalEventLog.setSummary(data.getSummary());
        paypalEventLog.setStatus(data.getStatus());
        paypalEventLog.setProcessStatus("not_process");
        paypalEventLog.setResource(data.getResource());
        paypalEventLog.setEventVersion(data.getEventVersion());
        paypalEventLog.setCreateBy("webhook");
        paypalEventLog.setCreateTime(LocalDateTime.now());

        this.save(paypalEventLog);
        return paypalEventLog;
    }

    @Override
    public void updateStatus(Long logId, String processStatus) {
        this.lambdaUpdate().eq(PaypalEventLog::getId, logId)
                .eq(PaypalEventLog::getProcessStatus, "not_process")
                .set(PaypalEventLog::getProcessStatus, processStatus).update();
    }

    @Override
    public void updateStatus(Long logId, String processStatus, String errorMessage) {
        this.lambdaUpdate().eq(PaypalEventLog::getId, logId)
                .eq(PaypalEventLog::getProcessStatus, "not_process")
                .set(PaypalEventLog::getProcessStatus, processStatus)
                .set(PaypalEventLog::getErrorMessage, errorMessage)
                .update();
    }
}