package com.lx.pl.pay.paypal.service.strategy;

import cn.hutool.core.util.StrUtil;
import com.lx.pl.pay.common.service.PayLumenRecordService;
import com.lx.pl.pay.paypal.annotation.PaypalEvent;
import com.lx.pl.pay.paypal.model.PaymentCaptureDetails;
import com.lx.pl.pay.paypal.model.domain.PayPalOrderPaymentRecord;
import com.lx.pl.pay.paypal.model.event.PaypalPaymentCaptureEvent;
import com.lx.pl.pay.paypal.service.IPaypalEventHandler;
import com.lx.pl.pay.paypal.service.PayPalOrderPaymentRecordService;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 *
 */
@Component
@PaypalEvent(eventType = {"PAYMENT.CAPTURE.COMPLETED"})
public class PaypalPaymentCaptureHandler extends IPaypalEventHandler<PaypalPaymentCaptureEvent> {

    @Autowired
    private PayLumenRecordService payLumenRecordService;
    @Autowired
    private PayPalOrderPaymentRecordService paymentOrderPaymentRecordService;

    @Override
    public void handleEvent(PaypalPaymentCaptureEvent data) {
        PaymentCaptureDetails captureModel = data.getModel();
        RLock lock = redissonClient.getLock(PAYPAL_ACTION_LOCK_PREFIX + captureModel.getId());
        String loginName = null;
        try {
            lock.lock();
            loginName = applicationContext.getBean(PaypalPaymentCaptureHandler.class).doHandleEvent(data);
            if (StrUtil.isNotBlank(loginName)) {
                vipService.resettingPersonalLumens(loginName);
            }
        } finally {
            unlockAndRefreshVip(lock, loginName);
        }
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public String doHandleEvent(PaypalPaymentCaptureEvent data) {
        log.info("PAYMENT.CAPTURE.COMPLETED: {}", data.getModel().toJSON());
        PaymentCaptureDetails model = data.getModel();
        if ("COMPLETED".equals(model.getStatus())) {
            PaymentCaptureDetails.RelatedIds relatedIds = model.getSupplementaryData().getRelatedIds();
            List<PayPalOrderPaymentRecord> byorderIds = paymentOrderPaymentRecordService.findByPaymentId(relatedIds.getOrderId());
            PaymentCaptureDetails.SellerReceivableBreakdown sellerReceivableBreakdown = model.getSellerReceivableBreakdown();
            paymentOrderPaymentRecordService.lambdaUpdate()
                    .eq(PayPalOrderPaymentRecord::getOrderId, byorderIds.get(0).getOrderId())
                    .set(sellerReceivableBreakdown != null && sellerReceivableBreakdown.getPaypalFee() != null, PayPalOrderPaymentRecord::getFee, sellerReceivableBreakdown.getPaypalFee().getValue())
                    .set(sellerReceivableBreakdown != null && sellerReceivableBreakdown.getNetAmount() != null, PayPalOrderPaymentRecord::getNetAmount, sellerReceivableBreakdown.getNetAmount()
                            .getValue())
                    .update();
            payLumenRecordService.saveOneTimeLumenForPaypal(byorderIds);
            return byorderIds.get(0).getLoginName();
        }
//        PayPalOrderPaymentRecord captureModel = payPalOrderPaymentRecordService.createPayment(data.getModel());
//        if (captureModel == null)
//            return null;
//        String loginName = captureModel.getLoginName();
//        return loginName;
        return null;
    }
}
