package com.lx.pl.pay.paypal.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lx.pl.pay.paypal.model.domain.PayPalOrderPaymentItem;

import java.util.List;

/**
 * PayPal订单支付记录item服务接口
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
public interface PayPalOrderPaymentItemService extends IService<PayPalOrderPaymentItem> {

    /**
     * 根据订单ID查询支付记录项
     * 
     * @param orderId 订单ID
     * @return 支付记录项列表
     */
    List<PayPalOrderPaymentItem> findByOrderId(String orderId);


    /**
     * 根据用户ID查询支付记录项
     * 
     * @param userId 用户ID
     * @return 支付记录项列表
     */
    List<PayPalOrderPaymentItem> findByUserId(Long userId);

    /**
     * 根据用户登录名查询支付记录项
     * 
     * @param loginName 用户登录名
     * @return 支付记录项列表
     */
    List<PayPalOrderPaymentItem> findByLoginName(String loginName);

    /**
     * 根据产品ID查询支付记录项
     * 
     * @param productId 产品ID
     * @return 支付记录项列表
     */
    List<PayPalOrderPaymentItem> findByProductId(String productId);

    /**
     * 根据支付状态查询支付记录项
     * 
     * @param status 支付状态
     * @return 支付记录项列表
     */
    List<PayPalOrderPaymentItem> findByStatus(String status);

    /**
     * 更新支付状态
     * 
     * @param paymentId 支付ID
     * @param status 新状态
     * @return 是否更新成功
     */
    boolean updateStatusByPaymentId(String paymentId, String status);
}
