package com.lx.pl.pay.paypal.model.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.lx.pl.db.mysql.gen.entity.MyBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@TableName("paypal_event_log")
@Data
public class PaypalEventLog extends MyBaseEntity {

    private static final long serialVersionUID = -2786969068218416423L;
    private Long id;

    private Boolean verified;

    private String webhookId;

    private String webhookCreateTime;

    private String resourceType;

    private String resourceVersion;

    private String eventType;

    private String summary;

    private String status;

    private String errorMessage;

    private String processStatus;

    private String resource;

    private String eventVersion;


}
