package com.lx.pl.pay.paypal.enums;

import lombok.Getter;

@Getter
public enum PaypalSubscriptonStatusEnum {
    /**
     * APPROVAL_PENDING	The subscription is created but not yet approved by the buyer.
     * APPROVED	The buyer has approved the subscription.
     * ACTIVE	The subscription is active.
     * SUSPENDED	The subscription is suspended.
     * CANCELLED	The subscription is cancelled.
     * EXPIRED	The subscription is expired.
     */
    APPROVAL_PENDING("APPROVAL_PENDING", "The subscription is created but not yet approved by the buyer."),
    APPROVED("APPROVED", "The buyer has approved the subscription."),
    ACTIVE("ACTIVE", "The subscription is active."),
    SUSPENDED("SUSPENDED", "The subscription is suspended."),
    CANCELLED("CANCELLED", "The subscription is cancelled."),
    EXPIRED("EXPIRED", "The subscription is expired.");

    private final String status;

    private final String desc;


    PaypalSubscriptonStatusEnum(String status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public static PaypalSubscriptonStatusEnum getByStatus(String status) {
        for (PaypalSubscriptonStatusEnum paypalSubscriptonStatusEnum : PaypalSubscriptonStatusEnum.values()) {
            if (paypalSubscriptonStatusEnum.status.equals(status)) {
                return paypalSubscriptonStatusEnum;
            }
        }
        return null;
    }


}
