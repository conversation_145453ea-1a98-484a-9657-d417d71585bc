package com.lx.pl.pay.paypal.model.vo;

import com.lx.pl.pay.stripe.dto.PaymentType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/7/17 17:04
 */
@Data
public class ProductItem {
    //plan, one 不能为null
    @Schema(description = "订阅：plan，一次性购买：one")
    private PaymentType type;
    //standard，pro
    @Schema(description = "产品等级: standard pro")
    private String product;
    // year, month
    @Schema(description = "价格间隔：year, month")
    private String priceInterval;

    @Schema(description = "lumen 数量：当type为one 必填：例如500， 1000")
    private Integer lumen;

    @Schema(description = "购买数量：当type为one 必填：例如1， 2")
    private Integer qty;
}