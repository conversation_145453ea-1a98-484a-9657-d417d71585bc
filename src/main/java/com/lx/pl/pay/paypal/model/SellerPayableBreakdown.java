package com.lx.pl.pay.paypal.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.paypal.base.rest.PayPalResource;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SellerPayableBreakdown extends PayPalResource {

    private GrossAmount grossAmount;
    private PayPalFee paypalFee;
    private List<PlatformFee> platformFees;
    private NetAmount netAmount;
    private TotalRefundedAmount totalRefundedAmount;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class GrossAmount extends PayPalResource {
        private String value;
        private String currencyCode;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class PayPalFee extends PayPalResource {
        private String value;
        private String currencyCode;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class PlatformFee extends PayPalResource {
        private Amount amount;
        private Payee payee;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Amount extends PayPalResource {
        private String value;
        private String currencyCode;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Payee extends PayPalResource {
        private String emailAddress;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class NetAmount extends PayPalResource {
        private String value;
        private String currencyCode;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TotalRefundedAmount extends PayPalResource {
        private String value;
        private String currencyCode;
    }
}