package com.lx.pl.pay.paypal.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lx.pl.pay.common.domain.PayLumenRecord;
import com.lx.pl.pay.common.service.PayLumenRecordService;
import com.lx.pl.pay.paypal.mapper.PayPalRefundRecordMapper;
import com.lx.pl.pay.paypal.model.PaymentCaptureDetails;
import com.lx.pl.pay.paypal.model.PaypalCheckoutOrderModel;
import com.lx.pl.pay.paypal.model.PaypalRefundModel;
import com.lx.pl.pay.paypal.model.domain.PayPalLogicSubscription;
import com.lx.pl.pay.paypal.model.domain.PayPalRefundRecord;
import com.lx.pl.pay.paypal.model.domain.PayPalSubPaymentRecord;
import com.lx.pl.pay.paypal.service.PayPalRefundRecordService;
import com.lx.pl.pay.paypal.service.PayPalSubPaymentRecordService;
import com.paypal.base.rest.APIContext;
import com.paypal.base.rest.PayPalRESTException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.List;

@Service
public class PayPalRefundRecordServiceImpl extends ServiceImpl<PayPalRefundRecordMapper, PayPalRefundRecord> implements PayPalRefundRecordService {

    @Autowired
    private PayLumenRecordService payLumenRecordService;
    @Autowired
    private PayPalSubPaymentRecordService payPalSubPaymentRecordService;

    @Autowired
    private APIContext apiContext;
    Logger log = LoggerFactory.getLogger("paypal-pay-msg");

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PayPalRefundRecord calculateAndDoRefundAmount(PayPalLogicSubscription validSubscription, long timeSeconds) throws Exception {
//        List<PayLumenRecord> lumenRecordList = payLumenRecordService.queryByLogicId(validSubscription.getId());
        BigDecimal percent = caculatePercent(null, validSubscription, timeSeconds);
        log.info("calculateAndDoRefundAmount percent: {} {}", percent, validSubscription.getPaymentId());
        PaymentCaptureDetails detail = PaymentCaptureDetails.detail(apiContext, validSubscription.getPaymentId());
        if (detail != null && "REFUNDED".equalsIgnoreCase(detail.getStatus())) {
            return null;
        }
        PayPalRefundRecord refund = this.refund(validSubscription.getPaymentId(), percent);
        if (refund != null) {
            payPalSubPaymentRecordService.updateRefundId(validSubscription.getPaymentId(), refund.getRefundId());
        }
        return refund;
    }

    @Override
    public PayPalRefundRecord findByRefundId(String refundId) {
        if (refundId != null) {
            return this.lambdaQuery().eq(PayPalRefundRecord::getRefundId, refundId).one();
        }
        return null;
    }

    @Override
    public void updateRecord(String refundId, PaypalRefundModel model, Long id) {
        if (refundId != null) {
            PayPalRefundRecord convert = model.convert();
            convert.setRefundId(refundId);
            this.lambdaUpdate().eq(PayPalRefundRecord::getRefundId, refundId)
                    .set(PayPalRefundRecord::getStatus, convert.getStatus())
                    .set(PayPalRefundRecord::getRefundReasonCode, convert.getRefundReasonCode())
                    .set(PayPalRefundRecord::getCurrency, convert.getCurrency())
                    .set(PayPalRefundRecord::getTotal, convert.getTotal())
                    .set(PayPalRefundRecord::getNetAmount, convert.getNetAmount())
                    .set(PayPalRefundRecord::getGrossAmount, convert.getGrossAmount())
                    .set(PayPalRefundRecord::getPaypalFee, convert.getPaypalFee())
                    .set(PayPalRefundRecord::getTotalRefundedAmount, convert.getTotalRefundedAmount())
                    .set(PayPalRefundRecord::getUpdateTime, convert.getUpdateTime())
                    .set(PayPalRefundRecord::getUpdateBy, convert.getUpdateBy())
                    .update();
        }
    }

    @Override
    public PayPalRefundRecord findByPaymentId(String paymentId) {
        return this.lambdaQuery().eq(PayPalRefundRecord::getPaymentId, paymentId).one();
    }

    private PayPalRefundRecord refund(String paymentId, BigDecimal percent) {

        List<PayPalRefundRecord> completed = this.lambdaQuery().eq(PayPalRefundRecord::getPaymentId, paymentId).eq(PayPalRefundRecord::getStatus, "completed").list();
        // 累加total字段
        BigDecimal totalRefunded = completed.stream().map(record -> {
            try {
                return new BigDecimal(record.getTotal());
            } catch (NumberFormatException e) {
                log.error("Invalid total value: {}", record.getTotal(), e);
                return BigDecimal.ZERO;
            }
        }).reduce(BigDecimal.ZERO, BigDecimal::add);
        PayPalSubPaymentRecord payPalSubPaymentRecord = payPalSubPaymentRecordService.queryByPaymentId(paymentId);
        BigDecimal srcTotalExecludeFee = new BigDecimal(payPalSubPaymentRecord.getTotal()).subtract(new BigDecimal(payPalSubPaymentRecord.getFee()));
        BigDecimal left = srcTotalExecludeFee.subtract(totalRefunded).compareTo(BigDecimal.ZERO) > 0 ? srcTotalExecludeFee.subtract(totalRefunded) : BigDecimal.ZERO;
        if (left.compareTo(BigDecimal.ZERO) == 0) {
            return null;
        }
        PaypalCheckoutOrderModel.Amount amount = new PaypalCheckoutOrderModel.Amount();
        amount.setCurrencyCode("USD");
        BigDecimal needRefund = left.multiply(percent).setScale(2, RoundingMode.HALF_UP);
        amount.setValue(needRefund.toString());
        PaypalRefundModel payPalRefundRecord = new PaypalRefundModel();
        payPalRefundRecord.setId(paymentId);
        payPalRefundRecord.setAmount(amount);
        try {
            log.info("refund: {}", payPalRefundRecord.toJSON());
            if (needRefund.compareTo(BigDecimal.ZERO)> 0) {
                PaypalRefundModel refund = PaypalRefundModel.refund(apiContext, payPalRefundRecord);
                PayPalRefundRecord record = refund.convert();
                record.setTotal(amount.getValue());
                record.setPaymentId(paymentId);
                record.setSaleId(paymentId);
                record.setRefundId(refund.getId());
                record.setCreateTime(LocalDateTime.now());
                this.save(record);
                log.info("refund4: {}", record);
                return record;
            } else {
                log.info("refund amount is 0");
                return null;
            }
        } catch (PayPalRESTException e) {
            throw new RuntimeException(e);
        }
    }

    private BigDecimal caculatePercent(List<PayLumenRecord> lumenRecordList, PayPalLogicSubscription validSubscription, Long timeSeconds) {
        Long startTimeSec = validSubscription.getSubStartSec();
        Long nextBillingSec = validSubscription.getNextBillingSec();
        long newSubActiveSec = timeSeconds;
        double timeRatio = 0.0;
        if (nextBillingSec > startTimeSec) {
            timeRatio = (double) (nextBillingSec - newSubActiveSec) / (nextBillingSec - startTimeSec);
        }

        // 确保时间比例在0到1之间
        timeRatio = Math.max(0.0, Math.min(1.0, timeRatio));
        return new BigDecimal(timeRatio);
    }
}