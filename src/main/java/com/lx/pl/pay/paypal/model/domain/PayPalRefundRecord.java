package com.lx.pl.pay.paypal.model.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.lx.pl.db.mysql.gen.entity.MyBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("paypal_refund_record")
public class PayPalRefundRecord extends MyBaseEntity {

    private Long id;
    private String refundId;  // 退款ID

    private String paymentId;  // 支付ID

    private String status;  // 退款状态

    private String refundReasonCode;  // 退款原因代码

    private String saleId;  // 关联的交易ID（paymentId）

    private String currency;  // 货币类型

    private String total;  // 退款总额

    private String netAmount;  // 退款到账金额

    private String grossAmount;  // 退款来源金额

    private String paypalFee;  // 退款手续费

    private String totalRefundedAmount;  // 已退款总金额

    /**
     * note_to_payer
     */
    private String noteToPayer;

    private String link;
}
