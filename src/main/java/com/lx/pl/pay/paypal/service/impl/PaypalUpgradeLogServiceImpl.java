package com.lx.pl.pay.paypal.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.pay.paypal.mapper.PaypalUpgradeLogMapper;
import com.lx.pl.pay.paypal.model.PaypalSubscriptionModel;
import com.lx.pl.pay.paypal.model.domain.PaypalUpgradeLog;
import com.lx.pl.pay.paypal.service.PaypalUpgradeLogService;
import com.lx.pl.service.UserService;
import com.paypal.base.rest.APIContext;
import com.paypal.base.rest.PayPalRESTException;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class PaypalUpgradeLogServiceImpl extends ServiceImpl<PaypalUpgradeLogMapper, PaypalUpgradeLog> implements PaypalUpgradeLogService {

    @Resource
    private ApplicationContext applicationContext;
    @Resource
    private APIContext apiContext;
    @Resource
    private UserService userService;

    public List<PaypalUpgradeLog> queryValidNotHandleSubscriptions() {
        return this.lambdaQuery()
                .eq(PaypalUpgradeLog::getHasRefund, false)
                .isNotNull(PaypalUpgradeLog::getNewSubActiveTime)
                .list();
    }

    @Override
    public void handleCancelSubscription() {
        List<PaypalUpgradeLog> paypalUpgradeLogs = this.queryValidNotHandleSubscriptions();
        if (CollUtil.isEmpty(paypalUpgradeLogs)) {
            return;
        }
        paypalUpgradeLogs.forEach(paypalUpgradeLog -> {
            try {
                try {
                    PaypalSubscriptionModel subscriptionModelnEW = PaypalSubscriptionModel.detail(apiContext, paypalUpgradeLog.getNewSubscriptionId());
                    User userById = userService.getUserById(paypalUpgradeLog.getUserId());
                    applicationContext.getBean(PayPalLogicSubscriptionServiceImpl.class)
                            .doHandleCancel(paypalUpgradeLog, subscriptionModelnEW, userById);
                } catch (PayPalRESTException e) {
                    log.error("Failed to get subscription detail", e);
                    throw new RuntimeException(e);
                }

            } catch (Exception e) {
                log.error("handleCancelSubscription error", e);
            }
        });
    }
}