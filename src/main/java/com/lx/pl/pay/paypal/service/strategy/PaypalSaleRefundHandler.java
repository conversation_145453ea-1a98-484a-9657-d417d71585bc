package com.lx.pl.pay.paypal.service.strategy;

import com.lx.pl.pay.paypal.annotation.PaypalEvent;
import com.lx.pl.pay.paypal.model.PaypalRefundModel;
import com.lx.pl.pay.paypal.model.domain.PayPalRefundRecord;
import com.lx.pl.pay.paypal.model.event.PayPalRefundEvent;
import com.lx.pl.pay.paypal.service.IPaypalEventHandler;
import org.redisson.api.RLock;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * PAYMENT.SALE.REFUNDED	A merchant refunds a sale.
 * PAYMENT.SALE.REVERSED	A payment is reversed on a subscription.
 */
@Component
@PaypalEvent(eventType = {"PAYMENT.CAPTURE.REFUNDED", "PAYMENT.SALE.REVERSED"})
public class PaypalSaleRefundHandler extends IPaypalEventHandler<PayPalRefundEvent> {
    @Override
    public void handleEvent(PayPalRefundEvent data) {
        RLock lock = redissonClient.getLock(PAYPAL_ACTION_LOCK_PREFIX + data.getModel().getId());

        try {
            lock.lock();
            this.applicationContext.getBean(PaypalSaleRefundHandler.class).doHandleEvent(data);
        } finally {
            unlockAndRefreshVip(lock, null);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public String doHandleEvent(PayPalRefundEvent data) {
        PaypalRefundModel model = data.getModel();
        String refundId = model.getId();
        PayPalRefundRecord payPalRefundRecord = payPalRefundRecordService.findByRefundId(refundId);
        if (payPalRefundRecord != null) {
            payPalRefundRecordService.updateRecord(refundId, model, payPalRefundRecord.getId());
            return null;
        } else {
            PayPalRefundRecord record = model.convert();
            record.setRefundId(model.getId());
            record.setCreateTime(LocalDateTime.now());
            record.setLink(model.getLinks().toString());
            payPalRefundRecordService.save(record);
        }
        return null;
    }
}
