package com.lx.pl.pay.paypal.controller;

import com.lx.pl.pay.paypal.service.PaypalService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.Enumeration;


@RestController
@RequestMapping("/api/paypal")
@Tag(name = "paypal回调")
public class PayPalWebhookController {
    Logger log = LoggerFactory.getLogger("paypal-pay-msg");

    @Resource
    private PaypalService paypalService;

    @PostMapping("/webhook")
    public ResponseEntity<String> webhook(HttpServletRequest request, @RequestBody byte[] payload) {
        try {
            Enumeration<String> headerNames = request.getHeaderNames();
            while (headerNames.hasMoreElements()) {
                String headerName = headerNames.nextElement();
                // 获取对应的请求头值
                String headerValue = request.getHeader(headerName);
                log.info("headerName: {}, headerValue: {}", headerName, headerValue);
            }
        } catch (Exception e) {
            log.error("error: ", e);
        }
        try {

            paypalService.constructEvent(request, payload);
        } catch (Exception e) {
            log.error("customerCallback error: ", e);
            // 返回合适的响应
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Webhook Error: " + e.getMessage() + " \n" + Arrays.toString(e.getStackTrace()));
        }
        return ResponseEntity.ok("Event processed successfully.");
    }

}
