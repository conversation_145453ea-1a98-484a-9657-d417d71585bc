package com.lx.pl.pay.paypal.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lx.pl.pay.paypal.model.PaypalCheckoutOrderModel;
import com.lx.pl.pay.paypal.model.domain.PayPalOrderPaymentRecord;

import java.util.List;

public interface PayPalOrderPaymentRecordService extends IService<PayPalOrderPaymentRecord> {
    List<PayPalOrderPaymentRecord>  createPayment(PaypalCheckoutOrderModel data);

    List<PayPalOrderPaymentRecord> findByPaymentId(String paymentId);
}
