package com.lx.pl.pay.paypal.util;

import java.io.Serializable;

/**
 * <AUTHOR> 封装授权Token上下文对象，基于OAuth认证标准
 * @version 1.0
 * @date 2023/9/12 15:55
 */
public class TokenContext implements Serializable {

    /**
     * Access token宽限时长（单位/s），防止拿到token后，再去调用刚好失效了
     **/
    private long graceTime = 20;

    /**
     * Access token，用于访问Rest api
     **/
    private String accessToken;

    /**
     * expire，access token过期时间，当OAuth2.0返回时间+当前时间（单位/s）
     **/
    private long expire = 0;

    /**
     * Token范围，常见的：Bearer
     **/
    private String tokenType;

    /**
     * App id，可自定义
     **/
    private String appId;

    /**
     * paypal请求域名(生产或者沙盒)
     * 示例：https://api-m.sandbox.paypal.com
     **/
    private String domainUrl;

    /**
     * 计算过期时长还剩多少并减去宽限时长(时间必须统一)
     *
     * @return
     */
    public long expiresIn() {
        return expire - System.currentTimeMillis() / 1000 - graceTime;
    }
}
