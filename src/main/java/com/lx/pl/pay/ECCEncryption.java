package com.ai.pay;

import org.bouncycastle.jce.provider.BouncyCastleProvider;

import javax.crypto.Cipher;
import java.net.URL;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.security.*;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

/**
 * <AUTHOR>
 */
public class ECCEncryption {

    private static final String PUBLIC_KEY = "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEmojq+Tvxoh3p498vLOqWfuGSp8MCJ+9N6cA57wNDSN1LML01lvAQp4ZvGfaPnPnKiMM0NRU6o32iqclNIi9hKg==";

    private static final String PRIVATE_KEY = "MIGTAgEAMBMGByqGSM49AgEGCCqGSM49AwEHBHkwdwIBAQQgquHbrvgcCFFxWxBnqChuGRIyH1NjH9UPTiZCEThY2O6gCgYIKoZIzj0DAQehRANCAASaiOr5O/GiHenj3y8s6pZ+4ZKnwwIn703pwDnvA0NI3UswvTWW8BCnhm8Z9o+c+cqIwzQ1FTqjfaKpyU0iL2Eq";

    static  {
        Security.addProvider(new BouncyCastleProvider());
    }

    // Encrypt using public key
    public static String encrypt(String plainText) throws Exception {
        Cipher cipher = Cipher.getInstance("ECIES", "BC");
        cipher.init(Cipher.ENCRYPT_MODE, base64ToPublicKey());
        byte[] encryptedBytes = cipher.doFinal(plainText.getBytes());
        return Base64.getEncoder().encodeToString(encryptedBytes);
    }

    // Decrypt using private key
    public static String decrypt(String encryptedText) throws Exception {
        Cipher cipher = Cipher.getInstance("ECIES", "BC");
        cipher.init(Cipher.DECRYPT_MODE, base64ToPrivateKey());
        byte[] encryptedBytes = Base64.getDecoder().decode(encryptedText);
        byte[] decryptedBytes = cipher.doFinal(encryptedBytes);
        return new String(decryptedBytes);
    }


    private static PublicKey base64ToPublicKey() throws Exception {
        byte[] decodedKey = Base64.getDecoder().decode(ECCEncryption.PUBLIC_KEY);
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(decodedKey);
        KeyFactory keyFactory = KeyFactory.getInstance("EC", "BC");
        return keyFactory.generatePublic(keySpec);
    }

    // Convert Base64 string to PrivateKey
    private static PrivateKey base64ToPrivateKey() throws Exception {
        byte[] decodedKey = Base64.getDecoder().decode(ECCEncryption.PRIVATE_KEY);
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(decodedKey);
        KeyFactory keyFactory = KeyFactory.getInstance("EC", "BC");
        return keyFactory.generatePrivate(keySpec);
    }


    /**
     * @param args
     * @throws Exception
     */

    public static void main(String[] args) throws Exception {
        // // 添加BouncyCastle作为提供者
        // Security.addProvider(new BouncyCastleProvider());
        //
        // // 生成ECC密钥对
        // KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("EC", "BC");
        // keyPairGenerator.initialize(256); // 256位椭圆曲线
        // KeyPair keyPair = keyPairGenerator.generateKeyPair();
        // // // 将密钥转换为 Base64 编码格式并打印
        // System.out.println("Public Key (Base64): " + Base64.getEncoder().encodeToString(keyPair.getPublic().getEncoded()));
        // System.out.println("Private Key (Base64): " + Base64.getEncoder().encodeToString(keyPair.getPrivate().getEncoded()));
        //
        // // // 公钥加密
        // Cipher cipher = Cipher.getInstance("ECIES", "BC");
        // cipher.init(Cipher.ENCRYPT_MODE, keyPair.getPublic());
        // byte[] ciphertext = cipher.doFinal("123456789123456789011".getBytes());
        String encrypt = encrypt("12345678912666656789011");
        System.out.println("加密后的数据: " + URLEncoder.encode("BHD/lkeX+wlW/wKJ0/ljctwk13oJySeKJ9IJxnoGexVzzRqe7UwXN+L5U0lvQZL3t7ygY8orMRTvcqll9Hkv1byFkRK9mAwvkqn8TW4fSPogmFSMeCPwE2zYZRR5OoLgO1Bdc56S+ooIUcHF", "utf-8"));
        // 私钥解密
        System.out.println(URLDecoder.decode("BHD/lkeX+wlW/wKJ0/ljctwk13oJySeKJ9IJxnoGexVzzRqe7UwXN+L5U0lvQZL3t7ygY8orMRTvcqll9Hkv1byFkRK9mAwvkqn8TW4fSPogmFSMeCPwE2zYZRR5OoLgO1Bdc56S+ooIUcHF", "utf-8"));
        System.out.println("解密后的数据: " + decrypt(""));
    }
}
