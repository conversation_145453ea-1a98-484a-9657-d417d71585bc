package com.ai.pay;

import org.bouncycastle.jce.provider.BouncyCastleProvider;

import javax.crypto.Cipher;
import java.security.*;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

/**
 *
 * <AUTHOR>
 */
public class RSAEncryption {
    private static final String PUBLIC_KEY = "MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAJLbPidOtMRTCGUDBbfMt3YQT+pfd22xyfdg26fQ1C5V1d1sERirBYfHa9fCA6/Lm78L7NJl8YCMd/2nvPi312sCAwEAAQ==";

    private static final String PRIVATE_KEY = "MIIBUwIBADANBgkqhkiG9w0BAQEFAASCAT0wggE5AgEAAkEAkts+J060xFMIZQMFt8y3dhBP6l93bbHJ92Dbp9DULlXV3WwRGKsFh8dr18IDr8ubvwvs0mXxgIx3/ae8+LfXawIDAQABAkBPJLanSmRjFXiFr45KUppgl0PqRE7BoDn/BoT6OLHQ0f74Lu6tUYquyjIS0n3Dmtv9KuPFwaKwwzwJrw7AdlohAiEA6LZFujmTb3n4Y/czv/QaGdss6FfxCnxVKesihOiKNVsCIQChjXsbuzvMWvAc1sJgA9ooioQU631fUdDpJ+qcee+zMQIgN7+SrK4mV8pMawZJa2664f7Tn0WSBQUMEeK2fkiG84kCIFQ9Ds12Awda/8s53J7uQ0pSd+7koDoc0l9RNg+jsW5xAiBFxDe5Zr9+kfvVVcQcqXrMq49KnowTwhF/QdadkAmKeg==";
    public static String decrypt(byte[] cipherText) throws Exception {
        // 创建 Cipher 对象
        Cipher cipher = Cipher.getInstance("RSA");
        // 初始化 Cipher，指定为解密模式并使用私钥
        cipher.init(Cipher.DECRYPT_MODE, base64ToPrivateKey());
        // 解密数据
        byte[] decryptedBytes = cipher.doFinal(cipherText);
        return new String(decryptedBytes);
    }

    public static byte[] encrypt(String plainText) throws Exception {
        // 创建 Cipher 对象
        Cipher cipher = Cipher.getInstance("RSA");
        // 初始化 Cipher，指定为加密模式并使用公钥
        cipher.init(Cipher.ENCRYPT_MODE, base64ToPublicKey());
        // 将明文加密
        return cipher.doFinal(plainText.getBytes());
    }

    private static PublicKey base64ToPublicKey() throws Exception {
        byte[] decodedKey = Base64.getDecoder().decode(RSAEncryption.PUBLIC_KEY);
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(decodedKey);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        return keyFactory.generatePublic(keySpec);
    }

    // Convert Base64 string to PrivateKey
    private static PrivateKey base64ToPrivateKey() throws Exception {
        byte[] decodedKey = Base64.getDecoder().decode(RSAEncryption.PRIVATE_KEY);
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(decodedKey);
        Security.addProvider(new BouncyCastleProvider());
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        return keyFactory.generatePrivate(keySpec);
    }
    public static void main(String[] args) throws Exception {
        // 生成密钥对
        // KeyPairGenerator keyPairGen = KeyPairGenerator.getInstance("RSA");
        // keyPairGen.initialize(512);
        // KeyPair pair = keyPairGen.generateKeyPair();
        //
        // // 将密钥转换为 Base64 编码格式并打印
        // System.out.println("Public Key (Base64): " + Base64.getEncoder().encodeToString(pair.getPublic().getEncoded()));
        // System.out.println("Private Key (Base64): " + Base64.getEncoder().encodeToString(pair.getPrivate().getEncoded()));
        // 加密
        String originalText = "12345678912345678910";
        byte[] cipherText = encrypt(originalText);
        String encryptedBase64 = Base64.getEncoder().encodeToString(cipherText);
        System.out.println("加密后的数据: " + new String(cipherText));
        System.out.println("加密并转为 Base64 后的数据: " + encryptedBase64);

        // 解密
        // String decryptedText = RSADecrypt.decrypt(cipherText, pair.getPrivate());
        String decryptedText = decrypt(Base64.getDecoder().decode(encryptedBase64));
        System.out.println("解密后的数据: " + decryptedText);
    }
}
