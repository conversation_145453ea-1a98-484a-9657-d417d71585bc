package com.lx.pl.pay.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.util.List;

/**
 * 保存支付记录请求DTO
 */
@Data
@Schema(description = "保存支付记录请求")
public class SavePaymentRecordRequest {

    /**
     * 登录名称
     */
    @NotBlank(message = "登录名称不能为空")
    @Schema(description = "登录名称", required = true)
    private String loginName;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    @Schema(description = "用户ID", required = true)
    private Long userId;

    /**
     * 平台类型
     */
    @NotNull(message = "平台类型不能为空")
    @Min(value = 0, message = "平台类型必须大于等于0")
    @Max(value = 4, message = "平台类型必须小于等于4")
    @Schema(description = "平台类型：0-Backend, 1-Stripe, 2-PayPal, 3-Apple, 4-Google", required = true)
    private Integer platform;

    /**
     * 来源
     */
    @Schema(description = "来源")
    private String source;

    /**
     * 详情描述
     */
    @Schema(description = "详情描述，例如：Text to image")
    private String detail;

    /**
     * 优惠码
     */
    @Schema(description = "优惠码")
    private String couponCode;

    /**
     * 折扣百分比
     */
    @Min(value = 0, message = "折扣百分比不能小于0")
    @Max(value = 100, message = "折扣百分比不能大于100")
    @Schema(description = "折扣百分比")
    private Integer percentOff;

    /**
     * 用户购买价格
     */
    @NotNull(message = "购买价格不能为空")
    @DecimalMin(value = "0.0", message = "购买价格不能小于0")
    @Schema(description = "用户购买价格", required = true)
    private Long amount;

    private Long totalLumen;


    private Long amountExcludingTax;

    /**
     * 折扣总价
     */
    @Schema(description = "折扣总价")
    private Long afterDiscountAmount;

    private String currency;

    /**
     * 外部交易ID（用于幂等校验）
     */
    @NotBlank(message = "外部交易ID不能为空")
    @Schema(description = "外部交易ID（用于幂等校验）", required = true)
    private String externalTransactionId;

    /**
     * 外部订单号（辅助幂等校验）
     */
    @Schema(description = "外部订单号（辅助幂等校验）")
    private String externalOrderId;

    /**
     * 支付状态
     */
    @NotBlank(message = "支付状态不能为空")
    @Pattern(regexp = "^(pending|completed|failed|cancelled)$", message = "支付状态必须是：pending, completed, failed, cancelled")
    @Schema(description = "支付状态：pending, completed, failed, cancelled", required = true)
    private String paymentStatus;

    /**
     * 支付记录详情列表
     */
    @Valid
    @Schema(description = "支付记录详情列表")
    private List<PaymentRecordItemRequest> items;

    /**
     * 支付记录详情项
     */
    @Data
    @Schema(description = "支付记录详情项")
    public static class PaymentRecordItemRequest {

        /**
         * 产品类型
         */
        @NotBlank(message = "产品类型不能为空")
        @Pattern(regexp = "^(plan|one)$", message = "产品类型必须是：plan, one")
        @Schema(description = "产品类型：plan 计划, one 购买lumen", required = true)
        private String productType;

        /**
         * 计划等级
         */
        @Schema(description = "计划等级：standard/pro")
        private String planLevel;

        /**
         * 价格间隔
         */
        @Schema(description = "价格间隔：month/year")
        private String priceInterval;

        /**
         * 折扣百分比
         */
        @Min(value = 0, message = "折扣百分比不能小于0")
        @Max(value = 100, message = "折扣百分比不能大于100")
        @Schema(description = "折扣百分比")
        private Integer percentOff;

        /**
         * 单位Lumen数量
         */
        @Min(value = 0, message = "单位Lumen数量不能小于0")
        @Schema(description = "单位Lumen数量")
        private Integer unitLumen;


        private Integer giftLumen;

        /**
         * 总Lumen数量
         */
        @Min(value = 0, message = "总Lumen数量不能小于0")
        @Schema(description = "总Lumen数量")
        private Integer totalLumen;

        /**
         * 单位金额
         */
        @DecimalMin(value = "0.0", message = "单位金额不能小于0")
        @Schema(description = "单位金额")
        private Long unitAmount;

        private String currency;

        /**
         * 总金额
         */
        @NotNull(message = "总金额不能为空")
        @DecimalMin(value = "0.0", message = "总金额不能小于0")
        @Schema(description = "总金额", required = true)
        private Long totalAmount;

        @Schema(description = "总金额(排除税)")
        private Long totalAmountExcludingTax;

        /**
         * 购买数量
         */
        @NotNull(message = "购买数量不能为空")
        @Min(value = 1, message = "购买数量不能小于1")
        @Schema(description = "购买数量", required = true)
        private Integer qty;
    }
}
