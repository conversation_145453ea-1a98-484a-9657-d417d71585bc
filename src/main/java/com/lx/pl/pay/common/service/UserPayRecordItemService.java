package com.lx.pl.pay.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lx.pl.pay.common.domain.UserPayRecordItem;

import java.util.List;

/**
 * 用户支付记录详情服务接口
 */
public interface UserPayRecordItemService extends IService<UserPayRecordItem> {

    /**
     * 根据支付记录ID查询详情
     *
     * @param recordId 支付记录ID
     * @return 支付记录详情列表
     */
    List<UserPayRecordItem> getByRecordId(Long recordId);

    /**
     * 根据用户ID查询支付记录详情
     *
     * @param userId 用户ID
     * @return 支付记录详情列表
     */
    List<UserPayRecordItem> getByUserId(Long userId);

    /**
     * 根据登录名查询支付记录详情
     *
     * @param loginName 登录名
     * @return 支付记录详情列表
     */
    List<UserPayRecordItem> getByLoginName(String loginName);

    /**
     * 根据产品类型查询支付记录详情
     *
     * @param productType 产品类型
     * @return 支付记录详情列表
     */
    List<UserPayRecordItem> getByProductType(String productType);

    /**
     * 根据计划等级查询支付记录详情
     *
     * @param planLevel 计划等级
     * @return 支付记录详情列表
     */
    List<UserPayRecordItem> getByPlanLevel(String planLevel);
}
