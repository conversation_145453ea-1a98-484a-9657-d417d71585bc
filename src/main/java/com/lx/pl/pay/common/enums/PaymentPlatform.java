package com.lx.pl.pay.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 支付平台枚举
 */
@Getter
@AllArgsConstructor
public enum PaymentPlatform {

    /**
     * 后台管理
     */
    BACKEND(0, "Backend", "后台管理"),

    /**
     * Stripe支付
     */
    STRIPE(1, "Stripe", "Stripe支付"),

    /**
     * PayPal支付
     */
    PAYPAL(2, "PayPal", "PayPal支付"),

    /**
     * Apple支付
     */
    APPLE(3, "Apple", "Apple支付"),

    /**
     * Google支付
     */
    GOOGLE(4, "Google", "Google支付");

    /**
     * 平台代码
     */
    private final Integer code;

    /**
     * 平台名称（英文）
     */
    private final String name;

    /**
     * 平台描述（中文）
     */
    private final String description;

    /**
     * 代码到枚举的映射
     */
    private static final Map<Integer, PaymentPlatform> CODE_MAP = Arrays.stream(values())
            .collect(Collectors.toMap(PaymentPlatform::getCode, Function.identity()));

    /**
     * 名称到枚举的映射
     */
    private static final Map<String, PaymentPlatform> NAME_MAP = Arrays.stream(values())
            .collect(Collectors.toMap(PaymentPlatform::getName, Function.identity()));

    /**
     * 根据代码获取枚举
     *
     * @param code 平台代码
     * @return 枚举对象，如果不存在返回null
     */
    public static PaymentPlatform getByCode(Integer code) {
        return CODE_MAP.get(code);
    }

    /**
     * 根据名称获取枚举
     *
     * @param name 平台名称
     * @return 枚举对象，如果不存在返回null
     */
    public static PaymentPlatform getByName(String name) {
        return NAME_MAP.get(name);
    }

    /**
     * 根据代码获取平台名称
     *
     * @param code 平台代码
     * @return 平台名称，如果不存在返回"Unknown"
     */
    public static String getNameByCode(Integer code) {
        PaymentPlatform platform = getByCode(code);
        return platform != null ? platform.getName() : "Unknown";
    }

    /**
     * 根据代码获取平台描述
     *
     * @param code 平台代码
     * @return 平台描述，如果不存在返回"未知平台"
     */
    public static String getDescriptionByCode(Integer code) {
        PaymentPlatform platform = getByCode(code);
        return platform != null ? platform.getDescription() : "未知平台";
    }

    /**
     * 检查代码是否有效
     *
     * @param code 平台代码
     * @return 是否有效
     */
    public static boolean isValidCode(Integer code) {
        return CODE_MAP.containsKey(code);
    }

    /**
     * 检查名称是否有效
     *
     * @param name 平台名称
     * @return 是否有效
     */
    public static boolean isValidName(String name) {
        return NAME_MAP.containsKey(name);
    }

    /**
     * 获取所有平台代码
     *
     * @return 平台代码数组
     */
    public static Integer[] getAllCodes() {
        return Arrays.stream(values())
                .map(PaymentPlatform::getCode)
                .toArray(Integer[]::new);
    }

    /**
     * 获取所有平台名称
     *
     * @return 平台名称数组
     */
    public static String[] getAllNames() {
        return Arrays.stream(values())
                .map(PaymentPlatform::getName)
                .toArray(String[]::new);
    }
}
