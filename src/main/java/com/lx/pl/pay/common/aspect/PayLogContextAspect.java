package com.lx.pl.pay.common.aspect;

import com.lx.pl.pay.common.annotation.PayLogContext;
import com.lx.pl.pay.common.util.PayLogContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

@Aspect
@Component
@Slf4j
public class PayLogContextAspect {

    /**
     * 处理带有@PayLogContext注解的方法
     */
    @Around("@annotation(payLogContext)")
    public Object handlePayLogContext(ProceedingJoinPoint joinPoint, PayLogContext payLogContext) throws Throwable {
        try {
            // 初始化PayLogContext
            String prefix = payLogContext.value();
            PayLogContextHolder.init(prefix);
            log.debug("PayLogContext initialized for method: {}", joinPoint.getSignature().getName());
            // 执行原方法
            return joinPoint.proceed();
        } finally {
            // 清理PayLogContext
            PayLogContextHolder.remove();
            log.debug("PayLogContext removed for method: {}", joinPoint.getSignature().getName());
        }
    }
}