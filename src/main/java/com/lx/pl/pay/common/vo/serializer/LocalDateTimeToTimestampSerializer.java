package com.lx.pl.pay.common.vo.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;

/**
 * LocalDateTime 转 UTC 时间戳（秒）序列化器
 */
public class LocalDateTimeToTimestampSerializer extends JsonSerializer<LocalDateTime> {

    @Override
    public void serialize(LocalDateTime value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        if (value == null) {
            gen.writeNull();
        } else {
            // 将LocalDateTime转换为UTC时间戳（秒）
            // 假设LocalDateTime是基于系统默认时区（Asia/Shanghai）
            long timestamp = value.atZone(ZoneId.of("Asia/Shanghai"))
                    .withZoneSameInstant(ZoneOffset.UTC)
                    .toEpochSecond();
            gen.writeString(String.valueOf(timestamp));
        }
    }
}
