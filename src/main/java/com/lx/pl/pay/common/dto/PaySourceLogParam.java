package com.lx.pl.pay.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "日志参数")
public class PaySourceLogParam {

    @Schema(description = "来源")
    @NotEmpty(message = "source can't be null")
    private String source;

    @Schema(description = "支付渠道(stripe, ios_pay, google_pay, paypal)")
    @NotEmpty(message = "payment channel can't be null")
    private String paymentChannel;

    @Schema(description = "产品类型")
    @NotEmpty(message = "priceType channel can't be null")
    private String priceType;
}