package com.lx.pl.pay.common.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lx.pl.db.mysql.gen.entity.MyBaseEntity;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("pay_source_log")
public class PaySourceLog extends MyBaseEntity {

    @TableId
    private Long id;

    private Long userId;

    private String loginName;

    private String source;

    private String paymentChannel;

    private String priceType;
}