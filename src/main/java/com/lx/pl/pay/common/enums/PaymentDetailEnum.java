package com.lx.pl.pay.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 支付详情枚举
 */
@Getter
@AllArgsConstructor
public enum PaymentDetailEnum {

    /**
     * 标准月度订阅
     */
    STANDARD_MONTHLY("Standard Monthly", "标准月度订阅", "standard", "month", PaymentSourceEnum.SUBSCRIBE),

    /**
     * 标准年度订阅
     */
    STANDARD_YEARLY("Standard Yearly", "标准年度订阅", "standard", "year", PaymentSourceEnum.SUBSCRIBE),

    /**
     * 专业月度订阅
     */
    PRO_MONTHLY("Pro Monthly", "专业月度订阅", "pro", "month", PaymentSourceEnum.SUBSCRIBE),

    /**
     * 专业年度订阅
     */
    PRO_YEARLY("Pro Yearly", "专业年度订阅", "pro", "year", PaymentSourceEnum.SUBSCRIBE),

    /**
     * Lumens购买
     */
    LUMENS_PURCHASE("{%s} Lumens", "Lumens购买", null, null, PaymentSourceEnum.PURCHASE_LUMENS),

    /**
     * 平台支付
     */
    PLATFORM_PAYMENT("{%s}(Gift)", "平台支付", null, null, PaymentSourceEnum.PLATFORM);

    /**
     * 详情名称（英文）
     */
    private final String name;

    /**
     * 详情描述（中文）
     */
    private final String description;

    private final String planLevel;

    private final String priceInterval;

    /**
     * 所属来源
     */
    private final PaymentSourceEnum source;

    /**
     * 根据名称获取枚举
     *
     * @param name 详情名称
     * @return 枚举对象，如果不存在返回null
     */
    public static PaymentDetailEnum getByName(String name) {
        if (name == null) {
            return null;
        }
        for (PaymentDetailEnum detail : values()) {
            if (detail.getName().equalsIgnoreCase(name)) {
                return detail;
            }
        }
        return null;
    }

    /**
     * 根据来源获取详情列表
     *
     * @param source 来源枚举
     * @return 该来源下的所有详情枚举数组
     */
    public static PaymentDetailEnum[] getBySource(PaymentSourceEnum source) {
        if (source == null) {
            return new PaymentDetailEnum[0];
        }
        return java.util.Arrays.stream(values())
                .filter(detail -> detail.getSource() == source)
                .toArray(PaymentDetailEnum[]::new);
    }

    /**
     * 检查名称是否有效
     *
     * @param name 详情名称
     * @return 是否有效
     */
    public static boolean isValidName(String name) {
        return getByName(name) != null;
    }

    /**
     * 根据planlevel 和 priceInterval 获取详情枚举
     *
     * @param planLevel     计划等级
     * @param priceInterval 价格间隔
     * @return 详情枚举对象，如果不存在返回null
     */
    public static PaymentDetailEnum getByPlanLevelAndPriceInterval(String planLevel, String priceInterval) {
        if (planLevel == null || priceInterval == null) {
            return null;
        }
        for (PaymentDetailEnum detail : values()) {
            if (detail.getPlanLevel().equalsIgnoreCase(planLevel) && detail.getPriceInterval().equalsIgnoreCase(priceInterval)) {
                return detail;
            }
        }
        return null;
    }
}