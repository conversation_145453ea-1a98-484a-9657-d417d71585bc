package com.lx.pl.pay.common.util;


import org.slf4j.MDC;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.UUID;

import static com.lx.pl.interceptor.LoggingInterceptor.TRACE_ID;

/**
 * <AUTHOR>
 * @description pay日志上下文
 */
public class PayLogContextHolder {


    private static final ThreadLocal<String> PAY_LOG_CONTEXT = new ThreadLocal<>();

    public static void init(String prefix) {
        // 获取本机内网IP
        PAY_LOG_CONTEXT.set(prefix + MDC.get(TRACE_ID));
    }

    public static String getLogUUID() {
        if (PAY_LOG_CONTEXT.get() == null) {
            init("DEFAULT-");
        }
        return PAY_LOG_CONTEXT.get().toString();
    }

    public static void remove() {
        PAY_LOG_CONTEXT.remove();
    }
}
