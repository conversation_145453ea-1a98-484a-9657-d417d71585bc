package com.lx.pl.pay.common.controller;

import com.lx.pl.annotation.Authorization;
import com.lx.pl.annotation.CurrentUser;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.dto.CommPageInfo;
import com.lx.pl.dto.generic.R;
import com.lx.pl.pay.common.domain.UserPayRecord;
import com.lx.pl.pay.common.domain.UserPayRecordItem;
import com.lx.pl.pay.common.enums.PaymentPlatform;
import com.lx.pl.pay.common.service.UserPayRecordItemService;
import com.lx.pl.pay.common.service.UserPayRecordService;
import com.lx.pl.pay.common.vo.UserPayRecordVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户支付记录控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/user-pay-record")
@Tag(name = "用户支付记录", description = "用户支付记录管理")
public class UserPayRecordController {

    @Autowired
    private UserPayRecordService userPayRecordService;

    @Autowired
    private UserPayRecordItemService userPayRecordItemService;

    @Operation(summary = "获取当前用户支付记录")
    @GetMapping("/list")
    @Authorization
    public R<List<UserPayRecord>> getUserPayRecords(@CurrentUser @Parameter(hidden = true) User user) {
        List<UserPayRecord> records = userPayRecordService.getByUserId(user.getId());
        return R.success(records);
    }

    @Operation(summary = "根据平台获取当前用户支付记录")
    @GetMapping("/list/platform/{platform}")
    @Authorization
    public R<List<UserPayRecord>> getUserPayRecordsByPlatform(
            @PathVariable @Parameter(description = "平台类型：0-Backend, 1-Stripe, 2-PayPal, 3-Apple, 4-Google") Integer platform,
            @CurrentUser @Parameter(hidden = true) User user) {

        // 验证平台代码有效性
        if (!PaymentPlatform.isValidCode(platform)) {
            return R.fail(400, "Invalid platform code: " + platform);
        }

        List<UserPayRecord> records = userPayRecordService.getByUserIdAndPlatform(user.getId(), platform);
        return R.success(records);
    }

    @Operation(summary = "获取支付记录详情")
    @GetMapping("/{recordId}/items")
    @Authorization
    public R<List<UserPayRecordItem>> getPayRecordItems(
            @PathVariable @Parameter(description = "支付记录ID") Long recordId) {
        List<UserPayRecordItem> items = userPayRecordItemService.getByRecordId(recordId);
        return R.success(items);
    }

    @Operation(summary = "分页查询当前用户支付记录展示（游标分页）")
    @GetMapping("/page-search-display")
    @Authorization
    public R<CommPageInfo<UserPayRecordVO>> getUserPayRecordsDisplayWithCursor(
            @RequestParam(value = "lastId", required = false) @Parameter(description = "上一页最后一条记录的ID（游标）") Long lastId,
            @RequestParam("pageSize") @Parameter(description = "每页大小，最大50") Integer pageSize,
            @RequestParam(value = "platform", required = false) @Parameter(description = "平台类型：0-Backend, 1-Stripe, 2-PayPal, 3-Apple, 4-Google") Integer platform,
            @CurrentUser @Parameter(hidden = true) User user) {

        // 限制页面大小
        if (pageSize > 50) {
            return R.fail(400, "pageSize exceeds 50!");
        }

        // 验证平台代码有效性
        if (platform != null && !PaymentPlatform.isValidCode(platform)) {
            return R.fail(400, "Invalid platform code: " + platform);
        }

        CommPageInfo<UserPayRecordVO> pageInfo = userPayRecordService.getByUserIdWithCursorVO(
                user.getId(), lastId, pageSize, platform);
        return R.success(pageInfo);
    }
}
