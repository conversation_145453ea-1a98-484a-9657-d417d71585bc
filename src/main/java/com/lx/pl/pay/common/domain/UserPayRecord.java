package com.lx.pl.pay.common.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lx.pl.db.mysql.gen.entity.MyBaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 用户支付记录实体类
 */
@Data
@TableName("user_pay_record")
@Schema(description = "用户支付记录")
public class UserPayRecord extends MyBaseEntity {

    /**
     * 主键 ID
     */
    @Schema(description = "主键 ID")
    @TableId(value = "id")
    private Long id;

    /**
     * 登录名称
     */
    @Schema(description = "登录名称")
    private String loginName;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Long userId;

    /**
     * 平台类型：0 backend, 1 stripe, 2 paypal, 3 apple, 4 google
     */
    @Schema(description = "平台类型：0 backend, 1 stripe, 2 paypal, 3 apple, 4 google")
    private Integer platform;

    /**
     * 来源
     */
    @Schema(description = "来源")
    private String source;

    /**
     * 详情描述，例如：Text to image
     */
    @Schema(description = "详情描述，例如：Text to image")
    private String detail;

    /**
     * 优惠码
     */
    @Schema(description = "优惠码")
    private String couponCode;

    /**
     * 折扣百分比
     */
    @Schema(description = "折扣百分比")
    private Integer percentOff;

    /**
     * 用户购买价格
     */
    @Schema(description = "用户购买价格")
    private Long amount;


    private Long amountExcludingTax;

    /**
     * 折扣总价
     */
    @Schema(description = "折扣总价")
    private Long afterDiscountAmount;

    private String currency;

    /**
     * 外部交易ID（用于幂等校验）
     * Stripe: payment_intent_id 或 invoice_id
     * PayPal: order_id 或 subscription_id
     * Apple: original_transaction_id
     * Google: purchase_token
     * Backend: 自定义业务ID
     */
    @Schema(description = "外部交易ID（用于幂等校验）")
    private String externalTransactionId;

    /**
     * 外部订单号（辅助幂等校验）
     */
    @Schema(description = "外部订单号（辅助幂等校验）")
    private String externalOrderId;

    /**
     * 支付状态
     */
    @Schema(description = "支付状态：pending, completed, failed, cancelled")
    private String paymentStatus;
}
