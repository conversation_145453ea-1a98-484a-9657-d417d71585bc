package com.lx.pl.pay.common.controller;

import com.lx.pl.annotation.Authorization;
import com.lx.pl.annotation.CurrentUser;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.dto.generic.R;
import com.lx.pl.pay.common.domain.PaySourceLog;
import com.lx.pl.pay.common.dto.PaySourceLogParam;
import com.lx.pl.pay.common.service.PaySourceLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@RestController
@RequestMapping("api/pay/source/log")
@Tag(name = "支付来源日志")
public class PaySourceLogController {

    @Autowired
    private PaySourceLogService paySourceLogService;

    @PostMapping("/add")
    @Operation(summary = "保存支付来源日志")
    @Authorization
    public R<Boolean> save(@RequestBody @Valid PaySourceLogParam paySourceLogParam, @CurrentUser @Parameter(hidden = true, required = false) User user) {
        return R.success(paySourceLogService.saveLog(paySourceLogParam, user));
    }
}