package com.lx.pl.pay.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "当前订阅")
public class SubscriptionCurrentDto {

    /**
     * 订阅每周期开始时间
     */
    @Schema(description = "订阅每周期开始时间")
    private Long currentPeriodStart;

    /**
     * 订阅每周期结束时间
     */
    @Schema(description = "订阅每周期结束时间")
    private Long currentPeriodEnd;

    /**
     * 会员类型：standard 普通会员 pro 高级会员
     */
    @Schema(description = "会员类型： standard 普通会员 pro 高级会员")
    private String planLevel;

    /**
     * 订阅计费周期：year, month
     */
    @Schema(description = "订阅计费周期：year, month")
    private String priceInterval;

    /**
     * 会员生效时间
     */
    @Schema(description = "会员生效时间")
    private Long vipBeginTime;

    /**
     * 会员过期时间
     */
    @Schema(description = "会员过期时间")
    private Long vipEndTime;

    /**
     * 订阅来源平台：stripe，ios，android
     */
    @Schema(description = "订阅来源平台：stripe，ios，android")
    private String vipPlatform;

    /**
     * 自动续订状态（1 表示启用，0 表示关闭）
     */
    @Schema(description = "自动续订状态（1 表示启用，0 表示关闭）")
    private Integer autoRenewStatus;

    /**
     * 是否是试用
     */
    @Schema(description = "是否是试用")
    private Boolean trial;

    @Schema(description = "版本")
    private String mark;

    @Schema(description = "价格")
    private String renewPrice;

}
