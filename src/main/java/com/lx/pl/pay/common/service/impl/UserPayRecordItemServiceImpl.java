package com.lx.pl.pay.common.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lx.pl.pay.common.domain.UserPayRecordItem;
import com.lx.pl.pay.common.mapper.UserPayRecordItemMapper;
import com.lx.pl.pay.common.service.UserPayRecordItemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户支付记录详情服务实现类
 */
@Slf4j
@Service
public class UserPayRecordItemServiceImpl extends ServiceImpl<UserPayRecordItemMapper, UserPayRecordItem> implements UserPayRecordItemService {

    @Override
    public List<UserPayRecordItem> getByRecordId(Long recordId) {
        return this.lambdaQuery()
                .eq(UserPayRecordItem::getRecordId, recordId)
                .orderByDesc(UserPayRecordItem::getCreateTime)
                .list();
    }

    @Override
    public List<UserPayRecordItem> getByUserId(Long userId) {
        return this.lambdaQuery()
                .eq(UserPayRecordItem::getUserId, userId)
                .orderByDesc(UserPayRecordItem::getCreateTime)
                .list();
    }

    @Override
    public List<UserPayRecordItem> getByLoginName(String loginName) {
        return this.lambdaQuery()
                .eq(UserPayRecordItem::getLoginName, loginName)
                .orderByDesc(UserPayRecordItem::getCreateTime)
                .list();
    }

    @Override
    public List<UserPayRecordItem> getByProductType(String productType) {
        return this.lambdaQuery()
                .eq(UserPayRecordItem::getProductType, productType)
                .orderByDesc(UserPayRecordItem::getCreateTime)
                .list();
    }

    @Override
    public List<UserPayRecordItem> getByPlanLevel(String planLevel) {
        return this.lambdaQuery()
                .eq(UserPayRecordItem::getPlanLevel, planLevel)
                .orderByDesc(UserPayRecordItem::getCreateTime)
                .list();
    }
}
