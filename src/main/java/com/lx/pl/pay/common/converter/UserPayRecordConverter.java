package com.lx.pl.pay.common.converter;

import com.lx.pl.pay.common.domain.UserPayRecord;
import com.lx.pl.pay.common.domain.UserPayRecordItem;
import com.lx.pl.pay.common.enums.PaymentPlatform;
import com.lx.pl.pay.common.vo.UserPayRecordItemVo;
import com.lx.pl.pay.common.vo.UserPayRecordVO;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户支付记录转换器
 */
public class UserPayRecordConverter {

    // 移除硬编码的平台映射，使用枚举代替

    /**
     * 转换单个支付记录为VO
     *
     * @param record 支付记录
     * @param items  支付记录详情列表
     * @return VO对象
     */
    public static UserPayRecordVO toVO(UserPayRecord record, List<UserPayRecordItem> items) {
        if (record == null) {
            return null;
        }

        UserPayRecordVO vo = new UserPayRecordVO();
        vo.setId(record.getId());
        vo.setAmount(record.getAmount());
        vo.setTime(record.getCreateTime());
        vo.setPlatform(record.getPlatform());
        vo.setPlatformName(PaymentPlatform.getNameByCode(record.getPlatform()));
        vo.setHasDiscount(record.getPercentOff() != null && record.getPercentOff() > 0);
        vo.setPercentOff(record.getPercentOff());
        vo.setCouponCode(record.getCouponCode());
        vo.setDetail(record.getDetail());
        vo.setItems(new ArrayList<>());
        vo.setSourceName(record.getSource() != null ? record.getSource() : "Unknown");

        // 处理详情信息
        if (!CollectionUtils.isEmpty(items)) {
            for (UserPayRecordItem item : items) {
                UserPayRecordItemVo itemVo = new UserPayRecordItemVo();
                BeanUtils.copyProperties(item, itemVo);
                vo.getItems().add(itemVo);
            }
        }

        return vo;
    }

    /**
     * 转换支付记录列表为VO列表
     *
     * @param records  支付记录列表
     * @param itemsMap 记录ID到详情列表的映射
     * @return VO列表
     */
    public static List<UserPayRecordVO> toVOList(List<UserPayRecord> records, Map<Long, List<UserPayRecordItem>> itemsMap) {
        if (CollectionUtils.isEmpty(records)) {
            return List.of();
        }

        return records.stream()
                .map(record -> {
                    List<UserPayRecordItem> items = itemsMap.getOrDefault(record.getId(), List.of());
                    return toVO(record, items);
                })
                .collect(Collectors.toList());
    }

    /**
     * 生成来源名称
     */
    private static String generateSourceName(UserPayRecordItem item) {
        if ("plan".equals(item.getProductType())) {
            return "Subscribe";
        } else if ("one".equals(item.getProductType())) {
            return "Purchase Lumen";
        }
        return "Unknown";
    }

    /**
     * 生成详情描述
     */
    private static String generateDetails(UserPayRecordItem firstItem, List<UserPayRecordItem> items) {
        if ("plan".equals(firstItem.getProductType())) {
            // 订阅计划：Standard Monthly, Pro Yearly
            String level = firstItem.getPlanLevel() != null ?
                    capitalize(firstItem.getPlanLevel()) : "Standard";
            String interval = firstItem.getPriceInterval() != null ?
                    capitalize(firstItem.getPriceInterval()) + "ly" : "Monthly";
            return level + " " + interval;
        } else if ("one".equals(firstItem.getProductType())) {
            // 一次性购买：500 Lumens, 1000 Lumens
            Integer totalLumen = firstItem.getTotalLumen();
            if (totalLumen != null) {
                return totalLumen + " Lumens";
            }
            // 如果没有总数，计算所有项目的Lumen总和
            int sum = items.stream()
                    .mapToInt(item -> item.getTotalLumen() != null ? item.getTotalLumen() : 0)
                    .sum();
            return sum + " Lumens";
        }
        return "Unknown Product";
    }

    /**
     * 首字母大写
     */
    private static String capitalize(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        return str.substring(0, 1).toUpperCase() + str.substring(1).toLowerCase();
    }
}
