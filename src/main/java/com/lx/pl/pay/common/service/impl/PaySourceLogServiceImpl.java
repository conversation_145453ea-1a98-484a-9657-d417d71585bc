package com.lx.pl.pay.common.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.pay.common.domain.PaySourceLog;
import com.lx.pl.pay.common.dto.PaySourceLogParam;
import com.lx.pl.pay.common.mapper.PaySourceLogMapper;
import com.lx.pl.pay.common.service.PaySourceLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Service
@Slf4j
public class PaySourceLogServiceImpl extends ServiceImpl<PaySourceLogMapper, PaySourceLog> implements PaySourceLogService {
    @Override
    public Boolean saveLog(PaySourceLogParam paySourceLogParam, User user) {
        try {
            PaySourceLog paySourceLog = new PaySourceLog();
            paySourceLog.setSource(paySourceLogParam.getSource());
            paySourceLog.setPaymentChannel(paySourceLogParam.getPaymentChannel());
            paySourceLog.setPriceType(paySourceLogParam.getPriceType());
            if (user != null) {
                paySourceLog.setUserId(user.getId());
                paySourceLog.setLoginName(user.getLoginName());
            }
            paySourceLog.setCreateTime(LocalDateTime.now());
            return this.save(paySourceLog);
        } catch (Exception e) {
            log.error("保存支付来源日志失败，报错信息为：", e);
            return false;
        }
    }
}