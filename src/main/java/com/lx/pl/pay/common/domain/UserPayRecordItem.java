package com.lx.pl.pay.common.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lx.pl.db.mysql.gen.entity.MyBaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 用户支付记录详情实体类
 */
@Data
@TableName("user_pay_record_item")
@Schema(description = "用户支付记录详情")
public class UserPayRecordItem extends MyBaseEntity {

    /**
     * 主键 ID
     */
    @Schema(description = "主键 ID")
    @TableId(value = "id")
    private Long id;

    /**
     * 登录名称
     */
    @Schema(description = "登录名称")
    private String loginName;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Long userId;

    /**
     * 支付记录ID
     */
    @Schema(description = "支付记录ID")
    private Long recordId;

    /**
     * 平台类型：0 backend, 1 stripe, 2 paypal, 3 apple, 4 google
     */
    @Schema(description = "平台类型：0 backend, 1 stripe, 2 paypal, 3 apple, 4 google")
    private Integer platform;

    /**
     * 产品类型：plan 计划, one 购买lumen
     */
    @Schema(description = "产品类型：plan 计划, one 购买lumen")
    private String productType;

    /**
     * 计划等级：standard/pro
     */
    @Schema(description = "计划等级：standard/pro")
    private String planLevel;

    /**
     * 价格间隔：month/year
     */
    @Schema(description = "价格间隔：month/year")
    private String priceInterval;

    /**
     * 折扣百分比
     */
    @Schema(description = "折扣百分比")
    private Integer percentOff;

    /**
     * 单位Lumen数量
     */
    @Schema(description = "单位Lumen数量")
    private Integer unitLumen;

    /**
     * 总Lumen数量
     */
    @Schema(description = "总Lumen数量")
    private Integer totalLumen;

    @Schema(description = "总Lumen数量")
    private Integer giftLumen;

    @Schema(description = "总Lumen数量")
    private Integer totalLumenExcludingTax;

    /**
     * 单位金额
     */
    @Schema(description = "单位金额")
    private BigDecimal unitAmount;

    /**
     * 总金额
     */
    @Schema(description = "总金额")
    private BigDecimal totalAmount;

    /**
     * 购买数量
     */
    @Schema(description = "购买数量")
    private Integer qty;
}
