package com.lx.pl.pay.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lx.pl.pay.common.domain.UserPayRecord;
import com.lx.pl.pay.common.dto.SavePaymentRecordRequest;
import com.lx.pl.pay.common.enums.PaymentPlatform;
import com.lx.pl.pay.common.service.PaymentIdempotencyService;
import com.lx.pl.pay.common.service.UserPayRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * 支付幂等校验服务实现类
 */
@Slf4j
@Service
public class PaymentIdempotencyServiceImpl implements PaymentIdempotencyService {



}
