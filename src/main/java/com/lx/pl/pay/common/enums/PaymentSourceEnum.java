package com.lx.pl.pay.common.enums;

import lombok.Getter;
import lombok.AllArgsConstructor;

/**
 * 支付来源枚举
 */
@Getter
@AllArgsConstructor
public enum PaymentSourceEnum {

    /**
     * 订阅
     */
    SUBSCRIBE("Subscribe", "订阅"),
    
    /**
     * 购买Lumens
     */
    PURCHASE_LUMENS("Purchase Lumens", "购买Lumens"),
    
    /**
     * 平台
     */
    PLATFORM("Platform", "平台");

    /**
     * 来源名称（英文）
     */
    private final String name;
    
    /**
     * 来源描述（中文）
     */
    private final String description;
    
    /**
     * 根据名称获取枚举
     *
     * @param name 来源名称
     * @return 枚举对象，如果不存在返回null
     */
    public static PaymentSourceEnum getByName(String name) {
        if (name == null) {
            return null;
        }
        for (PaymentSourceEnum source : values()) {
            if (source.getName().equalsIgnoreCase(name)) {
                return source;
            }
        }
        return null;
    }
    
    /**
     * 检查名称是否有效
     *
     * @param name 来源名称
     * @return 是否有效
     */
    public static boolean isValidName(String name) {
        return getByName(name) != null;
    }
}