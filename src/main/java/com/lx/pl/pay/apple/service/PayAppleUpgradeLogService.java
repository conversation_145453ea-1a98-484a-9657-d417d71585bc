package com.lx.pl.pay.apple.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lx.pl.pay.apple.domain.PayAppleUpgradeLog;

public interface PayAppleUpgradeLogService extends IService<PayAppleUpgradeLog> {

    /**
     * 记录Apple产品升级日志
     *
     * @param userId                用户ID
     * @param loginName             用户登录名
     * @param productId             新产品ID
     * @param originProductId       原产品ID
     * @param originalTransactionId 原始交易ID
     * @param triggerTransactionId  触发升级的交易ID
     */
    void logUpgrade(Long userId,
                    String loginName,
                    String productId,
                    String originProductId,
                    String originalTransactionId,
                    String triggerTransactionId);
} 