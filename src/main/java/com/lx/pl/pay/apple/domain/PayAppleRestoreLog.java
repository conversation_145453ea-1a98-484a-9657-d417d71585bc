package com.lx.pl.pay.apple.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
@TableName("pay_apple_restore_log")
public class PayAppleRestoreLog {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户id
     */
    private Long fromUserId;

    /**
     * 用户账号
     */
    private String fromLoginName;

    /**
     * restore 后的user
     */
    private Long toUserId;

    /**
     * restore 后的login name
     */
    private String toLoginName;

    /**
     * 源事物ID
     */
    private String originalTransactionId;

    /**
     * 触发restore 的事物
     */
    private String triggerTransactionId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 修改者
     */
    private String updateBy;
} 