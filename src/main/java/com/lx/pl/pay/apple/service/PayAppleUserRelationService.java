package com.lx.pl.pay.apple.service;

import com.apple.itunes.storekit.model.JWSTransactionDecodedPayload;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.pay.apple.domain.PayAppleUserRelation;

import java.util.List;
import java.util.function.Supplier;

public interface PayAppleUserRelationService extends IService<PayAppleUserRelation> {

    PayAppleUserRelation getOrCreatePayAppleUserRelation(User user);

    PayAppleUserRelation updateOriginalTransactionId(User user, String originalTransactionId);

    PayAppleUserRelation queryByOriginalTransactionId(String originalTransactionId);

    PayAppleUserRelation queryRelationByOriginalTransactionId(JWSTransactionDecodedPayload transaction, Supplier<List<String>> function);
}