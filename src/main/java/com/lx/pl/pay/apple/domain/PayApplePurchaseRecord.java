package com.lx.pl.pay.apple.domain;// Java 实体类

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@TableName("pay_apple_purchase_record")
public class PayApplePurchaseRecord {

    @TableId(value = "id")
    @Schema(description = "主键 ID")
    private Long id;

    @TableField(value = "user_id")
    @Schema(description = "用户 ID")
    private Long userId;

    @TableField(value = "login_name")
    @Schema(description = "用户账号")
    private String loginName;

    @TableField(value = "app_account_token")
    @Schema(description = "与客户关联起来的 UUID")
    private String appAccountToken;

    @TableField(value = "original_transaction_id")
    @Schema(description = "原始购买的交易标识符")
    private String originalTransactionId;

    @TableField(value = "transaction_id")
    @Schema(description = "交易的唯一标识符")
    private String transactionId;

    @TableField(value = "product_id")
    @Schema(description = "应用内购买的产品标识符")
    private String productId;

    @TableField(value = "purchase_date")
    @Schema(description = "购买或续订费用的 UNIX 时间 s")
    private Long purchaseDate;

    private Long originalPurchaseDate;


    @TableField(value = "expires_date")
    @Schema(description = "订阅到期或续订的 UNIX 时间")
    private Long expiresDate;

    @TableField(value = "logic_period_end")
    @Schema(description = "逻辑订阅每周期结束时间")
    private Long logicPeriodEnd;

    @TableField(value = "logic_period_start")
    @Schema(description = "逻辑订阅每周期开始时间")
    private Long logicPeriodStart;

    @TableField(value = "lumen_qty")
    @Schema(description = "lumen 总数")
    private Integer lumenQty;

    @TableField(value = "count")
    @Schema(description = "购买数量")
    private Integer count;

    @TableField(value = "price")
    @Schema(description = "价格乘以 1000 的整数值")
    private Long price;

    @TableField(value = "amount")
    @Schema(description = "价格")
    private BigDecimal amount;

    @TableField(value = "cancel")
    @Schema(description = "是否取消")
    private Boolean cancel;

    @TableField(value = "revocation_date")
    @Schema(description = "退还交易或撤销的时间")
    private Long revocationDate;

    @TableField(value = "vip_plat_form")
    @Schema(description = "VIP 平台：stripe，ios，android")
    private String vipPlatForm;

    @TableField(value = "create_time")
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @TableField(value = "update_time")
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @TableField(value = "create_by")
    @Schema(description = "创建者")
    private String createBy;

    @TableField(value = "update_by")
    @Schema(description = "修改者")
    private String updateBy;
}

