package com.lx.pl.pay.apple.controller;

import com.lx.pl.annotation.Authorization;
import com.lx.pl.annotation.CurrentUser;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.dto.generic.R;
import com.lx.pl.pay.common.annotation.PayLogContext;
import com.lx.pl.pay.apple.domain.PayAppleUserRelation;
import com.lx.pl.pay.apple.dto.PayAppleProductDto;
import com.lx.pl.pay.apple.dto.ValidateRequest;
import com.lx.pl.pay.apple.service.ApplePayService;
import com.lx.pl.pay.apple.service.PayAppleUserRelationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/api/apple-pay")
public class ApplePayController {

    @Autowired
    private ApplePayService applePayService;

    @Autowired
    private PayAppleUserRelationService payAppleUserRelationService;

    @PostMapping("/validateV2")
    @Authorization
    @PayLogContext(value = "validateV2-")
    @Operation(summary = "v2 验证payload")
    public R<Boolean> validateV2(@RequestBody @Valid ValidateRequest request, @CurrentUser @Parameter(hidden = true, required = false) User user) {
        boolean isValid = applePayService.validateReceiptV2(request, user);
        return R.success(isValid);
    }

    @PostMapping("/restore")
    @Authorization
    @PayLogContext(value = "restore-")
    @Operation(summary = "restore 会员切换")
    public R<Boolean> restore(@RequestBody @Valid ValidateRequest request, @CurrentUser @Parameter(hidden = true) User user) {
        boolean isValid = applePayService.restore(request, user);
        return R.success(isValid);
    }

    @PostMapping("/product")
    @Operation(summary = "获取产品")
    @Authorization
    @PayLogContext(value = "获取产品-")
    public R<List<PayAppleProductDto>> getProduct(@CurrentUser @Parameter(hidden = true) User user) {
        List<PayAppleProductDto> product = applePayService.getProduct();
        return R.success(product);
    }

    @PostMapping("/create-apple-relation")
    @Operation(summary = "创建苹果用户关系")
    @Authorization
    @PayLogContext(value = "创建苹果用户关系-")
    public R<String> getOrCreatePayAppleUserRelation(@CurrentUser @Parameter(hidden = true) User user) {
        PayAppleUserRelation payAppleUserRelation = payAppleUserRelationService.getOrCreatePayAppleUserRelation(user);
        return R.success(payAppleUserRelation.getAppAccountToken());
    }
}
