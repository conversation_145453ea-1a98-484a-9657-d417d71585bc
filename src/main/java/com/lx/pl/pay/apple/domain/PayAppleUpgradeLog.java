package com.lx.pl.pay.apple.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
@TableName("pay_apple_upgrade_log")
public class PayAppleUpgradeLog {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户账号
     */
    private String loginName;

    /**
     * 产品ID
     */
    private String productId;

    /**
     * 源产品ID
     */
    private String originProductId;

    /**
     * 源事物ID
     */
    private String originalTransactionId;

    /**
     * 触发upgrade的事物
     */
    private String triggerTransactionId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 修改者
     */
    private String updateBy;
} 