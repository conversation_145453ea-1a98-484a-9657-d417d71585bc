package com.lx.pl.pay.apple.domain;// Java 实体类

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lx.pl.db.mysql.gen.entity.MyBaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@TableName("pay_apple_user_relation")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PayAppleUserRelation extends MyBaseEntity {

    @TableId(value = "id")
    @Schema(description = "主键 ID")
    private Long id;

    @TableField(value = "user_id")
    @Schema(description = "用户 ID")
    private Long userId;

    @TableField(value = "login_name")
    @Schema(description = "用户账号")
    private String loginName;

    @TableField(value = "email")
    @Schema(description = "用户邮箱")
    private String email;

    @Schema(description = "用于将交易与您自己的服务上的客户关联起来的 UUID")
    private String appAccountToken;

    @TableField(value = "original_transaction_id")
    @Schema(description = "原始交易标识符")
    private String originalTransactionId;

    @TableField(value = "valid")
    @Schema(description = "是否有效")
    private Integer valid;
}
