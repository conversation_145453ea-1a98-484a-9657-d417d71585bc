package com.lx.pl.pay.apple.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lx.pl.db.mysql.gen.entity.MyBaseEntity;
import com.lx.pl.pay.apple.enums.ExpirationIntentEnum;
import com.lx.pl.pay.apple.enums.OfferDiscountTypeEnum;
import com.lx.pl.pay.apple.enums.OfferTypeEnum;
import com.lx.pl.pay.apple.enums.PriceIncreaseStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName("pay_apple_jws_renewal_info")
@Schema(description = "订阅信息表，存储 App Store 订阅相关数据")
public class AppleJWSRenewalInfo extends MyBaseEntity {

    @TableId(value = "id")
    @Schema(description = "主键 ID")
    private Long id;

    @TableField(value = "notification_uuid")
    @Schema(description = "每条通知分配一个唯一标识符。使用此值来识别和忽略重复的通知。")
    private String notificationUUID;

    @TableField(value = "auto_renew_product_id")
    @Schema(description = "下一个计费期续订的产品标识符")
    private String autoRenewProductId;

    @TableField(value = "auto_renew_status")
    @Schema(description = "自动续订订阅的续订状态")
    private Integer autoRenewStatus;

    @TableField(value = "currency")
    @Schema(description = "订阅的货币代码")
    private String currency;

    @TableField(value = "eligible_win_back_offer_ids")
    @Schema(description = "客户有资格获得的挽回优惠 ID 列表[1,2]")
    private String eligibleWinBackOfferIds;

    @TableField(value = "environment")
    @Schema(description = "服务器环境，沙盒或生产")
    private String environment;

    /**
     * @see ExpirationIntentEnum
     */
    @TableField(value = "expiration_intent")
    @Schema(description = "订阅过期的原因")
    private Integer expirationIntent;

    @TableField(value = "grace_period_expires_date")
    @Schema(description = "订阅续订的计费宽限期到期时间")
    private Long gracePeriodExpiresDate;

    @TableField(value = "is_in_billing_retry_period")
    @Schema(description = "是否处于账单重试期")
    private Boolean isInBillingRetryPeriod;

    /**
     * @see OfferDiscountTypeEnum
     */
    @TableField(value = "offer_discount_type")
    @Schema(description = "优惠折扣的付款方式")
    private String offerDiscountType;

    @TableField(value = "offer_identifier")
    @Schema(description = "优惠代码或促销标识符")
    private String offerIdentifier;

    /**
     * @see OfferTypeEnum
     */
    @TableField(value = "offer_type")
    @Schema(description = "订阅优惠的类型")
    private Integer offerType;

    @TableField(value = "original_transaction_id")
    @Schema(description = "原始购买的交易标识符")
    private String originalTransactionId;

    /**
     * @see PriceIncreaseStatus
     */
    @TableField(value = "price_increase_status")
    @Schema(description = "自动续订订阅是否会涨价")
    private Integer priceIncreaseStatus;

    @TableField(value = "product_id")
    @Schema(description = "应用内购买的产品标识符")
    private String productId;

    @TableField(value = "recent_subscription_start_date")
    @Schema(description = "一系列订阅购买中最早的开始日期")
    private Long recentSubscriptionStartDate;

    @TableField(value = "renewal_date")
    @Schema(description = "最新的自动续订订阅到期日期")
    private Long renewalDate;

    @TableField(value = "renewal_price")
    @Schema(description = "下一个计费期续订价格")
    private Long renewalPrice;

    @TableField(value = "signed_date")
    @Schema(description = "App Store 签署 JWS 数据的时间戳")
    private Long signedDate;
}
