package com.lx.pl.pay.apple.service;

import com.apple.itunes.storekit.model.JWSTransactionDecodedPayload;
import com.apple.itunes.storekit.model.Type;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.pay.apple.domain.PayApplePurchaseRecord;

public interface PayApplePurchaseRecordService extends IService<PayApplePurchaseRecord> {
    boolean saveAppleLogicPurchaseRecord(JWSTransactionDecodedPayload payload, User user, Type consumable);

    String recallLumenByTransactionId(String lastTransactionId);

}
