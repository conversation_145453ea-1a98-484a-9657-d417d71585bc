package com.lx.pl.pay.apple.enums;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "自动续订订阅价格上涨状态枚举")
public enum PriceIncreaseStatus {

    PENDING(0, "客户尚未对需要客户同意的自动续订订阅价格上涨做出回应"),

    APPROVED(1, "客户同意自动续订订阅价格上涨（需要客户同意），或者 App Store 已通知客户自动续订订阅价格上涨（不需要同意）");

    private final int code;
    private final String description;

    PriceIncreaseStatus(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static PriceIncreaseStatus fromCode(int code) {
        for (PriceIncreaseStatus status : values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        throw new IllegalArgumentException("无效的价格上涨状态代码: " + code);
    }
}
