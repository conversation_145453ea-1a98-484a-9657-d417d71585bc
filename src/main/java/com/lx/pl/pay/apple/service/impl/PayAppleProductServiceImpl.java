package com.lx.pl.pay.apple.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lx.pl.db.mysql.gen.entity.PayAppleProduct;
import com.lx.pl.db.mysql.gen.mapper.PayAppleProductMapper;
import com.lx.pl.enums.AppleErrorCode;
import com.lx.pl.exception.PayAppleException;
import com.lx.pl.pay.apple.service.PayAppleProductService;
import org.springframework.stereotype.Service;

/**
 * PayAppleProduct 苹果产品类
 */
@Service
public class PayAppleProductServiceImpl extends ServiceImpl<PayAppleProductMapper, PayAppleProduct> implements PayAppleProductService {
    @Override
    public Boolean isUpdate(PayAppleProduct originalEntity, PayAppleProduct currentEntity) {
        //根据planlevel 和 priceInterval 判断是否为更新，standard到pro为升级 month到year为升级，两个必须同时满足
        // 判断空值情况
        if (originalEntity == null || currentEntity == null) {
            throw new PayAppleException(AppleErrorCode.PRODUCT_PARAM_ERROR);
        }

        // 获取原始和当前的 planLevel 和 priceInterval
        String originalPlanLevel = originalEntity.getPlanLevel();
        String currentPlanLevel = currentEntity.getPlanLevel();
        String originalPriceInterval = originalEntity.getPriceInterval();
        String currentPriceInterval = currentEntity.getPriceInterval();

        // 排除完全相同的情况
        if (originalPlanLevel.equalsIgnoreCase(currentPlanLevel)
                && originalPriceInterval.equalsIgnoreCase(currentPriceInterval)) {
            return false; // 未发生变化，不是升级
        }

        // 逻辑判断
        // Case 1: standard month 到任何其他情况为升级
        if ("standard".equalsIgnoreCase(originalPlanLevel) && "month".equalsIgnoreCase(originalPriceInterval)) {
            return true;
        }

        // Case 2: standard year 到 pro year 为升级
        if ("standard".equalsIgnoreCase(originalPlanLevel) && "year".equalsIgnoreCase(originalPriceInterval)
                && "pro".equalsIgnoreCase(currentPlanLevel) && "year".equalsIgnoreCase(currentPriceInterval)) {
            return true;
        }

        // Case 3: pro month 到 pro year 为升级
        if ("pro".equalsIgnoreCase(originalPlanLevel) && "month".equalsIgnoreCase(originalPriceInterval)
                && "pro".equalsIgnoreCase(currentPlanLevel) && "year".equalsIgnoreCase(currentPriceInterval)) {
            return true;
        }

        // 其他情况均为非升级
        return false;
    }

    @Override
    public PayAppleProduct queryByProductId(String productId) {
        return this.lambdaQuery().eq(PayAppleProduct::getAppleProductId, productId).one();
    }
}
