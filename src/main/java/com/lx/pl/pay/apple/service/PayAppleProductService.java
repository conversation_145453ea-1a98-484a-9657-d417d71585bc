package com.lx.pl.pay.apple.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lx.pl.db.mysql.gen.entity.PayAppleProduct;

/**
 * Stripe 用户客户信息服务接口
 */
public interface PayAppleProductService extends IService<PayAppleProduct> {
    Boolean isUpdate(PayAppleProduct originalEntity, PayAppleProduct currentEntity);

    PayAppleProduct queryByProductId(String productId);
    // 可以定义一些特定的业务方法，比如：
    // StripeUserCustomer findByCustomerId(String customerId);
}
