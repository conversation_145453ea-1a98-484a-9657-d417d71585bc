package com.lx.pl.pay.apple.service;

import com.apple.itunes.storekit.model.JWSTransactionDecodedPayload;
import com.apple.itunes.storekit.model.TransactionHistoryRequest;
import com.lx.pl.db.mysql.gen.entity.PayAppleProduct;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.pay.apple.dto.PayAppleProductDto;
import com.lx.pl.pay.apple.dto.ValidateRequest;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

@ResponseBody
public interface ApplePayService {

    /**
     * 获取苹果产品列表
     *
     * @return 苹果产品列表
     */
    List<PayAppleProductDto> getProduct();


    boolean validateReceiptV2(ValidateRequest request, User user);

    boolean handleInitialPurchase(JWSTransactionDecodedPayload payload, User user, PayAppleProduct product);

    List<String> getTransactionHistory(JWSTransactionDecodedPayload payload, TransactionHistoryRequest.ProductType productType);

    boolean restore(ValidateRequest request, User user);
}
