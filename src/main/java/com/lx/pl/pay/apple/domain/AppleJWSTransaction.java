package com.lx.pl.pay.apple.domain;// Java 实体类

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lx.pl.db.mysql.gen.entity.MyBaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName("pay_apple_jws_transaction")
public class AppleJWSTransaction extends MyBaseEntity {

    private Long id;

    private Long userId;

    private String loginName;

    @TableField(value = "log_uuid")
    private String logUUID;

    @TableField(value = "original_transaction_id")
    @Schema(description = "原始购买的交易标识符")
    private String originalTransactionId;

    @TableField(value = "transaction_id")
    @Schema(description = "交易的唯一标识符")
    private String transactionId;

    @TableField(value = "transaction_reason")
    @Schema(description = "购买交易的原因")
    private String transactionReason;

    @TableField(value = "app_account_token")
    @Schema(description = "用于将交易与您自己的服务上的客户关联起来的 UUID")
    private String appAccountToken;

    @TableField(value = "bundle_id")
    @Schema(description = "应用程序的软件包标识符")
    private String bundleId;

    @TableField(value = "currency")
    @Schema(description = "三字母 ISO 4217 货币代码")
    private String currency;

    @TableField(value = "environment")
    @Schema(description = "服务器环境，沙盒或生产")
    private String environment;

    @TableField(value = "expires_date")
    @Schema(description = "订阅到期或续订的 UNIX 时间")
    private Long expiresDate;

    @TableField(value = "in_app_ownership_type")
    @Schema(description = "描述交易是否由客户购买或通过家人共享提供")
    private String inAppOwnershipType;

    @TableField(value = "is_upgraded")
    @Schema(description = "指示客户是否升级到另一个订阅")
    private Boolean isUpgraded;

    @TableField(value = "offer_discount_type")
    @Schema(description = "订阅优惠的付款方式")
    private String offerDiscountType;

    @TableField(value = "offer_identifier")
    @Schema(description = "优惠代码或促销优惠标识符")
    private String offerIdentifier;

    @TableField(value = "offer_type")
    @Schema(description = "促销优惠类型")
    private Integer offerType;

    @TableField(value = "original_purchase_date_ms")
    @Schema(description = "原始交易的购买日期（毫秒）")
    private Long originalPurchaseDateMs;
    private Long originalPurchaseDate;

    @TableField(value = "price")
    @Schema(description = "价格乘以 1000 的整数值")
    private Long price;

    @TableField(value = "product_id")
    @Schema(description = "应用内购买的产品标识符")
    private String productId;

    @TableField(value = "purchase_date_ms")
    @Schema(description = "购买或续订费用的 UNIX 时间（毫秒）")
    private Long purchaseDateMs;

    private Long purchaseDate;

    @TableField(value = "quantity")
    @Schema(description = "用户购买的消耗品数量")
    private Integer quantity;

    @TableField(value = "revocation_date_ms")
    @Schema(description = "退还交易或撤销的时间（毫秒）")
    private Long revocationDateMs;
    private Long revocationDate;

    @TableField(value = "revocation_reason")
    @Schema(description = "退款或撤销的原因")
    private Integer revocationReason;

    @TableField(value = "signed_date_ms")
    @Schema(description = "App Store 签署 JWS 数据的时间（毫秒）")
    private Long signedDateMs;


    @TableField(value = "storefront")
    @Schema(description = "购买相关的 App Store 店面的三位字母代码")
    private String storefront;

    @TableField(value = "storefront_id")
    @Schema(description = "App Store 店面的唯一标识符")
    private String storefrontId;

    @TableField(value = "subscription_group_identifier")
    @Schema(description = "订阅组的标识符")
    private String subscriptionGroupIdentifier;

    @TableField(value = "type")
    @Schema(description = "应用内购买的类型")
    private String type;

    @TableField(value = "web_order_line_item_id")
    @Schema(description = "跨设备订阅购买事件的唯一标识符")
    private String webOrderLineItemId;
}
