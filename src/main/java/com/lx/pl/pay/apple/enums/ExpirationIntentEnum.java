package com.lx.pl.pay.apple.enums;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "订阅过期原因枚举")
public enum ExpirationIntentEnum {
    CUSTOMER_CANCELED(1, "客户取消了订阅"),
    BILLING_ERROR(2, "账单错误，例如客户的付款信息不再有效"),
    PRICE_INCREASE_DECLINED(3, "客户不同意需要客户同意的自动续订订阅价格上涨，导致订阅到期"),
    PRODUCT_UNAVAILABLE(4, "续订时无法购买该产品"),
    OTHER_REASON(5, "订阅由于其他原因而过期");

    private final int code;
    private final String description;

    ExpirationIntentEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static ExpirationIntentEnum fromCode(int code) {
        for (ExpirationIntentEnum reason : values()) {
            if (reason.getCode() == code) {
                return reason;
            }
        }
        throw new IllegalArgumentException("无效的过期原因代码: " + code);
    }
}
