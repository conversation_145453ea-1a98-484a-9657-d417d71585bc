package com.lx.pl.pay.apple.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lx.pl.pay.apple.domain.PayAppleRestoreLog;

public interface PayAppleRestoreLogService extends IService<PayAppleRestoreLog> {

    /**
     * 记录Apple Store恢复购买日志
     *
     * @param fromUserId            原用户ID
     * @param fromLoginName         原用户登录名
     * @param toUserId              目标用户ID
     * @param toLoginName           目标用户登录名
     * @param originalTransactionId 原始交易ID
     * @param triggerTransactionId  触发恢复的交易ID
     */
    void logRestore(Long fromUserId,
                    String fromLoginName,
                    Long toUserId,
                    String toLoginName,
                    String originalTransactionId,
                    String triggerTransactionId);
} 