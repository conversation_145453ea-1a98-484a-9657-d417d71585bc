package com.lx.pl.pay.apple.enums;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "订阅优惠类型枚举")
public enum OfferTypeEnum {
    INTRODUCTORY_OFFER(1, "一份介绍性优惠"),

    PROMOTIONAL_OFFER(2, "促销优惠"),

    SUBSCRIPTION_CODE_OFFER(3, "带有订阅优惠代码的优惠"),

    WIN_BACK_OFFER(4, "赢回提议");

    private final int code;
    private final String description;

    OfferTypeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static OfferTypeEnum fromCode(int code) {
        for (OfferTypeEnum type : values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("无效的订阅优惠类型代码: " + code);
    }
}
