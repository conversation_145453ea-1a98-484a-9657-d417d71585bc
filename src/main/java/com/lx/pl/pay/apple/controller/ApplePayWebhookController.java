package com.lx.pl.pay.apple.controller;


import com.apple.itunes.storekit.model.ResponseBodyV2;
import com.lx.pl.mq.producer.NormalMessageProducer;
import com.lx.pl.pay.common.annotation.PayLogContext;
import com.lx.pl.pay.apple.service.ApplePayCallbackService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;

@RestController
@RequestMapping("/api/apple-pay/v2")
public class ApplePayWebhookController {

    Logger log = LoggerFactory.getLogger("apple-pay-msg");
    @Resource
    private ApplePayCallbackService applePayCallbackService;

    @Resource
    private NormalMessageProducer<String> normalMessageProducer;

    @RequestMapping("/webhook")
    @PayLogContext(value = "apple-webhook-")
    public ResponseEntity<String> validateReceipt(@RequestBody ResponseBodyV2 v2Param, HttpServletResponse response) throws IOException {
        log.info("v2Param: {}", v2Param);
        try {
            applePayCallbackService.processSubscriptionEvent(v2Param.getSignedPayload());
        } catch (Exception e) {
            log.error("customerCallback error: ", e);
            // 返回合适的响应
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Webhook Error: " + e.getMessage() + " \n" + Arrays.toString(e.getStackTrace()));
        }
        return ResponseEntity.ok("Event processed successfully.");
    }

}
