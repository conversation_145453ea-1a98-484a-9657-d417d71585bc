package com.lx.pl.pay.apple.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lx.pl.pay.apple.domain.PayAppleRestoreLog;
import com.lx.pl.pay.apple.mapper.PayAppleRestoreLogMapper;
import com.lx.pl.pay.apple.service.PayAppleRestoreLogService;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@Service
public class PayAppleRestoreLogServiceImpl extends ServiceImpl<PayAppleRestoreLogMapper, PayAppleRestoreLog> implements PayAppleRestoreLogService {

    Logger log = LoggerFactory.getLogger("apple-pay-msg");

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void logRestore(Long fromUserId,
                           String fromLoginName,
                           Long toUserId,
                           String toLoginName,
                           String originalTransactionId,
                           String triggerTransactionId) {
        try {
            PayAppleRestoreLog storeLog = new PayAppleRestoreLog()
                    .setFromUserId(fromUserId)
                    .setFromLoginName(fromLoginName)
                    .setToUserId(toUserId)
                    .setToLoginName(toLoginName)
                    .setOriginalTransactionId(originalTransactionId)
                    .setTriggerTransactionId(triggerTransactionId)
                    .setCreateTime(LocalDateTime.now())
                    .setUpdateTime(LocalDateTime.now());

            this.save(storeLog);
            log.info("记录Apple Store恢复购买日志成功: fromUser={}, toUser={}, originalTransactionId={}",
                    fromLoginName, toLoginName, originalTransactionId);
        } catch (Exception e) {
            log.error("记录Apple Store恢复购买日志失败: fromUser={}, toUser={}, originalTransactionId={}",
                    fromLoginName, toLoginName, originalTransactionId, e);
            throw new RuntimeException("Failed to log Apple Store restore", e);
        }
    }
} 