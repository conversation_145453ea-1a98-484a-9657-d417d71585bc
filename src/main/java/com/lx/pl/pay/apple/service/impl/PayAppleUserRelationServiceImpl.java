package com.lx.pl.pay.apple.service.impl;

import cn.hutool.core.util.StrUtil;
import com.apple.itunes.storekit.model.JWSTransactionDecodedPayload;
import com.apple.itunes.storekit.verification.SignedDataVerifier;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.db.mysql.gen.mapper.UserMapper;
import com.lx.pl.enums.AppleErrorCode;
import com.lx.pl.exception.PayAppleException;
import com.lx.pl.pay.apple.domain.PayAppleUserRelation;
import com.lx.pl.pay.apple.mapper.PayAppleUserRelationMapper;
import com.lx.pl.pay.apple.service.PayAppleUserRelationService;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import java.util.function.Supplier;

import static com.lx.pl.enums.AppleErrorCode.FAILED_TO_UPDATE_PAYAPPLEUSERRELATION;

@Service
public class PayAppleUserRelationServiceImpl extends ServiceImpl<PayAppleUserRelationMapper, PayAppleUserRelation> implements PayAppleUserRelationService {
    Logger log = LoggerFactory.getLogger("apple-pay-msg");

    @Resource
    private PayAppleUserRelationMapper payAppleUserRelationMapper;

    @Resource
    private UserMapper userMapper;
    @Resource
    public SignedDataVerifier signedPayloadVerifier;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private ApplicationContext applicationContext;

    @Override
    public PayAppleUserRelation getOrCreatePayAppleUserRelation(User user) {
        //根据userId查询PayAppleUserRelation,通过payAppleUserRelationService查询
        RLock lock = redissonClient.getLock("pay:apple:" + user.getId());
        try {
            lock.lock();
            return applicationContext.getBean(PayAppleUserRelationServiceImpl.class).getPayAppleUserRelation(user);
        } catch (Exception e) {
            log.error("pay:apple:{}", user.getId(), e);
            throw new PayAppleException(AppleErrorCode.FAILED_TO_UPDATE_PAYAPPLEUSERRELATION);
        } finally {
            if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                lock.unlock();
            }
        }
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public PayAppleUserRelation getPayAppleUserRelation(User user) {
        PayAppleUserRelation payAppleUserRelation = this.getOne(new QueryWrapper<PayAppleUserRelation>().eq("user_id", user.getId()));
        if (payAppleUserRelation == null) {
            payAppleUserRelation = PayAppleUserRelation.builder()
                    .userId(user.getId())
                    .loginName(user.getLoginName())
                    .email(user.getEmail())
                    .appAccountToken(UUID.randomUUID().toString())
                    .valid(1)
                    .build();
            payAppleUserRelation.setCreateTime(LocalDateTime.now());
            this.save(payAppleUserRelation);
        }
        return payAppleUserRelation;
    }

    @Override
    public PayAppleUserRelation updateOriginalTransactionId(User user, String originalTransactionId) {
        // 根据 userId 查询 PayAppleUserRelation
        PayAppleUserRelation payAppleUserRelation = this.getOne(
                new QueryWrapper<PayAppleUserRelation>().eq("user_id", user.getId())
        );

        if (payAppleUserRelation != null) {
            payAppleUserRelation.setOriginalTransactionId(originalTransactionId);
            try {
                this.updateById(payAppleUserRelation);
                log.info("Updated PayAppleUserRelation for userId: {}, originalTransactionId: {}", user.getId(), originalTransactionId);
            } catch (Exception e) {
                log.error("Failed to update PayAppleUserRelation for userId: {}, originalTransactionId: {}", user.getId(), originalTransactionId, e);
                throw new PayAppleException(FAILED_TO_UPDATE_PAYAPPLEUSERRELATION);
            }
        } else {
            log.warn("No PayAppleUserRelation found for userId: {}", user.getId());
            throw new PayAppleException(AppleErrorCode.PAY_APPLE_USER_RELATION_NOT_FOUND);
        }

        return payAppleUserRelation;
    }

    @Override
    public PayAppleUserRelation queryByOriginalTransactionId(String originalTransactionId) {
        return this.lambdaQuery().eq(PayAppleUserRelation::getOriginalTransactionId, originalTransactionId).one();
    }


    @Override
    public PayAppleUserRelation queryRelationByOriginalTransactionId(JWSTransactionDecodedPayload transaction, Supplier<List<String>> function) {
        PayAppleUserRelation userRelation = this.queryByOriginalTransactionId(transaction.getOriginalTransactionId());
        if (userRelation == null) {
            // 1. 查询交易历史,找到初始购买记录获取appToken
            userRelation = getOrUpdatePayAppleUserRelation(transaction, function.get());
        }
        return userRelation;
    }

    private PayAppleUserRelation getOrUpdatePayAppleUserRelation(JWSTransactionDecodedPayload transaction, List<String> signedTransaction) {
        String appAccountToken = findAppAccountTokenFromHistory(transaction, signedTransaction);
        PayAppleUserRelation userRelation = null;
        if (appAccountToken != null) {
            // 根据appToken查找或创建用户关系
            userRelation = updateUserRelationFromAppToken(appAccountToken, transaction);
        }
        if (userRelation == null) {
            log.error("用户订阅关系不存在, transactionId: {}", transaction.getTransactionId());
            throw new RuntimeException("用户订阅关系不存在");
        }
        return userRelation;
    }


    private PayAppleUserRelation updateUserRelationFromAppToken(String appAccountToken, JWSTransactionDecodedPayload transaction) {
        log.info("根据appToken创建用户关系: appToken={}, originalTransactionId={}", appAccountToken, transaction.getOriginalTransactionId());
        try {
            // 查找现有的用户关系
            PayAppleUserRelation relation = this.lambdaQuery()
                    .eq(PayAppleUserRelation::getAppAccountToken, appAccountToken).one();
            if (relation == null) {
                log.warn("未找到对应的用户关系: appToken={}", appAccountToken);
                throw new RuntimeException("未找到对应的用户关系");
            }
            // 更新originalTransactionId
            if (StrUtil.isBlank(relation.getOriginalTransactionId())) {
                relation.setOriginalTransactionId(transaction.getOriginalTransactionId());
                relation.setUpdateTime(LocalDateTime.now());
                this.lambdaUpdate().eq(PayAppleUserRelation::getId, relation.getId())
                        .set(PayAppleUserRelation::getOriginalTransactionId, relation.getOriginalTransactionId())
                        .set(PayAppleUserRelation::getUpdateTime, relation.getUpdateTime()).update();
                log.info("更新用户关系originalTransactionId: userId={}", relation.getUserId());
            }
            return relation;
        } catch (Exception e) {
            log.error("创建用户关系失败: appToken={}", appAccountToken, e);
            throw new PayAppleException(FAILED_TO_UPDATE_PAYAPPLEUSERRELATION, e);
        }
    }

    private String findAppAccountTokenFromHistory(JWSTransactionDecodedPayload transaction, List<String> transactions) {
        log.info("查询交易历史获取appToken: originalTransactionId={}", transaction.getOriginalTransactionId());

        try {
            // 查找初始购买记录
            for (String signedTransaction : transactions) {
                JWSTransactionDecodedPayload historicalTransaction = signedPayloadVerifier.verifyAndDecodeTransaction(signedTransaction);
                if (historicalTransaction.getTransactionId().equals(historicalTransaction.getOriginalTransactionId())) {
                    String appToken = historicalTransaction.getAppAccountToken() != null ? historicalTransaction.getAppAccountToken()
                            .toString() : null;
                    if (appToken == null) {
                        log.warn("初始购买记录的appToken为空: originalTransactionId={}", historicalTransaction.getOriginalTransactionId());
                        throw new RuntimeException("初始购买记录的appToken为空");
                    }
                    log.info("找到初始购买记录的appToken: {}", appToken);
                    return appToken;
                }
            }
            log.warn("未找到初始购买记录: originalTransactionId={}", transaction.getOriginalTransactionId());
            return null;
        } catch (Exception e) {
            log.error("查询交易历史失败: originalTransactionId={}", transaction.getOriginalTransactionId(), e);
            throw new RuntimeException("Failed to find app account token", e);
        }
    }

}