package com.lx.pl.pay.apple.config;

import com.apple.itunes.storekit.client.AppStoreServerAPIClient;
import com.apple.itunes.storekit.model.Environment;
import com.apple.itunes.storekit.verification.SignedDataVerifier;
import com.lx.pl.pay.apple.service.impl.ApplePayCallbackServiceImpl;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ResourceLoader;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Configuration
public class PayBeanConfig {
    @Resource
    ResourceLoader resourceLoader;

    @Value("${pay.apple.issuerId:69a6de80-ea80-47e3-e053-5b8c7c11a4d1}")
    private String issuerId;
    @Value("${pay.apple.keyId:D477NXY4AC}")
    private String keyId;
    @Value("${pay.apple.bundleId:com.piclumen}")
    private String bundleId;
    @Value("${pay.apple.env:Sandbox}")
    private String env;
    @Value("${pay.apple.appAppleId:6720725066}")
    private Long appAppleId;

    @Bean
    public AppStoreServerAPIClient appStoreServerAPIClient() {
        String encodedKey = null;
        try {
            org.springframework.core.io.Resource resource = resourceLoader.getResource("classpath:apple/SubscriptionKey_D477NXY4AC.p8");
            try (InputStream inputStream = resource.getInputStream();
                 BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {

                // 将内容读取为单一字符串
                encodedKey = reader.lines().collect(Collectors.joining("\n"));
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        Environment environment = Environment.SANDBOX.getValue()
                .equalsIgnoreCase(env) ? Environment.SANDBOX : Environment.PRODUCTION;
        return new AppStoreServerAPIClient(encodedKey, keyId, issuerId, bundleId, environment);
    }

    @Bean
    public SignedDataVerifier signedPayloadVerifier() {
        Environment environment = Environment.SANDBOX.getValue()
                .equalsIgnoreCase(env) ? Environment.SANDBOX : Environment.PRODUCTION;
        Set<InputStream> rootCas = Set.of(
                Objects.requireNonNull(ApplePayCallbackServiceImpl.class.getClassLoader()
                        .getResourceAsStream("apple/AppleRootCA-G2.cer")),
                Objects.requireNonNull(ApplePayCallbackServiceImpl.class.getClassLoader()
                        .getResourceAsStream("apple/AppleRootCA-G3.cer"))
        );
        // appAppleId must be provided for the Production environment
        return new SignedDataVerifier(rootCas, bundleId, appAppleId, environment, true);
    }
}
