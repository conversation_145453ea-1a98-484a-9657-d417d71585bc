package com.lx.pl.pay.apple.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lx.pl.pay.apple.domain.PayAppleUpgradeLog;
import com.lx.pl.pay.apple.mapper.PayAppleUpgradeLogMapper;
import com.lx.pl.pay.apple.service.PayAppleUpgradeLogService;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@Service
public class PayAppleUpgradeLogServiceImpl extends ServiceImpl<PayAppleUpgradeLogMapper, PayAppleUpgradeLog> implements PayAppleUpgradeLogService {

    Logger log = LoggerFactory.getLogger("apple-pay-msg");

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void logUpgrade(Long userId,
                           String loginName,
                           String productId,
                           String originProductId,
                           String originalTransactionId,
                           String triggerTransactionId) {
        try {
            PayAppleUpgradeLog upgradeLog = new PayAppleUpgradeLog()
                    .setUserId(userId)
                    .setLoginName(loginName)
                    .setProductId(productId)
                    .setOriginProductId(originProductId)
                    .setOriginalTransactionId(originalTransactionId)
                    .setTriggerTransactionId(triggerTransactionId)
                    .setCreateTime(LocalDateTime.now())
                    .setUpdateTime(LocalDateTime.now());

            this.save(upgradeLog);
            log.info("记录Apple产品升级日志成功: user={}, productId={}, originalTransactionId={}",
                    loginName, productId, originalTransactionId);
        } catch (Exception e) {
            log.error("记录Apple产品升级日志失败: user={}, productId={}, originalTransactionId={}",
                    loginName, productId, originalTransactionId, e);
            throw new RuntimeException("Failed to log Apple product upgrade", e);
        }
    }
} 