package com.lx.pl.pay.apple.enums;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "产品折扣的付款方式枚举")
public enum OfferDiscountTypeEnum {
    FREE_TRIAL("FREE_TRIAL", "免费试用"),

    PAY_AS_YOU_GO("PAY_AS_YOU_GO", "随用随付"),

    PAY_UP_FRONT("PAY_UP_FRONT", "预付款");

    private final String code;
    private final String description;

    OfferDiscountTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static OfferDiscountTypeEnum fromCode(String code) {
        for (OfferDiscountTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("无效的折扣类型代码: " + code);
    }
}
