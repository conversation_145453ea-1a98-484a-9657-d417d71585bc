package com.lx.pl.pay.apple.service.impl;

import com.apple.itunes.storekit.model.JWSTransactionDecodedPayload;
import com.apple.itunes.storekit.model.Type;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lx.pl.db.mysql.gen.entity.PayAppleProduct;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.pay.apple.VipPlatform;
import com.lx.pl.pay.apple.domain.PayApplePurchaseRecord;
import com.lx.pl.pay.apple.mapper.PayApplePurchaseRecordMapper;
import com.lx.pl.pay.apple.service.PayAppleProductService;
import com.lx.pl.pay.apple.service.PayApplePurchaseRecordService;
import com.lx.pl.pay.common.service.PayLumenRecordService;

import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Service
public class PayApplePurchaseRecordServiceImpl extends ServiceImpl<PayApplePurchaseRecordMapper, PayApplePurchaseRecord> implements PayApplePurchaseRecordService {

    Logger log = LoggerFactory.getLogger("apple-pay-msg");

    @Resource
    private PayAppleProductService payAppleProductService;
    @Resource
    private PayLumenRecordService payLumenRecordService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveAppleLogicPurchaseRecord(JWSTransactionDecodedPayload payload, User user, Type type) {
        log.info("start saveAppleLogicPurchaseRecord userId:{}, transactionId:{}", user.getId(), payload.getTransactionId());

        // 检查记录是否已存在
        PayApplePurchaseRecord existRecord = this.lambdaQuery()
                .eq(PayApplePurchaseRecord::getTransactionId, payload.getTransactionId()).one();
        if (existRecord != null) {
            log.info("payLogicPurchaseRecord already exists userId:{}, transactionId:{}", user.getId(), payload.getTransactionId());
            return false;
        }

        // 获取对应的Apple产品信息
        PayAppleProduct product = payAppleProductService.lambdaQuery()
                .eq(PayAppleProduct::getAppleProductId, payload.getProductId()).one();
        if (product == null) {
            throw new RuntimeException("Apple product not found: " + payload.getProductId());
        }

        // 构建购买记录
        PayApplePurchaseRecord record = buildPayApplePurchaseRecord(payload, user, product);
        // 保存记录
        this.save(record);

        if (Type.AUTO_RENEWABLE_SUBSCRIPTION.getValue().equals(type.getValue())) {
            payLumenRecordService.saveLumenRecordForApple(record);
        }

        if (Type.CONSUMABLE.getValue().equals(type.getValue())) {
            payLumenRecordService.saveOneTimeLumenRecordForApple(record);
        }
        log.info("end saveAppleLogicPurchaseRecord userId:{}, transactionId:{}", user.getId(), payload.getTransactionId());
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String recallLumenByTransactionId(String lastTransactionId) {
        PayApplePurchaseRecord one = this.lambdaQuery().eq(PayApplePurchaseRecord::getTransactionId, lastTransactionId)
                .one();
        if (one == null) {
            log.info("未找到购买记录: {}", lastTransactionId);
            return lastTransactionId;
        }
        payLumenRecordService.recallLumen(one.getId(), "apple upgrade");
        this.lambdaUpdate().eq(PayApplePurchaseRecord::getTransactionId, lastTransactionId)
                .set(PayApplePurchaseRecord::getCancel, true)
                .update();
        log.info("end recallLumenByTransactionId {}", lastTransactionId);
        return one.getProductId();

    }

    @NotNull
    private static PayApplePurchaseRecord buildPayApplePurchaseRecord(JWSTransactionDecodedPayload payload, User user, PayAppleProduct product) {
        if (payload == null || user == null || product == null) {
            throw new IllegalArgumentException("Required parameters cannot be null: payload=" + payload + ", user=" + user + ", product=" + product);
        }

        PayApplePurchaseRecord record = new PayApplePurchaseRecord();
        record.setCreateTime(LocalDateTime.now());
        record.setUserId(user.getId());
        record.setLoginName(user.getLoginName());
        record.setLumenQty(product.getLumen());
        record.setVipPlatForm(VipPlatform.IOS.getPlatformName());

        // 安全处理 PriceInterval
        String priceInterval = product.getPriceInterval();
        if (priceInterval != null && priceInterval.toLowerCase().contains("year")) {
            record.setCount(12);
        } else {
            record.setCount(1);
        }

        // 安全处理可能为空的字段
        record.setPrice(payload.getPrice());
        record.setExpiresDate(payload.getExpiresDate() != null ? payload.getExpiresDate() / 1000 : null);
        record.setOriginalTransactionId(payload.getOriginalTransactionId());
        record.setTransactionId(payload.getTransactionId());
        record.setPurchaseDate(payload.getPurchaseDate() != null ? payload.getPurchaseDate() / 1000 : null);
        record.setOriginalPurchaseDate(payload.getOriginalPurchaseDate() != null ? payload.getOriginalPurchaseDate() / 1000 : null);

        if (payload.getRevocationDate() != null) {
            record.setRevocationDate(payload.getRevocationDate() / 1000);
        }

        record.setCancel(false);

        // 安全处理 AppAccountToken
        if (payload.getAppAccountToken() != null) {
            record.setAppAccountToken(payload.getAppAccountToken().toString());
        }

        // 安全处理时间戳
        record.setLogicPeriodStart(payload.getPurchaseDate());
        record.setLogicPeriodEnd(payload.getExpiresDate());
        record.setProductId(payload.getProductId());

        return record;
    }
}