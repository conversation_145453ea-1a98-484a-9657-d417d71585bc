package com.lx.pl.Filter;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.lx.pl.service.RedisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.web.context.support.WebApplicationContextUtils;

import javax.servlet.*;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Objects;

@Component
public class HeaderFilter implements Filter {

    private RedisService redisService;

    @Override
    public void init(FilterConfig filterConfig) {
        ServletContext servletContext = filterConfig.getServletContext();
        this.redisService = WebApplicationContextUtils
                .getRequiredWebApplicationContext(servletContext)
                .getBean(RedisService.class);
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) {
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        try {
            String version = (String) redisService.get("Web-Version");
            String suspensionVersion =(String) redisService.get("suspension-Version");
            httpResponse.addHeader("Web-Version", StringUtil.isNotBlank(version) ? version : "unknown");
            httpResponse.addHeader("suspension-Version",StringUtil.isNotBlank(suspensionVersion) ? suspensionVersion : "0");
            httpResponse.setHeader("Access-Control-Expose-Headers", "Web-Version,suspension-Version");
            // Continue with the filter chain
            chain.doFilter(request, response);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}