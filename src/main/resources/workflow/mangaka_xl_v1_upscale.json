{"3": {"inputs": {"seed": ["78", 0], "steps": ["85", 0], "cfg": ["84", 0], "sampler_name": ["76", 0], "scheduler": ["77", 0], "denoise": 0.38, "model": ["86", 0], "positive": ["52", 0], "negative": ["53", 0], "latent_image": ["89", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "4": {"inputs": {"ckpt_name": "animagineXLV31_v31.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "8": {"inputs": {"samples": ["3", 0], "vae": ["4", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "52": {"inputs": {"text": ["79", 0], "clip": ["86", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "53": {"inputs": {"text": ["81", 0], "clip": ["86", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "74": {"inputs": {"filename": "%date/%time_%seed", "path": "", "extension": "png", "steps": ["85", 0], "cfg": ["84", 0], "modelname": "animagineXLV31_v31.safetensors", "sampler_name": ["76", 0], "scheduler": ["77", 0], "positive": ["79", 0], "negative": ["81", 0], "seed_value": ["78", 0], "width": ["82", 0], "height": ["83", 0], "lossless_webp": true, "quality_jpeg_or_webp": 100, "counter": 0, "time_format": "%Y-%m-%d-%H%M%S", "images": ["8", 0]}, "class_type": "Save Image w/Metadata", "_meta": {"title": "Save Image w/Metadata"}}, "76": {"inputs": {"sampler_name": "euler_ancestral"}, "class_type": "Sampler Selector", "_meta": {"title": "Sampler Selector"}}, "77": {"inputs": {"scheduler": "karras"}, "class_type": "Scheduler Selector", "_meta": {"title": "Scheduler Selector"}}, "78": {"inputs": {"seed": 123456789}, "class_type": "Seed Generator", "_meta": {"title": "Seed Generator"}}, "79": {"inputs": {"string": "1girl, bare shoulders"}, "class_type": "String Literal", "_meta": {"title": "String Literal"}}, "81": {"inputs": {"string": "watermark"}, "class_type": "String Literal", "_meta": {"title": "String Literal"}}, "82": {"inputs": {"int": 832}, "class_type": "Width/Height Literal", "_meta": {"title": "Width/Height Literal"}}, "83": {"inputs": {"int": 1216}, "class_type": "Width/Height Literal", "_meta": {"title": "Width/Height Literal"}}, "84": {"inputs": {"float": 6.5}, "class_type": "Cfg Literal", "_meta": {"title": "Cfg Literal"}}, "85": {"inputs": {"int": 12}, "class_type": "Int Literal", "_meta": {"title": "Int Literal"}}, "86": {"inputs": {"lora_name": "Animated_Concept.safetensors", "strength_model": 0.2, "strength_clip": 0.2, "model": ["4", 0], "clip": ["4", 1]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "Load LoRA"}}, "87": {"inputs": {"image": "ComfyUI_temp_zcppx_00001_.png", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "88": {"inputs": {"upscale_model": ["91", 0], "image": ["87", 0]}, "class_type": "ImageUpscaleWithModel", "_meta": {"title": "Upscale Image (using Model)"}}, "89": {"inputs": {"pixels": ["90", 0], "vae": ["4", 2]}, "class_type": "VAEEncode", "_meta": {"title": "VAE Encode"}}, "90": {"inputs": {"upscale_method": "bilinear", "scale_by": 0.5, "image": ["88", 0]}, "class_type": "ImageScaleBy", "_meta": {"title": "Upscale Image By"}}, "91": {"inputs": {"model_name": "RealESRGAN_x4plus_anime_6B.pth"}, "class_type": "UpscaleModelLoader", "_meta": {"title": "Load Upscale Model"}}}