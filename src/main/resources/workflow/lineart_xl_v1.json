{"3": {"inputs": {"seed": ["78", 0], "steps": ["85", 0], "cfg": ["84", 0], "sampler_name": ["76", 0], "scheduler": ["77", 0], "denoise": 1, "model": ["86", 0], "positive": ["52", 0], "negative": ["53", 0], "latent_image": ["5", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "4": {"inputs": {"ckpt_name": "animagineXLV31_v31.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "5": {"inputs": {"width": ["82", 0], "height": ["83", 0], "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "Empty Latent Image"}}, "8": {"inputs": {"samples": ["3", 0], "vae": ["4", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "52": {"inputs": {"text": ["79", 0], "clip": ["86", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "53": {"inputs": {"text": ["81", 0], "clip": ["86", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "74": {"inputs": {"filename": "%date/%time_%seed", "path": "", "extension": "png", "steps": ["85", 0], "cfg": ["84", 0], "modelname": "sdxl_manga_x1.safetensors", "sampler_name": ["76", 0], "scheduler": ["77", 0], "positive": ["79", 0], "negative": ["81", 0], "seed_value": ["78", 0], "width": ["82", 0], "height": ["83", 0], "lossless_webp": true, "quality_jpeg_or_webp": 100, "counter": 0, "time_format": "%Y-%m-%d-%H%M%S", "images": ["93", 0]}, "class_type": "Save Image w/Metadata", "_meta": {"title": "Save Image w/Metadata"}}, "76": {"inputs": {"sampler_name": "euler_ancestral"}, "class_type": "Sampler Selector", "_meta": {"title": "Sampler Selector"}}, "77": {"inputs": {"scheduler": "karras"}, "class_type": "Scheduler Selector", "_meta": {"title": "Scheduler Selector"}}, "78": {"inputs": {"seed": 645087430094818}, "class_type": "Seed Generator", "_meta": {"title": "Seed Generator"}}, "79": {"inputs": {"string": "1girl, bare shoulders"}, "class_type": "String Literal", "_meta": {"title": "String Literal"}}, "81": {"inputs": {"string": "watermark"}, "class_type": "String Literal", "_meta": {"title": "String Literal"}}, "82": {"inputs": {"int": 832}, "class_type": "Width/Height Literal", "_meta": {"title": "Width/Height Literal"}}, "83": {"inputs": {"int": 1216}, "class_type": "Width/Height Literal", "_meta": {"title": "Width/Height Literal"}}, "84": {"inputs": {"float": 6.5}, "class_type": "Cfg Literal", "_meta": {"title": "Cfg Literal"}}, "85": {"inputs": {"int": 25}, "class_type": "Int Literal", "_meta": {"title": "Int Literal"}}, "86": {"inputs": {"lora_name": "Animated_Concept.safetensors", "strength_model": 0.5, "strength_clip": 0.5, "model": ["4", 0], "clip": ["4", 1]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "Load LoRA"}}, "87": {"inputs": {"coarse": "disable", "resolution": ["88", 0], "image": ["8", 0]}, "class_type": "LineArtPreprocessor", "_meta": {"title": "Realistic Lineart"}}, "88": {"inputs": {"mode": false, "a": ["82", 0], "b": ["83", 0]}, "class_type": "ImpactMinMax", "_meta": {"title": "ImpactMinMax"}}, "89": {"inputs": {"image": ["87", 0]}, "class_type": "ImageInvert", "_meta": {"title": "Invert Image"}}, "90": {"inputs": {"pixels": ["89", 0], "vae": ["4", 2]}, "class_type": "VAEEncode", "_meta": {"title": "VAE Encode"}}, "91": {"inputs": {"seed": ["78", 0], "steps": ["85", 0], "cfg": ["84", 0], "sampler_name": ["76", 0], "scheduler": ["77", 0], "denoise": 0.65, "model": ["86", 0], "positive": ["52", 0], "negative": ["53", 0], "latent_image": ["90", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "92": {"inputs": {"samples": ["91", 0], "vae": ["4", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "93": {"inputs": {"brightness": 0, "contrast": 1, "saturation": 0, "sharpness": 1, "blur": 0, "gaussian_blur": 0, "edge_enhance": 0, "detail_enhance": "false", "image": ["92", 0]}, "class_type": "Image Filter Adjustments", "_meta": {"title": "Image Filter Adjustments"}}}