{"1": {"inputs": {"transparency": true, "model": "isnet-anime", "post_processing": false, "only_mask": false, "alpha_matting": false, "alpha_matting_foreground_threshold": 240, "alpha_matting_background_threshold": 10, "alpha_matting_erode_size": 10, "background_color": "none", "images": ["2", 0]}, "class_type": "Image Rembg (Remove Background)", "_meta": {"title": "Image Rembg (Remove Background)"}}, "2": {"inputs": {"image": "input_image", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "6": {"inputs": {"filename": "%date/%time_%seed", "path": "", "extension": "png", "steps": 1, "cfg": 1, "modelname": "sd_xl_base_1.0.safetensors", "sampler_name": "euler", "scheduler": "normal", "positive": "remove background", "negative": "remove background", "seed_value": 0, "width": 0, "height": 0, "lossless_webp": true, "quality_jpeg_or_webp": 100, "counter": 0, "time_format": "%Y-%m-%d-%H%M%S", "images": ["1", 0]}, "class_type": "Save Image w/Metadata", "_meta": {"title": "Save Image w/Metadata"}}}