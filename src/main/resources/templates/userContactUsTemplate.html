<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户反馈</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f4f4;
            margin: 0;
            padding: 0;
        }
        .container {
            width: 80%;
            margin: 20px auto;
            background-color: #ffffff;
            padding: 20px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            padding: 20px;
            background-color: #007BFF;
            color: white;
        }
        .content {
            padding: 20px;
        }
        .user-info {
            margin-bottom: 20px;
        }
        .user-info h3 {
            margin: 0;
        }
        .user-info p {
            margin: 5px 0;
        }
        .container .email-source {
            font-size: 12px; /* 调整字体大小 */
            color: #888888; /* 设置灰色 */
        }
        .feedback {
            background-color: #f1f1f1;
            padding: 20px;
            margin-bottom: 20px;
        }
        .images {
            margin-top: 20px;
        }
        .images img {
            max-width: 100%;
            height: auto;
            display: block;
            margin-bottom: 10px;
        }
        .footer {
            text-align: center;
            padding: 10px;
            background-color: #ddd;
            color: #666;
        }
    </style>
</head>
<body>
<div class="container">
    <div class="header">
        <h1>用户反馈</h1>
    </div>
    <div class="content">
        <div class="user-info">
            <p>用户名: ${fullName}</p>
            <p>用户邮箱: ${email}</p>
        </div>
        <div class="feedback">
            <h3>用户反馈信息:</h3>
            <p>${message}</p>
        </div>
        <p class="email-source"> ${userAgent}</p>

        <!-- 图片展示区 -->
        <div class="images">
            <h3>上传的图片:</h3>
            ${urlList}  <!-- 这里简单放置占位符 -->
        </div>
    </div>
</div>
</body>
</html>
