<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lx.pl.db.mysql.gen.mapper.UserMapper">

     <select id="getNewUserNums" parameterType="java.lang.String" resultType="java.lang.Long">
         select count(*) from gpt_user where 1 = 1
         <if test="startDate != null and '' != startDate">
             and  create_time > #{startDate}
         </if>
         <if test="endDate != null and '' != endDate">
             and  #{endDate} > create_time
         </if>
     </select>

    <select id="getUserNums" parameterType="java.lang.String" resultType="java.lang.Long">
        select count(*) from gpt_user where 1 = 1
        <if test="startDate != null and '' != startDate">
            and  create_time >= #{startDate}
        </if>
        <if test="endDate != null and '' != endDate">
            and  #{endDate} > create_time
        </if>
        <if test="opexLoginNameList != null and opexLoginNameList.size() > 0">
            and login_name not in
            <foreach collection="opexLoginNameList" item="opexLoginName" separator="," open="(" close=")">
                #{opexLoginName}
            </foreach>
        </if>
    </select>

    <select id="getUserTopPictureNums" parameterType="java.lang.String" resultType="com.lx.pl.dto.StatisticsUser">
        SELECT f.login_name,count(f.id) as num,u.user_name
          FROM gpt_prompt_file f
          join gpt_user u on u.login_name = f.login_name
          where prompt_id is not null
          <if test="startDate != null and '' != startDate">
            and  f.create_time > #{startDate}
          </if>
          <if test="endDate != null and '' != endDate">
            and  #{endDate} > f.create_time
         </if>
          GROUP BY f.login_name order by  num desc limit 100
    </select>

    <select id="getUserIdByLoginNames" parameterType="java.util.List" resultType="java.lang.String">
         SELECT id from gpt_user where 1 = 1
        <if test="opexLoginNameList != null and opexLoginNameList.size() > 0">
            and login_name in
            <foreach collection="opexLoginNameList" item="opexLoginName" separator="," open="(" close=")">
                #{opexLoginName}
            </foreach>
        </if>
    </select>

    <update id="batchCollectionUpdateUsers">
        <foreach collection="users" item="user" separator=";">
            UPDATE gpt_user
            SET used_collect_num = #{user.usedCollectNum}
            WHERE login_name = #{user.loginName}
        </foreach>
    </update>
</mapper>
