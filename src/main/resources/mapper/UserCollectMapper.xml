<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lx.pl.db.mysql.gen.mapper.UserCollectMapper">



    <select id="selectNumByLoginNames"  resultType="com.lx.pl.dto.CollectLoginNameNum">
        select login_name,count(1) as num from ${tableName}
        where login_name in
        <foreach collection="loginNames" item="loginName" separator="," open="(" close=")">
            #{loginName}
        </foreach>
        group by login_name

    </select>
</mapper>
