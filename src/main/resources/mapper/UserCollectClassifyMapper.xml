<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lx.pl.db.mysql.gen.mapper.UserCollectClassifyMapper">

    <update id="updateSizeAndNumAdd">
        UPDATE gpt_user_collect_classify
        SET
        collect_nums = collect_nums + #{num}
        WHERE
        login_name = #{loginName}
        AND id  = #{classifyId}
    </update>

    <update id="updateSizeAndNumReduce">
        UPDATE gpt_user_collect_classify
        SET
            collect_nums = collect_nums - #{num}
        WHERE
            login_name = #{loginName}
          AND id  = #{classifyId}
    </update>


</mapper>
