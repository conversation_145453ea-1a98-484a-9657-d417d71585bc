<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lx.pl.db.mysql.gen.mapper.PromptFileMapper">

    <select id="getPromptFileList"  parameterType="java.util.List" resultType="com.lx.pl.dto.PromptFilePageResult">
        select f.*,u.thumbnail_avatar_url,u.user_name from gpt_explore_file f
        inner join gpt_user u on f.login_name = u.login_name
        <if test="'likes' == collationName">
            inner join gpt_user_like l on l.prompt_id = f.prompt_id and l.file_name = f.file_name
        </if>
        where f.del = 0 and f.sensitive_message is null
        <if test="'likes' == collationName">
            and l.login_name = #{loginName}
        </if>
        and DATE_SUB(NOW(), INTERVAL 30 MINUTE) > f.create_time
        <if test="vagueKey != null and '' != vagueKey">
            and f.prompt like CONCAT('%', #{vagueKey}, '%')
        </if>
        order by
        <if test="'hot' == collationName">
            like_nums desc ,
        </if>
        <if test="'likes' == collationName">
            l.create_time desc ,
        </if>
        f.create_time desc ,f.id desc
    </select>

    <select id="getTaskAvgTime" parameterType="java.lang.String" resultType="java.lang.Double">
        select AVG(TIMESTAMPDIFF(SECOND, create_time, gen_end_time)) AS avg_date_diff from gpt_prompt_record
        where prompt_id is not null and gen_end_time is not null
        <if test="startDate != null and '' != startDate">
            and create_time >= #{startDate}
        </if>
        <if test="endDate != null and '' != endDate">
            and #{endDate} > create_time
        </if>
        <if test="opexLoginNameList != null and opexLoginNameList.size() > 0">
            and login_name not in
            <foreach collection="opexLoginNameList" item="opexLoginName" separator="," open="(" close=")">
                #{opexLoginName}
            </foreach>
        </if>
    </select>

    <select id="getCreatePictureNums" parameterType="java.lang.String" resultType="java.lang.Long">
        select count(id) as nums  from gpt_prompt_file where prompt_id is not null
        <if test="startDate != null and '' != startDate">
            and  create_time >= #{startDate}
        </if>
        <if test="endDate != null and '' != endDate">
            and  #{endDate} > create_time
        </if>
        <if test="opexLoginNameList != null and opexLoginNameList.size() > 0">
            and login_name not in
            <foreach collection="opexLoginNameList" item="opexLoginName" separator="," open="(" close=")">
                #{opexLoginName}
            </foreach>
        </if>
    </select>

    <select id="getUserAvgCratePictureNums" parameterType="java.util.List" resultType="java.lang.Long">
        select avg(num) as nums from (
             SELECT count(id) as num  FROM gpt_prompt_file where prompt_id is not null
             <if test="startDate != null and '' != startDate">
                and  create_time >= #{startDate}
             </if>
             <if test="endDate != null and '' != endDate">
                and  #{endDate} > create_time
             </if>
             <if test="opexLoginNameList != null and opexLoginNameList.size() > 0">
                and login_name not in
             <foreach collection="opexLoginNameList" item="opexLoginName" separator="," open="(" close=")">
                #{opexLoginName}
            </foreach>
        </if>
             GROUP BY login_name) t
    </select>

    <select id="getUserMaxCratePictureNums" parameterType="java.util.List" resultType="java.lang.Long">
        select max(num) as nums from (
        SELECT count(id) as num  FROM gpt_prompt_file where prompt_id is not null
        <if test="startDate != null and '' != startDate">
            and  create_time > #{startDate}
        </if>
        <if test="endDate != null and '' != endDate">
            and  #{endDate} > create_time
        </if>
        <if test="opexLoginNameList != null and opexLoginNameList.size() > 0">
            and login_name not in
            <foreach collection="opexLoginNameList" item="opexLoginName" separator="," open="(" close=")">
                #{opexLoginName}
            </foreach>
        </if>
        GROUP BY login_name) t
    </select>

    <select id="getExplorePromptFileList"   resultType="com.lx.pl.dto.CommHistoryFile">
        select u.id as userId,u.user_name as userName,u.login_name as userLoginName,
        u.thumbnail_avatar_url as userAvatarUrl,f.id as promptFileId,f.file_url as fileUrl,f.thumbnail_url as thumbnailUrl,
        f.high_thumbnail_url as highThumbnailUrl,f.create_time as createTime,e.gen_info as genInfo,e.prompt as prompt,
        f.width as realWidth,f.height as realHeight,e.id as exploreId
        from gpt_explore_file e
        inner join gpt_user u on e.login_name = u.login_name
        inner join ${tableName} f on f.prompt_id = e.prompt_id and f.file_name = e.file_name
        <if test="lastId != null">
            and  e.id > #{lastId}
        </if>
        order by e.id asc
        limit #{batchSize}
    </select>

    <select id="getUserLikePromptFileListByCursor"   resultType="com.lx.pl.dto.CommHistoryLike">
        select u.id as userId,u.user_name as userName,u.login_name as userLoginName,
        u.thumbnail_avatar_url as userAvatarUrl,f.id as originImgId,l.id as originLikeId
        from gpt_user_like l
        inner join ${tableName} f on f.prompt_id = l.prompt_id and f.file_name = l.file_name
        inner join gpt_user u on l.login_name = u.login_name
        where f.del = 0 and f.id is not null
        <if test="lastId != null">
            and  l.id > #{lastId}
        </if>
        order by l.id asc
        limit #{batchSize}
    </select>


    <!-- 批量更新文件大小 -->
    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="files" item="file" index="index" separator=";">
            UPDATE ${tableName}
            SET size = #{file.size}
            WHERE id = #{file.id} AND login_name = #{file.loginName}
        </foreach>
    </update>

</mapper>