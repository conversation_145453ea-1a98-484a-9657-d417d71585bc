<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lx.pl.db.mysql.gen.mapper.PromptRecordMapper">

    <select id="getGenCreate" parameterType="java.lang.String" resultType="java.lang.Long">
        SELECT  COUNT(*) as nums  from gpt_prompt_record
        where 1 = 1
        <if test="startDate != null and '' != startDate">
            and  create_time > #{startDate}
        </if>
        <if test="endDate != null and '' != endDate">
            and  #{endDate} > create_time
        </if>
        <if test="opexLoginNameList != null and opexLoginNameList.size() > 0">
            and login_name not in
            <foreach collection="opexLoginNameList" item="opexLoginName" separator="," open="(" close=")">
                #{opexLoginName}
            </foreach>
        </if>
        <if test="genModeType != null and '' != genModeType">
            and  gen_mode = #{genModeType}
        </if>
    </select>

    <select id="getGenPicAvgTime" parameterType="java.lang.String" resultType="java.lang.Double">
        select avg(timestampdiff(SECOND,create_time,gen_start_time)) as genPicAvgTime from gpt_prompt_record
        where 1 = 1
        <if test="startDate != null and '' != startDate">
            and  create_time > #{startDate}
        </if>
        <if test="endDate != null and '' != endDate">
            and  #{endDate} > create_time
        </if>
        <if test="opexLoginNameList != null and opexLoginNameList.size() > 0">
            and login_name not in
            <foreach collection="opexLoginNameList" item="opexLoginName" separator="," open="(" close=")">
                #{opexLoginName}
            </foreach>
        </if>
    </select>

    <select id="getGenBatchSize" parameterType="java.lang.String" resultType="com.lx.pl.dto.common.Statistics">
        SELECT  batch_size as label,COUNT(*) as value  from gpt_prompt_record
        where 1 = 1
        <if test="startDate != null and '' != startDate">
            and  create_time > #{startDate}
        </if>
        <if test="endDate != null and '' != endDate">
            and  #{endDate} > create_time
        </if>
        <if test="opexLoginNameList != null and opexLoginNameList.size() > 0">
            and login_name not in
            <foreach collection="opexLoginNameList" item="opexLoginName" separator="," open="(" close=")">
                #{opexLoginName}
            </foreach>
        </if>
        GROUP BY batch_size
        order by batch_size
    </select>

    <select id="getGenOriginCreate" parameterType="java.lang.String" resultType="com.lx.pl.dto.common.Statistics">
        SELECT  origin_create as label,COUNT(*) as value  from gpt_prompt_record
        where 1 = 1
        <if test="startDate != null and '' != startDate">
            and  create_time > #{startDate}
        </if>
        <if test="endDate != null and '' != endDate">
            and  #{endDate} > create_time
        </if>
        <if test="opexLoginNameList != null and opexLoginNameList.size() > 0">
            and login_name not in
            <foreach collection="opexLoginNameList" item="opexLoginName" separator="," open="(" close=")">
                #{opexLoginName}
            </foreach>
        </if>
        GROUP BY origin_create
        order by origin_create
    </select>

    <select id="getGenBatchSizePicAvgTime" parameterType="java.lang.String" resultType="java.lang.Double">
        select avg(timestampdiff(SECOND,gen_start_time,gen_end_time)) as genPicAvgTime from gpt_prompt_record
        where 1 = 1
        <if test="startDate != null and '' != startDate">
            and  create_time > #{startDate}
        </if>
        <if test="endDate != null and '' != endDate">
            and  #{endDate} > create_time
        </if>
        <if test="opexLoginNameList != null and opexLoginNameList.size() > 0">
            and login_name not in
            <foreach collection="opexLoginNameList" item="opexLoginName" separator="," open="(" close=")">
                #{opexLoginName}
            </foreach>
        </if>
        <if test="batchSize != null and '' != batchSize">
            and  batch_size = #{batchSize}
        </if>
    </select>

    <select id="getImgToImgStyleNums" parameterType="java.lang.String" resultType="java.lang.Long">
        SELECT count(id) FROM gpt_prompt_record
        where 1 = 1
        <if test="startDate != null and '' != startDate">
            and  create_time > #{startDate}
        </if>
        <if test="endDate != null and '' != endDate">
            and  #{endDate} > create_time
        </if>
        <if test="opexLoginNameList != null and opexLoginNameList.size() > 0">
            and login_name not in
            <foreach collection="opexLoginNameList" item="opexLoginName" separator="," open="(" close=")">
                #{opexLoginName}
            </foreach>
        </if>
        <if test="style != null and '' != style">
            and  JSON_EXTRACT(gen_info, '$.img2img_info.style')  = #{style};
        </if>
    </select>

    <select id="getGenModelId" parameterType="java.lang.String" resultType="com.lx.pl.dto.common.Statistics">
        SELECT  model_id as label,COUNT(*) as value  from gpt_prompt_record
        where 1 = 1
        <if test="startDate != null and '' != startDate">
            and  create_time > #{startDate}
        </if>
        <if test="endDate != null and '' != endDate">
            and  #{endDate} > create_time
        </if>
        <if test="opexLoginNameList != null and opexLoginNameList.size() > 0">
            and login_name not in
            <foreach collection="opexLoginNameList" item="opexLoginName" separator="," open="(" close=")">
                #{opexLoginName}
            </foreach>
        </if>
        GROUP BY model_id
        order by model_id
    </select>

    <select id="getMaxAspectRatio" parameterType="java.util.List" resultType="com.lx.pl.dto.common.Statistics">
        select  aspect_ratio as label,max(num) as value from (
        SELECT count(id) as num,aspect_ratio  FROM gpt_prompt_record where 1 = 1
        <if test="startDate != null and '' != startDate">
            and  create_time > #{startDate}
        </if>
        <if test="endDate != null and '' != endDate">
            and  #{endDate} > create_time
        </if>
        <if test="opexLoginNameList != null and opexLoginNameList.size() > 0">
            and login_name not in
            <foreach collection="opexLoginNameList" item="opexLoginName" separator="," open="(" close=")">
                #{opexLoginName}
            </foreach>
        </if>
        GROUP BY aspect_ratio) t
        GROUP BY aspect_ratio;
    </select>

    <update id="updateDelFlagForUnusedPrompts" parameterType="java.util.List">
        UPDATE gpt_prompt_record
        SET del = true
        WHERE login_name = #{loginName}
        and prompt_id in
        <foreach collection="promptIds" item="promptId" separator="," open="(" close=")">
            #{promptId}
        </foreach>
    </update>

    <select id="getPromptRecordEmptyImg"   parameterType="java.util.List" resultType="String">
        SELECT distinct gpr.prompt_id
        FROM gpt_prompt_record gpr
        LEFT JOIN gpt_prompt_file gpf
          on gpf.login_name = gpr.login_name
          and gpr.prompt_id = gpf.prompt_id
          and  gpf.del = false
        WHERE gpr.login_name = #{loginName}
        and gpr.prompt_id IN
        <foreach item="promptId" collection="promptIds" open="(" separator="," close=")">
            #{promptId}
        </foreach>
        AND gpf.id is null
    </select>



</mapper>