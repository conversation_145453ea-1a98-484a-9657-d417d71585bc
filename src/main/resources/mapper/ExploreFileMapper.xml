<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lx.pl.db.mysql.gen.mapper.ExploreFileMapper">

    <select id="getExploreFileList"  resultType="com.lx.pl.dto.ExploreFileListResult">
        select f.*,u.thumbnail_avatar_url,u.user_name from gpt_explore_file f
        inner join gpt_user u on f.login_name = u.login_name
        where f.del = 0 and f.sensitive_message is null
        and DATE_SUB(NOW(), INTERVAL 30 MINUTE) > f.create_time
        order by  f.create_time desc ,f.id desc
    </select>

    <select id="getExploreFileListTotal" resultType="java.lang.Integer">
        select count(f.id) from gpt_explore_file f
        inner join gpt_user u on f.login_name = u.login_name
        where f.del = 0 and f.sensitive_message is null
        and DATE_SUB(NOW(), INTERVAL 30 MINUTE) > f.create_time
        order by  f.create_time desc ,f.id desc
    </select>
</mapper>
