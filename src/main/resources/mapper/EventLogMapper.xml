<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lx.pl.db.mysql.gen.mapper.EventLogMapper">

    <select id="getUseOperateNumsByTime"  parameterType="java.util.List" resultType="java.lang.Long">
        select count(*) from (
        select count(DISTINCT  user_id) as num from gpt_event_log where user_id is not null
        <if test="startDate != null">
            and create_time >= #{startDate,jdbcType=TIMESTAMP}
        </if>
        <if test="endDate != null">
            and create_time <![CDATA[ < ]]> #{endDate,jdbcType=TIMESTAMP}
        </if>
        <if test="opexUserIdList != null and opexUserIdList.size() > 0">
            and user_id not in
            <foreach collection="opexUserIdList" item="userId" separator="," open="(" close=")">
                #{userId}
            </foreach>
        </if>
        group by user_id
        ) t
    </select>

    <!-- 创建新表 -->
    <insert id="createTable">
        CREATE TABLE ${tableName} LIKE gpt_event_log_test_2024_9;
    </insert>


    <select id="selectFirstLogPerDay" resultType="com.lx.pl.db.mysql.gen.entity.EventLog">
        SELECT *
        FROM gpt_event_log
        WHERE (user_id, create_time) IN (
            SELECT user_id, MIN(create_time)
            FROM gpt_event_log
            GROUP BY DATE_FORMAT(create_time, '%Y-%m-%d'), user_id
        )
    </select>


</mapper>
