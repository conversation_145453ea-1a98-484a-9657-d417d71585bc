<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lx.pl.db.mysql.gen.mapper.HistoryImgMapper">

    <select id="getHistoryList"  resultType="com.lx.pl.dto.HistoryImgResult">
        select f.login_name,f.prompt_id,f.file_name as imgName,f.thumbnail_name,f.high_thumbnail_name,
               f.file_url as imgUrl,f.thumbnail_url,f.high_thumbnail_url,f.sensitive_message,f.width as realWidth,f.height as realHeight,
               f.create_time,f.is_public,f.mini_thumbnail_url,f.high_mini_url,p.prompt,p.gen_info,p.origin_create
        from gpt_prompt_file f
        inner join gpt_prompt_record p
            on f.prompt_id = p.prompt_id
        where f.del = 0   and f.login_name = #{loginName}
        <if test="notCollection != null and notCollection">
            and f.collect_nums=0
        </if>
        <if test="vagueKey != null and '' != vagueKey">
            and p.prompt like CONCAT('%', #{vagueKey}, '%')
        </if>
        <if test="vipType != null and vipType != '' and vipType == 'basic'">
            and f.create_time > DATE_SUB(NOW(), INTERVAL 30 DAY)
        </if>
        <if test="'Descending' == collationName">
            order by f.create_time desc ,f.id desc
        </if>
        <if test="'Ascending' == collationName">
            order by f.create_time asc ,f.id asc
        </if>
    </select>

    <select id="getNotCollectionList" resultType="com.lx.pl.dto.NotPublishImgResult">
        select f.login_name,f.prompt_id,f.file_name as imgName,f.width as realWidth,f.height as realHeight,
               f.create_time,f.is_public,f.high_mini_url,f.thumbnail_url,f.file_url as imgUrl
        from gpt_prompt_file f
                 inner join gpt_prompt_record p
                            on f.prompt_id = p.prompt_id
        where f.del = 0  and f.login_name =  #{loginName}  and f.collect_nums=0 and f.sensitive_message is null and p.origin_create != 'customUpload'
        <if test="publicStatus != null">
            and f.is_public = #{publicStatus}
        </if>
        <if test="vipType != null and vipType != '' and vipType == 'basic'">
            and f.create_time > DATE_SUB(NOW(), INTERVAL 30 DAY)
        </if>
        order by f.create_time desc ,f.id desc
    </select>
</mapper>
