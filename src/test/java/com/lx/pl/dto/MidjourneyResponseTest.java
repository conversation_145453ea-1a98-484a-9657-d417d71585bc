//package com.lx.pl.dto;
//
//import com.fasterxml.jackson.databind.ObjectMapper;
//import com.lx.pl.dto.midjourney.MidjourneyResponse;
//import org.junit.jupiter.api.Test;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.ActiveProfiles;
//
//import javax.annotation.Resource;
//
/// **
// * Midjourney响应DTO测试
// *
// * <AUTHOR>
// */
//@SpringBootTest
//@ActiveProfiles("test")
//public class MidjourneyResponseTest {
//
//    @Resource
//    private ObjectMapper objectMapper;
//
//    /**
//     * 测试TaskStatusResponse的JSON反序列化
//     */
//    @Test
//    void testTaskStatusResponseDeserialization() {
//        try {
//            // 模拟API返回的JSON（包含action字段）
//            String jsonResponse = """
//                {
//                    "status": "SUCCESS",
//                    "jobId": "12345678-1234-1234-1234-123456789abc",
//                    "message": "success",
//                    "data": {
//                        "action": "imagine",
//                        "actions": "imagine",
//                        "jobId": "12345678-1234-1234-1234-123456789abc",
//                        "progress": "100",
//                        "prompt": "a cute cat",
//                        "discordImage": "https://cdn.discordapp.com/test.png",
//                        "cdnImage": "https://cdnb.ttapi.io/test.png",
//                        "width": 1024,
//                        "height": 1024,
//                        "components": ["upsample1", "upsample2", "upsample3", "upsample4"],
//                        "images": ["https://cdnb.ttapi.io/image1.png"],
//                        "quota": 1,
//                        "unknownField": "this should be ignored"
//                    }
//                }
//                """;
//
//            // 测试反序列化
//            MidjourneyResponse.TaskStatusResponse response = objectMapper.readValue(
//                    jsonResponse, MidjourneyResponse.TaskStatusResponse.class);
//
//            // 验证基本字段
//            assert "SUCCESS".equals(response.getStatus());
//            assert "12345678-1234-1234-1234-123456789abc".equals(response.getJobId());
//            assert "success".equals(response.getMessage());
//
//            // 验证data字段
//            MidjourneyResponse.TaskData data = response.getData();
//            assert data != null;
//            assert "imagine".equals(data.getActions());
//            assert "imagine".equals(data.getAction()); // 新增的兼容字段
//            assert "12345678-1234-1234-1234-123456789abc".equals(data.getJobId());
//            assert "100".equals(data.getProgress());
//            assert "a cute cat".equals(data.getPrompt());
//            assert Integer.valueOf(1024).equals(data.getWidth());
//            assert Integer.valueOf(1024).equals(data.getHeight());
//            assert Integer.valueOf(1).equals(data.getQuota());
//
//            System.out.println("✅ TaskStatusResponse反序列化测试通过");
//            System.out.println("Actions: " + data.getActions());
//            System.out.println("Action: " + data.getAction());
//            System.out.println("JobId: " + data.getJobId());
//            System.out.println("Progress: " + data.getProgress());
//
//        } catch (Exception e) {
//            System.err.println("❌ TaskStatusResponse反序列化测试失败: " + e.getMessage());
//            e.printStackTrace();
//        }
//    }
//
//    /**
//     * 测试BaseResponseData的JSON反序列化
//     */
//    @Test
//    void testBaseResponseDataDeserialization() {
//        try {
//            // 模拟API返回的JSON
//            String jsonResponse = """
//                {
//                    "status": "SUCCESS",
//                    "message": "操作成功",
//                    "data": {
//                        "jobId": "12345678-1234-1234-1234-123456789abc",
//                        "extraField": "this should be ignored"
//                    }
//                }
//                """;
//
//            // 测试反序列化
//            MidjourneyResponse response = objectMapper.readValue(jsonResponse, MidjourneyResponse.class);
//
//            // 验证基本字段
//            assert "SUCCESS".equals(response.getStatus());
//            assert "操作成功".equals(response.getMessage());
//            assert response.getData() != null;
//
//            System.out.println("✅ BaseResponseData反序列化测试通过");
//            System.out.println("Status: " + response.getStatus());
//            System.out.println("Message: " + response.getMessage());
//            System.out.println("Data: " + response.getData());
//
//        } catch (Exception e) {
//            System.err.println("❌ BaseResponseData反序列化测试失败: " + e.getMessage());
//            e.printStackTrace();
//        }
//    }
//
//    /**
//     * 测试包含未知字段的JSON反序列化
//     */
//    @Test
//    void testUnknownFieldsIgnored() {
//        try {
//            // 包含很多未知字段的JSON
//            String jsonResponse = """
//                {
//                    "status": "SUCCESS",
//                    "jobId": "test-job-id",
//                    "message": "success",
//                    "unknownField1": "should be ignored",
//                    "unknownField2": 123,
//                    "unknownField3": true,
//                    "data": {
//                        "action": "imagine",
//                        "actions": "imagine",
//                        "jobId": "test-job-id",
//                        "progress": "50",
//                        "prompt": "test prompt",
//                        "unknownDataField1": "ignored",
//                        "unknownDataField2": ["array", "values"],
//                        "unknownDataField3": {
//                            "nested": "object"
//                        }
//                    }
//                }
//                """;
//
//            // 测试反序列化
//            MidjourneyResponse.TaskStatusResponse response = objectMapper.readValue(
//                    jsonResponse, MidjourneyResponse.TaskStatusResponse.class);
//
//            // 验证已知字段正常解析
//            assert "SUCCESS".equals(response.getStatus());
//            assert "test-job-id".equals(response.getJobId());
//            assert response.getData() != null;
//            assert "imagine".equals(response.getData().getAction());
//            assert "50".equals(response.getData().getProgress());
//
//            System.out.println("✅ 未知字段忽略测试通过");
//            System.out.println("成功忽略了未知字段，正常解析了已知字段");
//
//        } catch (Exception e) {
//            System.err.println("❌ 未知字段忽略测试失败: " + e.getMessage());
//            e.printStackTrace();
//        }
//    }
//}
