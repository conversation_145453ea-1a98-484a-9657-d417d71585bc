package com.lx.pl.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lx.pl.dto.midjourney.MidjourneyResponse;
import com.lx.pl.util.JsonUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

/**
 * Prompt Check功能测试
 * 
 * <AUTHOR>
 */
@SpringBootTest
@ActiveProfiles("test")
public class PromptCheckTest {
    
    @Resource
    private ObjectMapper objectMapper;
    
    /**
     * 测试解析HTTP 400响应的JSON
     */
    @Test
    void testParsePromptCheckErrorResponse() {
        try {
            // 模拟API返回的HTTP 400响应JSON
            String errorResponseJson = """
                {
                    "status": "FAILED",
                    "message": "[Invalid parameter] `--style` is not compatible with `--version 6`",
                    "data": null
                }
                """;
            
            // 测试解析
            MidjourneyResponse.PromptCheckResponse response = JsonUtils.readValue(
                    errorResponseJson, MidjourneyResponse.PromptCheckResponse.class);
            
            // 验证解析结果
            assert "FAILED".equals(response.getStatus());
            assert "[Invalid parameter] `--style` is not compatible with `--version 6`".equals(response.getMessage());
            assert response.getData() == null;
            
            System.out.println("✅ HTTP 400响应解析测试通过");
            System.out.println("Status: " + response.getStatus());
            System.out.println("Message: " + response.getMessage());
            System.out.println("Data: " + response.getData());
            
        } catch (Exception e) {
            System.err.println("❌ HTTP 400响应解析测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试解析HTTP 200成功响应的JSON
     */
    @Test
    void testParsePromptCheckSuccessResponse() {
        try {
            // 模拟API返回的HTTP 200响应JSON
            String successResponseJson = """
                {
                    "status": "SUCCESS",
                    "message": "success",
                    "data": "Prompt verification successful."
                }
                """;
            
            // 测试解析
            MidjourneyResponse.PromptCheckResponse response = JsonUtils.readValue(
                    successResponseJson, MidjourneyResponse.PromptCheckResponse.class);
            
            // 验证解析结果
            assert "SUCCESS".equals(response.getStatus());
            assert "success".equals(response.getMessage());
            assert "Prompt verification successful.".equals(response.getData());
            
            System.out.println("✅ HTTP 200响应解析测试通过");
            System.out.println("Status: " + response.getStatus());
            System.out.println("Message: " + response.getMessage());
            System.out.println("Data: " + response.getData());
            
        } catch (Exception e) {
            System.err.println("❌ HTTP 200响应解析测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试解析包含敏感词的响应
     */
    @Test
    void testParsePromptCheckBannedWordsResponse() {
        try {
            // 模拟包含敏感词的响应
            String bannedWordsResponseJson = """
                {
                    "status": "FAILED",
                    "message": "banned prompt words：sexy",
                    "data": null
                }
                """;
            
            // 测试解析
            MidjourneyResponse.PromptCheckResponse response = JsonUtils.readValue(
                    bannedWordsResponseJson, MidjourneyResponse.PromptCheckResponse.class);
            
            // 验证解析结果
            assert "FAILED".equals(response.getStatus());
            assert "banned prompt words：sexy".equals(response.getMessage());
            assert response.getData() == null;
            
            System.out.println("✅ 敏感词响应解析测试通过");
            System.out.println("Status: " + response.getStatus());
            System.out.println("Message: " + response.getMessage());
            System.out.println("Data: " + response.getData());
            
        } catch (Exception e) {
            System.err.println("❌ 敏感词响应解析测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试解析各种错误情况
     */
    @Test
    void testParseVariousErrorResponses() {
        // 测试空prompt错误
        try {
            String emptyPromptJson = """
                {
                    "status": "FAILED",
                    "message": "\\"prompt\\" cannot be empty.",
                    "data": null
                }
                """;
            
            MidjourneyResponse.PromptCheckResponse response = JsonUtils.readValue(
                    emptyPromptJson, MidjourneyResponse.PromptCheckResponse.class);
            
            assert "FAILED".equals(response.getStatus());
            assert "\"prompt\" cannot be empty.".equals(response.getMessage());
            
            System.out.println("✅ 空prompt错误解析测试通过");
            
        } catch (Exception e) {
            System.err.println("❌ 空prompt错误解析测试失败: " + e.getMessage());
        }
        
        // 测试参数错误
        try {
            String paramErrorJson = """
                {
                    "status": "FAILED",
                    "message": "[Invalid parameter] Unknown parameter --xyz",
                    "data": null
                }
                """;
            
            MidjourneyResponse.PromptCheckResponse response = JsonUtils.readValue(
                    paramErrorJson, MidjourneyResponse.PromptCheckResponse.class);
            
            assert "FAILED".equals(response.getStatus());
            assert "[Invalid parameter] Unknown parameter --xyz".equals(response.getMessage());
            
            System.out.println("✅ 参数错误解析测试通过");
            
        } catch (Exception e) {
            System.err.println("❌ 参数错误解析测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试创建默认失败响应
     */
    @Test
    void testCreateFallbackResponse() {
        try {
            // 创建默认失败响应
            MidjourneyResponse.PromptCheckResponse fallbackResponse = new MidjourneyResponse.PromptCheckResponse();
            fallbackResponse.setStatus("FAILED");
            fallbackResponse.setMessage("Prompt检验失败");
            fallbackResponse.setData(null);
            
            // 验证创建的响应
            assert "FAILED".equals(fallbackResponse.getStatus());
            assert "Prompt检验失败".equals(fallbackResponse.getMessage());
            assert fallbackResponse.getData() == null;
            
            System.out.println("✅ 默认失败响应创建测试通过");
            System.out.println("Status: " + fallbackResponse.getStatus());
            System.out.println("Message: " + fallbackResponse.getMessage());
            
        } catch (Exception e) {
            System.err.println("❌ 默认失败响应创建测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
