package com.lx.pl.pay.common.enums;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * PaymentPlatform 枚举测试类
 */
public class PaymentPlatformTest {

    @Test
    public void testGetByCode() {
        // 测试有效代码
        assertEquals(PaymentPlatform.BACKEND, PaymentPlatform.getByCode(0));
        assertEquals(PaymentPlatform.STRIPE, PaymentPlatform.getByCode(1));
        assertEquals(PaymentPlatform.PAYPAL, PaymentPlatform.getByCode(2));
        assertEquals(PaymentPlatform.APPLE, PaymentPlatform.getByCode(3));
        assertEquals(PaymentPlatform.GOOGLE, PaymentPlatform.getByCode(4));

        // 测试无效代码
        assertNull(PaymentPlatform.getByCode(99));
        assertNull(PaymentPlatform.getByCode(-1));
        assertNull(PaymentPlatform.getByCode(null));
    }

    @Test
    public void testGetByName() {
        // 测试有效名称
        assertEquals(PaymentPlatform.BACKEND, PaymentPlatform.getByName("Backend"));
        assertEquals(PaymentPlatform.STRIPE, PaymentPlatform.getByName("Stripe"));
        assertEquals(PaymentPlatform.PAYPAL, PaymentPlatform.getByName("PayPal"));
        assertEquals(PaymentPlatform.APPLE, PaymentPlatform.getByName("Apple"));
        assertEquals(PaymentPlatform.GOOGLE, PaymentPlatform.getByName("Google"));

        // 测试无效名称
        assertNull(PaymentPlatform.getByName("InvalidPlatform"));
        assertNull(PaymentPlatform.getByName("stripe")); // 大小写敏感
        assertNull(PaymentPlatform.getByName(null));
    }

    @Test
    public void testGetNameByCode() {
        // 测试有效代码
        assertEquals("Backend", PaymentPlatform.getNameByCode(0));
        assertEquals("Stripe", PaymentPlatform.getNameByCode(1));
        assertEquals("PayPal", PaymentPlatform.getNameByCode(2));
        assertEquals("Apple", PaymentPlatform.getNameByCode(3));
        assertEquals("Google", PaymentPlatform.getNameByCode(4));

        // 测试无效代码
        assertEquals("Unknown", PaymentPlatform.getNameByCode(99));
        assertEquals("Unknown", PaymentPlatform.getNameByCode(-1));
        assertEquals("Unknown", PaymentPlatform.getNameByCode(null));
    }

    @Test
    public void testGetDescriptionByCode() {
        // 测试有效代码
        assertEquals("后台管理", PaymentPlatform.getDescriptionByCode(0));
        assertEquals("Stripe支付", PaymentPlatform.getDescriptionByCode(1));
        assertEquals("PayPal支付", PaymentPlatform.getDescriptionByCode(2));
        assertEquals("Apple支付", PaymentPlatform.getDescriptionByCode(3));
        assertEquals("Google支付", PaymentPlatform.getDescriptionByCode(4));

        // 测试无效代码
        assertEquals("未知平台", PaymentPlatform.getDescriptionByCode(99));
        assertEquals("未知平台", PaymentPlatform.getDescriptionByCode(-1));
        assertEquals("未知平台", PaymentPlatform.getDescriptionByCode(null));
    }

    @Test
    public void testIsValidCode() {
        // 测试有效代码
        assertTrue(PaymentPlatform.isValidCode(0));
        assertTrue(PaymentPlatform.isValidCode(1));
        assertTrue(PaymentPlatform.isValidCode(2));
        assertTrue(PaymentPlatform.isValidCode(3));
        assertTrue(PaymentPlatform.isValidCode(4));

        // 测试无效代码
        assertFalse(PaymentPlatform.isValidCode(99));
        assertFalse(PaymentPlatform.isValidCode(-1));
        assertFalse(PaymentPlatform.isValidCode(null));
    }

    @Test
    public void testIsValidName() {
        // 测试有效名称
        assertTrue(PaymentPlatform.isValidName("Backend"));
        assertTrue(PaymentPlatform.isValidName("Stripe"));
        assertTrue(PaymentPlatform.isValidName("PayPal"));
        assertTrue(PaymentPlatform.isValidName("Apple"));
        assertTrue(PaymentPlatform.isValidName("Google"));

        // 测试无效名称
        assertFalse(PaymentPlatform.isValidName("InvalidPlatform"));
        assertFalse(PaymentPlatform.isValidName("stripe")); // 大小写敏感
        assertFalse(PaymentPlatform.isValidName(null));
        assertFalse(PaymentPlatform.isValidName(""));
    }

    @Test
    public void testGetAllCodes() {
        Integer[] codes = PaymentPlatform.getAllCodes();
        
        assertEquals(5, codes.length);
        assertArrayEquals(new Integer[]{0, 1, 2, 3, 4}, codes);
    }

    @Test
    public void testGetAllNames() {
        String[] names = PaymentPlatform.getAllNames();
        
        assertEquals(5, names.length);
        assertArrayEquals(new String[]{"Backend", "Stripe", "PayPal", "Apple", "Google"}, names);
    }

    @Test
    public void testEnumProperties() {
        // 测试枚举属性
        PaymentPlatform stripe = PaymentPlatform.STRIPE;
        assertEquals(Integer.valueOf(1), stripe.getCode());
        assertEquals("Stripe", stripe.getName());
        assertEquals("Stripe支付", stripe.getDescription());

        PaymentPlatform backend = PaymentPlatform.BACKEND;
        assertEquals(Integer.valueOf(0), backend.getCode());
        assertEquals("Backend", backend.getName());
        assertEquals("后台管理", backend.getDescription());
    }

    @Test
    public void testEnumValues() {
        // 测试所有枚举值
        PaymentPlatform[] values = PaymentPlatform.values();
        assertEquals(5, values.length);
        
        assertEquals(PaymentPlatform.BACKEND, values[0]);
        assertEquals(PaymentPlatform.STRIPE, values[1]);
        assertEquals(PaymentPlatform.PAYPAL, values[2]);
        assertEquals(PaymentPlatform.APPLE, values[3]);
        assertEquals(PaymentPlatform.GOOGLE, values[4]);
    }
}
