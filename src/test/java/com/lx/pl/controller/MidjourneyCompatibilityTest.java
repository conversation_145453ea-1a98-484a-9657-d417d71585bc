//package com.lx.pl.controller;
//
//import com.lx.pl.controller.midjourney.MidjourneyController;
//import com.lx.pl.dto.GenGenericPara;
//import com.lx.pl.dto.Resolution;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.ActiveProfiles;
//
//import java.lang.reflect.Method;
//
/// **
// * Midjourney兼容接口测试
// *
// * <AUTHOR>
// */
//@SpringBootTest
//@ActiveProfiles("test")
//public class MidjourneyCompatibilityTest {
//
//    private MidjourneyController controller;
//
//    @BeforeEach
//    void setUp() {
//        controller = new MidjourneyController();
//    }
//
//    /**
//     * 测试基础prompt构建
//     */
//    @Test
//    void testBasicPromptBuilding() throws Exception {
//        GenGenericPara genParameters = new GenGenericPara();
//        genParameters.setPrompt("a beautiful landscape");
//
//        String result = invokePrivateMethod("buildMidjourneyPrompt", genParameters);
//
//        assert "a beautiful landscape".equals(result);
//        System.out.println("✅ 基础prompt构建测试通过: " + result);
//    }
//
//    /**
//     * 测试分辨率转换
//     */
//    @Test
//    void testResolutionConversion() throws Exception {
//        GenGenericPara genParameters = new GenGenericPara();
//        genParameters.setPrompt("test");
//
//        // 测试1:1比例
//        Resolution resolution1 = new Resolution();
//        resolution1.setWidth(1024);
//        resolution1.setHeight(1024);
//        genParameters.setResolution(resolution1);
//
//        String result1 = invokePrivateMethod("buildMidjourneyPrompt", genParameters);
//        assert result1.contains("--ar 1:1");
//        System.out.println("✅ 1:1比例测试通过: " + result1);
//
//        // 测试16:9比例
//        Resolution resolution2 = new Resolution();
//        resolution2.setWidth(1344);
//        resolution2.setHeight(768);
//        genParameters.setResolution(resolution2);
//
//        String result2 = invokePrivateMethod("buildMidjourneyPrompt", genParameters);
//        assert result2.contains("--ar 16:9");
//        System.out.println("✅ 16:9比例测试通过: " + result2);
//
//        // 测试9:16比例
//        Resolution resolution3 = new Resolution();
//        resolution3.setWidth(768);
//        resolution3.setHeight(1344);
//        genParameters.setResolution(resolution3);
//
//        String result3 = invokePrivateMethod("buildMidjourneyPrompt", genParameters);
//        assert result3.contains("--ar 9:16");
//        System.out.println("✅ 9:16比例测试通过: " + result3);
//    }
//
//    /**
//     * 测试负面提示词转换
//     */
//    @Test
//    void testNegativePromptConversion() throws Exception {
//        GenGenericPara genParameters = new GenGenericPara();
//        genParameters.setPrompt("a beautiful landscape");
//        genParameters.setNegative_prompt("blurry, low quality");
//
//        String result = invokePrivateMethod("buildMidjourneyPrompt", genParameters);
//
//        assert result.contains("--no blurry, low quality");
//        System.out.println("✅ 负面提示词转换测试通过: " + result);
//    }
//
//    /**
//     * 测试种子值转换
//     */
//    @Test
//    void testSeedConversion() throws Exception {
//        GenGenericPara genParameters = new GenGenericPara();
//        genParameters.setPrompt("test");
//        genParameters.setSeed(12345L);
//
//        String result = invokePrivateMethod("buildMidjourneyPrompt", genParameters);
//
//        assert result.contains("--seed 12345");
//        System.out.println("✅ 种子值转换测试通过: " + result);
//    }
//
//    /**
//     * 测试CFG转换
//     */
//    @Test
//    void testCfgConversion() throws Exception {
//        GenGenericPara genParameters = new GenGenericPara();
//        genParameters.setPrompt("test");
//        genParameters.setCfg(7.5);
//
//        String result = invokePrivateMethod("buildMidjourneyPrompt", genParameters);
//
//        assert result.contains("--stylize");
//        System.out.println("✅ CFG转换测试通过: " + result);
//
//        // 测试CFG转换函数
//        int stylize1 = invokePrivateMethod("convertCfgToStylize", 1.0);
//        assert stylize1 == 0;
//
//        int stylize2 = invokePrivateMethod("convertCfgToStylize", 30.0);
//        assert stylize2 == 1000;
//
//        int stylize3 = invokePrivateMethod("convertCfgToStylize", 7.5);
//        System.out.println("CFG 7.5 转换为 stylize: " + stylize3);
//    }
//
//    /**
//     * 测试复杂参数组合
//     */
//    @Test
//    void testComplexParameterCombination() throws Exception {
//        GenGenericPara genParameters = new GenGenericPara();
//        genParameters.setPrompt("a majestic dragon flying in the sky");
//        genParameters.setNegative_prompt("blurry, low quality, cartoon");
//        genParameters.setSeed(42L);
//        genParameters.setCfg(12.0);
//
//        Resolution resolution = new Resolution();
//        resolution.setWidth(1344);
//        resolution.setHeight(768);
//        genParameters.setResolution(resolution);
//
//        String result = invokePrivateMethod("buildMidjourneyPrompt", genParameters);
//
//        System.out.println("✅ 复杂参数组合测试结果: " + result);
//
//        // 验证包含所有参数
//        assert result.contains("a majestic dragon flying in the sky");
//        assert result.contains("--ar 16:9");
//        assert result.contains("--no blurry, low quality, cartoon");
//        assert result.contains("--seed 42");
//        assert result.contains("--stylize");
//    }
//
//    /**
//     * 测试宽高比计算（使用AspectRatioUtils）
//     */
//    @Test
//    void testAspectRatioCalculation() throws Exception {
//        // 测试标准SHAPE_ALL配置中的比例
//        String ratio1 = invokePrivateMethod("calculateAspectRatio", 1024, 1024);
//        assert "1:1".equals(ratio1);
//
//        String ratio2 = invokePrivateMethod("calculateAspectRatio", 1344, 768);
//        assert "16:9".equals(ratio2);
//
//        String ratio3 = invokePrivateMethod("calculateAspectRatio", 768, 1344);
//        assert "9:16".equals(ratio3);
//
//        String ratio4 = invokePrivateMethod("calculateAspectRatio", 1152, 896);
//        assert "4:3".equals(ratio4);
//
//        String ratio5 = invokePrivateMethod("calculateAspectRatio", 1216, 832);
//        assert "3:2".equals(ratio5);
//
//        String ratio6 = invokePrivateMethod("calculateAspectRatio", 1536, 640);
//        assert "21:9".equals(ratio6);
//
//        // 测试非标准比例（会计算最简比例）
//        String ratio7 = invokePrivateMethod("calculateAspectRatio", 1920, 1080);
//        assert "16:9".equals(ratio7); // 1920:1080 = 16:9
//
//        String ratio8 = invokePrivateMethod("calculateAspectRatio", 1200, 900);
//        assert "4:3".equals(ratio8); // 1200:900 = 4:3
//
//        System.out.println("✅ 宽高比计算测试通过（使用AspectRatioUtils）");
//        System.out.println("1024x1024 -> " + ratio1);
//        System.out.println("1344x768 -> " + ratio2);
//        System.out.println("768x1344 -> " + ratio3);
//        System.out.println("1152x896 -> " + ratio4);
//        System.out.println("1216x832 -> " + ratio5);
//        System.out.println("1536x640 -> " + ratio6);
//        System.out.println("1920x1080 -> " + ratio7);
//        System.out.println("1200x900 -> " + ratio8);
//    }
//
//    /**
//     * 测试生成模式确定
//     */
//    @Test
//    void testModeDetermination() throws Exception {
//        GenGenericPara genParameters1 = new GenGenericPara();
//        genParameters1.setGen_mode("fast");
//        String mode1 = invokePrivateMethod("determineMidjourneyMode", genParameters1);
//        assert "fast".equals(mode1);
//
//        GenGenericPara genParameters2 = new GenGenericPara();
//        genParameters2.setGen_mode("quality");
//        String mode2 = invokePrivateMethod("determineMidjourneyMode", genParameters2);
//        assert "relax".equals(mode2);
//
//        GenGenericPara genParameters3 = new GenGenericPara();
//        String mode3 = invokePrivateMethod("determineMidjourneyMode", genParameters3);
//        assert "fast".equals(mode3);
//
//        System.out.println("✅ 生成模式确定测试通过");
//        System.out.println("fast -> " + mode1);
//        System.out.println("quality -> " + mode2);
//        System.out.println("null -> " + mode3);
//    }
//
//    /**
//     * 测试最大公约数计算
//     */
//    @Test
//    void testGcdCalculation() throws Exception {
//        int gcd1 = invokePrivateMethod("gcd", 1920, 1080);
//        assert gcd1 == 120;
//
//        int gcd2 = invokePrivateMethod("gcd", 1024, 1024);
//        assert gcd2 == 1024;
//
//        int gcd3 = invokePrivateMethod("gcd", 1344, 768);
//        assert gcd3 == 192;
//
//        System.out.println("✅ 最大公约数计算测试通过");
//        System.out.println("gcd(1920, 1080) = " + gcd1);
//        System.out.println("gcd(1024, 1024) = " + gcd2);
//        System.out.println("gcd(1344, 768) = " + gcd3);
//    }
//
//    /**
//     * 调用私有方法的辅助函数
//     */
//    @SuppressWarnings("unchecked")
//    private <T> T invokePrivateMethod(String methodName, Object... args) throws Exception {
//        Class<?>[] paramTypes = new Class[args.length];
//        for (int i = 0; i < args.length; i++) {
//            if (args[i] instanceof Integer) {
//                paramTypes[i] = int.class;
//            } else if (args[i] instanceof Double) {
//                paramTypes[i] = double.class;
//            } else {
//                paramTypes[i] = args[i].getClass();
//            }
//        }
//
//        Method method = MidjourneyController.class.getDeclaredMethod(methodName, paramTypes);
//        method.setAccessible(true);
//        return (T) method.invoke(controller, args);
//    }
//}
