# Flux Kontext Pro API 集成文档

本文档描述了如何在PicLumen项目中集成Black Forest Labs的Flux Kontext Pro API。

## 概述

Flux Kontext Pro是一个先进的AI图像编辑模型，提供以下功能：
- **文本生图** (Text-to-Image)：从文本描述直接生成图像
- **图像编辑** (Image Editing)：基于文本指令编辑现有图像
- **精确局部修改**：可以精确修改图像的特定部分
- **文本编辑**：直接编辑图像中的文字内容
- **风格转换**：支持各种艺术风格转换

## 配置

### 1. 配置文件设置

在 `application.properties` 中添加以下配置：

```properties
# Flux Kontext Pro API配置
flux.kontext.api.base-url=https://api.bfl.ai
flux.kontext.api.api-key=YOUR_BFL_API_KEY_HERE
flux.kontext.api.callback-url=http://your-domain.com/api/flux-kontext/callback/webhook
flux.kontext.api.default-timeout=300
flux.kontext.api.max-retries=3
flux.kontext.api.default-safety-tolerance=2
flux.kontext.api.default-output-format=jpeg
flux.kontext.api.prompt-check-enabled=true
flux.kontext.api.polling-interval=2000
flux.kontext.api.max-polling-attempts=150
flux.kontext.api.translation-enabled=false
```

### 2. 获取API密钥

1. 访问 [Black Forest Labs Dashboard](https://dashboard.bfl.ai/)
2. 注册账户并获取API密钥
3. 将API密钥替换配置文件中的 `YOUR_BFL_API_KEY_HERE`

## API接口

### 1. 文本生图

**接口**: `POST /api/flux-kontext/text-to-image`

**请求体**:
```json
{
  "prompt": "a beautiful sunset over the ocean",
  "aspectRatio": "16:9",
  "seed": 42,
  "enablePromptCheck": true
}
```

**响应**:
```json
{
  "status": 2000,
  "message": "success",
  "data": {
    "id": "request-id-12345",
    "pollingUrl": "https://api.bfl.ai/v1/get_result?id=request-id-12345"
  }
}
```

### 2. 图像编辑

**接口**: `POST /api/flux-kontext/image-edit`

**请求体**:
```json
{
  "prompt": "change the car color to red",
  "inputImage": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...",
  "aspectRatio": "1:1",
  "seed": 42,
  "enablePromptCheck": true
}
```

**响应**:
```json
{
  "status": 2000,
  "message": "success",
  "data": {
    "id": "request-id-12345",
    "pollingUrl": "https://api.bfl.ai/v1/get_result?id=request-id-12345"
  }
}
```

### 3. 兼容接口

**接口**: `POST /api/flux-kontext/create`

兼容原有的GenController接口格式，支持GenGenericPara参数。

### 4. 获取任务状态

**接口**: `GET /api/flux-kontext/status/{requestId}`

**响应**:
```json
{
  "status": 2000,
  "message": "success",
  "data": {
    "id": "request-id-12345",
    "status": "Ready",
    "result": {
      "sample": "https://signed-url-to-image.jpg",
      "width": 1024,
      "height": 1024,
      "seed": 42,
      "processingTime": 15.5
    }
  }
}
```

## 任务状态

- `Queued`: 排队中
- `Processing`: 处理中
- `Ready`: 完成
- `Error`: 失败

## 异步处理流程

1. **任务提交**: 调用API提交任务，返回任务ID
2. **状态轮询**: 系统每2秒轮询一次任务状态
3. **结果处理**: 任务完成后自动下载图像并保存到数据库
4. **通知用户**: 通过WebSocket通知前端任务完成

## 数据库集成

### PromptRecord表

新增的记录会设置：
- `originCreate`: "fluxKontextTextToImage" 或 "fluxKontextImageEdit"
- `promptParams`: 存储Flux Kontext特定参数
- `genInfo`: 存储完整的请求信息

### PromptFile表

生成的图像会保存到此表，包含：
- 图像URL
- 图像尺寸
- 创建时间等信息

## 支持的宽高比

Flux Kontext支持从3:7到7:3的宽高比，常用比例：
- `1:1` - 正方形 (1024x1024)
- `16:9` - 宽屏
- `9:16` - 竖屏
- `4:3` - 标准
- `3:4` - 竖版标准

## 错误处理

系统实现了完整的错误处理机制：
- API调用失败自动重试
- 轮询超时处理
- 数据库回滚
- 用户友好的错误提示

## 使用示例

### 基本文本生图

```bash
curl -X POST "http://localhost:48080/api/flux-kontext/text-to-image" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "a beautiful landscape with mountains and lake",
    "aspectRatio": "16:9"
  }'
```

### 图像编辑

```bash
curl -X POST "http://localhost:48080/api/flux-kontext/image-edit" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "change the sky to sunset colors",
    "inputImage": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...",
    "aspectRatio": "1:1"
  }'
```

## 注意事项

1. **API密钥安全**: 请妥善保管API密钥，不要在客户端代码中暴露
2. **图像格式**: 输入图像需要转换为Base64格式
3. **结果有效期**: API返回的图像URL有效期为10分钟，系统会自动下载保存
4. **并发限制**: 每个用户最多同时处理10个任务
5. **费用控制**: Flux Kontext Pro是付费服务，请合理控制使用量

## 故障排除

### 常见问题

1. **API密钥无效**: 检查配置文件中的API密钥是否正确
2. **网络连接问题**: 确保服务器可以访问api.bfl.ai
3. **图像格式错误**: 确保输入图像是有效的Base64格式
4. **任务超时**: 检查轮询配置和网络状况

### 日志查看

系统会记录详细的日志信息，可以通过以下方式查看：
- 应用日志: 查看FluxKontextService和FluxKontextCallbackService的日志
- API调用日志: 查看Retrofit的HTTP请求日志
- 数据库日志: 查看PromptRecord和PromptFile的操作日志

## 性能优化

1. **缓存机制**: 可以考虑对相同prompt的结果进行缓存
2. **批量处理**: 对于大量任务，可以实现批量提交和处理
3. **负载均衡**: 在高并发场景下，可以考虑多实例部署
4. **监控告警**: 建议添加API调用成功率和响应时间的监控
