-- 用户支付记录表
CREATE TABLE `user_pay_record`
(
    `id`              bigint       NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `login_name`      varchar(255) NOT NULL COMMENT '登录名称',
    `user_id`         bigint       NOT NULL COMMENT '用户ID',
    `platform`        tinyint      NOT NULL DEFAULT 0 COMMENT '平台类型：0 backend, 1 stripe, 2 paypal, 3 apple, 4 google',
    `source`          varchar(255)          DEFAULT NULL COMMENT '来源',
    `detail`          varchar(255)          DEFAULT NULL COMMENT '详情描述，例如：Text to image',
    `coupon_code`     varchar(255)          DEFAULT NULL COMMENT '优惠码',
    `percent_off`     int                   DEFAULT NULL COMMENT '折扣百分比',
    `amount`          varchar(255)         DEFAULT NULL COMMENT '用户购买价格',
    `discount_amount` varchar(255)         DEFAULT NULL COMMENT '折扣总价',
    `create_by`       varchar(80)           DEFAULT NULL COMMENT '创建者',
    `create_time`     datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by`       varchar(80)           DEFAULT NULL COMMENT '更新者',
    `update_time`     datetime              DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB  DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT ='用户支付记录表';

-- 用户支付记录详情表
CREATE TABLE `user_pay_record_item`
(
    `id`             bigint       NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `login_name`     varchar(255) NOT NULL COMMENT '登录名称',
    `user_id`        bigint       NOT NULL COMMENT '用户ID',
    `record_id`      bigint       NOT NULL COMMENT '支付记录ID',
    `platform`       tinyint      NOT NULL DEFAULT 0 COMMENT '平台类型：0 backend, 1 stripe, 2 paypal, 3 apple, 4 google',
    `product_type`   varchar(50)           DEFAULT NULL COMMENT '产品类型：plan 计划, one 购买lumen',
    `plan_level`     varchar(50)           DEFAULT NULL COMMENT '计划等级：standard/pro',
    `price_interval` varchar(20)           DEFAULT NULL COMMENT '价格间隔：month/year',
    `percent_off`    int                   DEFAULT NULL COMMENT '折扣百分比',
    `unit_lumen`     int                   DEFAULT NULL COMMENT '单位Lumen数量',
    `total_lumen`    int                   DEFAULT NULL COMMENT '总Lumen数量',
    `unit_amount`    varchar(255)         DEFAULT NULL COMMENT '单位金额',
    `total_amount`   varchar(255)         DEFAULT NULL COMMENT '总金额',
    `qty`            int                   DEFAULT 1 COMMENT '购买数量',
    `create_by`      varchar(80)           DEFAULT NULL COMMENT '创建者',
    `create_time`    datetime              DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by`      varchar(80)           DEFAULT NULL COMMENT '更新者',
    `update_time`    datetime              DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT ='用户支付记录详情表';
