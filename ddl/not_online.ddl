ALTER TABLE pay_apple_product ADD COLUMN status tinyint DEFAULT 1 COMMENT '状态';
ALTER TABLE pay_apple_product ADD COLUMN initial_lumen int DEFAULT 0 COMMENT '状态' after lumen;
INSERT INTO `piclumen`.`pay_apple_product` (`id`, `apple_product_id`, `plan_level`, `lumen`, `initial_lumen`, `product_type`, `price_interval`, `vip_level`, `mark`, `status`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (9, 'basic.lumens.100', 'basic', 100, 50, 'one', NULL, -1, 'v2', 1, '2025-06-06 17:38:54', '2025-06-06 17:38:54', 'admin', 'admin');
INSERT INTO `piclumen`.`pay_apple_product` (`id`, `apple_product_id`, `plan_level`, `lumen`, `initial_lumen`, `product_type`, `price_interval`, `vip_level`, `mark`, `status`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (10, 'basic.lumens.1000', 'basic', 1000, 500, 'one', NULL, -1, 'v2', 1, '2025-06-06 17:38:54', '2025-06-06 17:38:54', 'admin', 'admin');
INSERT INTO `piclumen`.`pay_apple_product` (`id`, `apple_product_id`, `plan_level`, `lumen`, `initial_lumen`, `product_type`, `price_interval`, `vip_level`, `mark`, `status`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (11, 'basic.lumens.10000', 'basic', 10000, 5000, 'one', NULL, -1, 'v2', 1, '2025-06-06 17:38:54', '2025-06-06 17:38:54', 'admin', 'admin');
INSERT INTO `piclumen`.`pay_apple_product` (`id`, `apple_product_id`, `plan_level`, `lumen`, `initial_lumen`, `product_type`, `price_interval`, `vip_level`, `mark`, `status`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (12, 'standard.lumens.100', 'standard', 100, 50, 'one', NULL, -1, 'v2', 1, '2025-06-06 17:38:54', '2025-06-06 17:38:54', 'admin', 'admin');
INSERT INTO `piclumen`.`pay_apple_product` (`id`, `apple_product_id`, `plan_level`, `lumen`, `initial_lumen`, `product_type`, `price_interval`, `vip_level`, `mark`, `status`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (13, 'standard.lumens.1000', 'standard', 1000, 500, 'one', NULL, -1, 'v2', 1, '2025-06-06 17:38:54', '2025-06-06 17:38:54', 'admin', 'admin');
INSERT INTO `piclumen`.`pay_apple_product` (`id`, `apple_product_id`, `plan_level`, `lumen`, `initial_lumen`, `product_type`, `price_interval`, `vip_level`, `mark`, `status`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (14, 'standard.lumens.10000', 'standard', 10000, 5000, 'one', NULL, -1, 'v2', 1, '2025-06-06 17:38:54', '2025-06-06 17:38:54', 'admin', 'admin');
INSERT INTO `piclumen`.`pay_apple_product` (`id`, `apple_product_id`, `plan_level`, `lumen`, `initial_lumen`, `product_type`, `price_interval`, `vip_level`, `mark`, `status`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (15, 'pro.lumens.100', 'pro', 100, 50, 'one', NULL, -1, 'v2', 1, '2025-06-06 17:38:54', '2025-06-06 17:38:54', 'admin', 'admin');
INSERT INTO `piclumen`.`pay_apple_product` (`id`, `apple_product_id`, `plan_level`, `lumen`, `initial_lumen`, `product_type`, `price_interval`, `vip_level`, `mark`, `status`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (16, 'pro.lumens.1000', 'pro', 1000, 500, 'one', NULL, -1, 'v2', 1, '2025-06-06 17:38:54', '2025-06-06 17:38:54', 'admin', 'admin');
INSERT INTO `piclumen`.`pay_apple_product` (`id`, `apple_product_id`, `plan_level`, `lumen`, `initial_lumen`, `product_type`, `price_interval`, `vip_level`, `mark`, `status`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (17, 'pro.lumens.10000', 'pro', 10000, 5000, 'one', NULL, -1, 'v2', 1, '2025-06-06 17:38:54', '2025-06-06 17:38:54', 'admin', 'admin');
