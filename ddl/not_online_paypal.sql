CREATE TABLE `piclumen`.`paypal_coupon`
(
    `id`             bigint       NOT NULL COMMENT '主键ID',
    `type`           int          NOT NULL COMMENT '优惠类型(1-sub,2-lumen)',
    `plan_level`     varchar(255) NULL DEFAULT NULL,
    `price_interval` varchar(255) NULL DEFAULT NULL,
    `duration`       varchar(16)  NOT NULL COMMENT '有效期类型(forever/once)',
    `percent_off`    int          NULL DEFAULT 0 COMMENT '折扣百分比(如20代表八折)',
    `valid`          tinyint(1)   NULL DEFAULT 1 COMMENT '是否有效(0无效,1有效)',
    `mark`           varchar(255) NULL DEFAULT NULL COMMENT '备注',
    `paypal_plan_id` varchar(64)  NULL DEFAULT NULL COMMENT 'Paypal计划ID',
    `create_time`           datetime     NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by`             varchar(255) NULL DEFAULT NULL COMMENT '创建人',
    `update_time`           datetime     NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by`             varchar(255) NULL DEFAULT NULL COMMENT '更新人'
);

CREATE TABLE `piclumen`.`paypal_downgrade_log`
(
    `id`                  bigint       NOT NULL COMMENT '主键',
    `type`                varchar(255) NULL DEFAULT NULL COMMENT 'immediate/next_billing_period',
    `src_subscription_id` varchar(255) NULL DEFAULT NULL COMMENT '源订阅ID',
    `new_subscription_id` varchar(255) NULL DEFAULT NULL COMMENT '新订阅ID',
    `src_plan_id`         varchar(255) NULL DEFAULT NULL COMMENT '源计划ID',
    `new_plan_id`         varchar(255) NULL DEFAULT NULL COMMENT '新计划ID',
    `refund_amount`       varchar(255) NULL DEFAULT NULL COMMENT '退款金额',
    `create_time`           datetime     NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by`             varchar(255) NULL DEFAULT NULL COMMENT '创建人',
    `update_time`           datetime     NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by`             varchar(255) NULL DEFAULT NULL COMMENT '更新人'
);

CREATE TABLE `piclumen`.`paypal_event_log`
(
    `id`                  bigint       NOT NULL COMMENT 'PayPal 事件日志的唯一 ID',
    `webhook_id`          varchar(255) NOT NULL COMMENT '事件ID',
    `verified`            tinyint(1)   NULL DEFAULT NULL,
    `webhook_create_time` varchar(255) NULL DEFAULT NULL COMMENT '事件创建时间',
    `resource_type`       varchar(255) NULL DEFAULT NULL COMMENT '资源类型，例如 checkout-order, payment-sale',
    `resource_version`    varchar(255) NULL DEFAULT NULL COMMENT '资源版本号',
    `event_type`          varchar(255) NULL DEFAULT NULL COMMENT '事件类型，例如 CHECKOUT.ORDER.APPROVED, PAYMENT.SALE.REFUNDED',
    `summary`             text         NULL COMMENT '事件摘要信息',
    `status`              varchar(255) NULL DEFAULT NULL COMMENT '事件状态，例如 SUCCESS, FAILURE',
    `error_message`       longtext     NULL,
    `process_status`      varchar(255) NULL DEFAULT NULL,
    `resource`            text         NULL COMMENT '存储完整的 JSON 资源数据',
    `event_version`       varchar(255) NULL DEFAULT NULL COMMENT '事件的版本号',
    `create_time`           datetime     NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by`             varchar(255) NULL DEFAULT NULL COMMENT '创建人',
    `update_time`           datetime     NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by`             varchar(255) NULL DEFAULT NULL COMMENT '更新人'
);

CREATE TABLE `piclumen`.`paypal_link`
(
    `id`          bigint        NOT NULL AUTO_INCREMENT COMMENT '唯一标识 ID',
    `refer_id`    bigint        NULL DEFAULT NULL COMMENT '关联的 PayPal 事件 ID',
    `href`        varchar(1024) NULL DEFAULT NULL COMMENT '链接地址',
    `rel`         varchar(255)  NULL DEFAULT NULL COMMENT '链接的关系类型，例如 self, capture, update',
    `method`      varchar(10)   NULL DEFAULT NULL COMMENT '请求方法，例如 GET, POST, PATCH',
    `create_time`           datetime     NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by`             varchar(255) NULL DEFAULT NULL COMMENT '创建人',
    `update_time`           datetime     NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by`             varchar(255) NULL DEFAULT NULL COMMENT '更新人'
);

CREATE TABLE `piclumen`.`paypal_logic_order`
(
    `id`            bigint       NOT NULL COMMENT '主键ID',
    `user_id`       bigint       NOT NULL COMMENT '用户ID',
    `login_name`    varchar(255) NULL DEFAULT NULL COMMENT '登录用户名',
    `status`        varchar(50)  NULL DEFAULT NULL COMMENT '订单状态',
    `reference_id`  varchar(255) NULL DEFAULT NULL COMMENT '参考ID',
    `order_id`      varchar(255) NULL DEFAULT NULL COMMENT 'PayPal订单ID',
    `amount`        varchar(255) NULL DEFAULT NULL COMMENT '订单金额',
    `currency`      varchar(10)  NULL DEFAULT NULL COMMENT '货币类型',
    `payer_id`      varchar(255) NULL DEFAULT NULL COMMENT '付款人PayPal ID',
    `payer_email`   varchar(255) NULL DEFAULT NULL COMMENT '付款人邮箱',
    `payer_country` varchar(100) NULL DEFAULT NULL COMMENT '付款人国家',
    `create_time`           datetime     NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by`             varchar(255) NULL DEFAULT NULL COMMENT '创建人',
    `update_time`           datetime     NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by`             varchar(255) NULL DEFAULT NULL COMMENT '更新人'
);

CREATE TABLE `piclumen`.`paypal_logic_subscription`
(
    `id`                     bigint       NOT NULL COMMENT '主键ID',
    `user_id`                bigint       NOT NULL COMMENT '用户ID',
    `login_name`             varchar(255) NULL DEFAULT NULL COMMENT '登录用户名',
    `status`                 varchar(50)  NULL DEFAULT NULL COMMENT '订阅状态',
    `quantity`               int          NULL DEFAULT NULL,
    `subscription_id`        varchar(255) NULL DEFAULT NULL COMMENT 'PayPal订阅ID',
    `plan_id`                varchar(255) NULL DEFAULT NULL COMMENT '订阅计划ID',
    `payment_id`             varchar(255) NULL DEFAULT NULL,
    `start_utc_sec`          bigint       NULL DEFAULT NULL COMMENT '订阅开始时间（秒级时间戳）',
    `sub_start_sec`          bigint       NULL DEFAULT NULL,
    `next_billing_sec`       bigint       NULL DEFAULT NULL COMMENT '下次计费时间（秒级时间戳）',
    `start_time`             varchar(255) NULL DEFAULT NULL COMMENT '订阅开始时间',
    `next_billing_time`      varchar(255) NULL DEFAULT NULL COMMENT '下次计费时间',
    `last_payment_time`      varchar(255) NULL DEFAULT NULL COMMENT '上次支付时间',
    `outstanding_value`      varchar(255) NULL DEFAULT NULL,
    `last_payment_value`     varchar(255) NULL DEFAULT NULL COMMENT '上次支付金额',
    `status_update_time_sec` bigint       NULL DEFAULT NULL COMMENT '状态更新时间（秒级时间戳）',
    `subscriber_email`       varchar(255) NULL DEFAULT NULL COMMENT '订阅者邮箱',
    `subscriber_payer_id`    varchar(255) NULL DEFAULT NULL COMMENT '订阅者PayPal Payer ID',
    `invalid`                tinyint(1)   NULL DEFAULT NULL COMMENT '是否无效',
    `create_time`           datetime     NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by`             varchar(255) NULL DEFAULT NULL COMMENT '创建人',
    `update_time`           datetime     NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by`             varchar(255) NULL DEFAULT NULL COMMENT '更新人'
);

CREATE TABLE `piclumen`.`paypal_order_payment_item`
(
    `id`              bigint       NOT NULL COMMENT '主键ID',
    `user_id`         bigint       NULL DEFAULT NULL COMMENT '用户ID',
    `login_name`      varchar(255) NULL DEFAULT NULL COMMENT '登录用户名',
    `order_id`        varchar(255) NULL DEFAULT NULL COMMENT '关联的PayPal订单ID',
    `payment_id`      varchar(255) NULL DEFAULT NULL COMMENT '支付交易ID',
    `product_id`      varchar(255) NULL DEFAULT NULL COMMENT '产品ID',
    `qty`             int          NULL DEFAULT NULL,
    `status`          varchar(50)  NULL DEFAULT NULL COMMENT '支付状态',
    `amount`          varchar(255) NULL DEFAULT NULL COMMENT '支付金额',
    `discount_amount` varchar(255) NULL DEFAULT NULL,
    `currency`        varchar(10)  NULL DEFAULT NULL COMMENT '货币类型',
    `create_time`           datetime     NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by`             varchar(255) NULL DEFAULT NULL COMMENT '创建人',
    `update_time`           datetime     NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by`             varchar(255) NULL DEFAULT NULL COMMENT '更新人'
);

CREATE TABLE `piclumen`.`paypal_order_payment_record`
(
    `id`              bigint         NOT NULL COMMENT '主键ID',
    `user_id`         bigint         NULL DEFAULT NULL COMMENT '用户ID',
    `login_name`      varchar(255)   NULL DEFAULT NULL COMMENT '登录用户名',
    `order_id`        varchar(255)   NULL DEFAULT NULL COMMENT '关联的PayPal订单ID',
    `payment_id`      varchar(255)   NULL DEFAULT NULL COMMENT '支付交易ID',
    `status`          varchar(50)    NULL DEFAULT NULL COMMENT '支付状态',
    `amount`          varchar(255)   NULL DEFAULT NULL COMMENT '支付金额',
    `discount_amount` varchar(255)   NULL DEFAULT NULL,
    `currency`        varchar(10)    NULL DEFAULT NULL COMMENT '货币类型',
    `fee`             decimal(10, 2) NULL DEFAULT NULL COMMENT '手续费',
    `net_amount`      varchar(255)   NULL DEFAULT NULL COMMENT '实际到账金额',
    `refund_id`       bigint         NULL DEFAULT NULL COMMENT '退款ID（表示已退款）',
    `reference_id`    varchar(255)   NULL DEFAULT NULL,
    `pay_create_time` varchar(255)   NULL DEFAULT NULL,
    `payee_id`        varchar(255)   NULL DEFAULT NULL,
    `payee_email`     varchar(255)   NULL DEFAULT NULL,
    `create_time`           datetime     NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by`             varchar(255) NULL DEFAULT NULL COMMENT '创建人',
    `update_time`           datetime     NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by`             varchar(255) NULL DEFAULT NULL COMMENT '更新人'
);

CREATE TABLE `piclumen`.`paypal_product`
(
    `id`             bigint       NOT NULL COMMENT 'ID',
    `paypal_plan_id` varchar(100) NOT NULL COMMENT 'paypal的planid或者',
    `plan_level`     varchar(100) NULL DEFAULT NULL COMMENT 'standard，pro',
    `lumen`          int          NULL DEFAULT NULL COMMENT 'lumen',
    `initial_lumen`  int          NULL DEFAULT NULL,
    `product_type`   varchar(100) NOT NULL COMMENT 'plan, one',
    `price_interval` varchar(20)  NULL DEFAULT NULL COMMENT 'year, month',
    `first_buy_sub`  int          NULL DEFAULT 0,
    `product_id`     varchar(255) NULL DEFAULT NULL,
    `source_price`   varchar(255) NULL DEFAULT NULL,
    `price`          varchar(10)  NULL DEFAULT NULL COMMENT '价格',
    `vip_level`      int          NULL DEFAULT NULL COMMENT 'standard month 1-->pro year  4',
    `mark`           varchar(255) NULL DEFAULT NULL COMMENT '商品描述信息',
    `status`         tinyint(1)   NULL DEFAULT NULL,
    `create_time`           datetime     NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by`             varchar(255) NULL DEFAULT NULL COMMENT '创建人',
    `update_time`           datetime     NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by`             varchar(255) NULL DEFAULT NULL COMMENT '更新人'
);

CREATE TABLE `piclumen`.`paypal_refund_record`
(
    `id`                    bigint       NOT NULL COMMENT '主键ID',
    `refund_id`             varchar(255) NULL DEFAULT NULL COMMENT '退款ID',
    `payment_id`            varchar(255) NULL DEFAULT NULL COMMENT '关联的支付交易ID',
    `status`                varchar(50)  NULL DEFAULT NULL COMMENT '退款状态',
    `refund_reason_code`    varchar(255) NULL DEFAULT NULL COMMENT '退款原因代码',
    `sale_id`               varchar(255) NULL DEFAULT NULL COMMENT '销售ID（payment_id）',
    `currency`              varchar(255) NULL DEFAULT NULL COMMENT '货币类型',
    `total`                 varchar(255) NULL DEFAULT NULL COMMENT '退款总金额',
    `net_amount`            varchar(255) NULL DEFAULT NULL COMMENT '退款到账金额',
    `gross_amount`          varchar(255) NULL DEFAULT NULL COMMENT '从收到的金额中退款',
    `paypal_fee`            varchar(255) NULL DEFAULT NULL COMMENT '从交易费中退款',
    `total_refunded_amount` varchar(255) NULL DEFAULT NULL COMMENT '已退款总金额',
    `note_to_payer`         text         NULL,
    `link`                  text         NULL,
    `create_time`           datetime     NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by`             varchar(255) NULL DEFAULT NULL COMMENT '创建人',
    `update_time`           datetime     NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by`             varchar(255) NULL DEFAULT NULL COMMENT '更新人'
);

CREATE TABLE `piclumen`.`paypal_sub_payment_record`
(
    `id`                  bigint       NOT NULL COMMENT '主键ID',
    `user_id`             bigint       NULL DEFAULT NULL COMMENT '用户ID',
    `login_name`          varchar(255) NULL DEFAULT NULL COMMENT '登录用户名',
    `subscription_id`     varchar(255) NULL DEFAULT NULL COMMENT '订阅ID',
    `paypal_logic_sub_id` bigint       NULL DEFAULT NULL COMMENT '关联的PayPal逻辑订阅ID',
    `payment_id`          varchar(255) NULL DEFAULT NULL COMMENT '支付交易ID',
    `state`               varchar(50)  NULL DEFAULT NULL COMMENT '支付状态',
    `total`               varchar(255) NULL DEFAULT NULL COMMENT '支付总金额',
    `subtotal`            varchar(255) NULL DEFAULT NULL COMMENT '支付小计',
    `currency`            varchar(10)  NULL DEFAULT NULL COMMENT '货币类型',
    `fee`                 varchar(255) NULL DEFAULT NULL COMMENT '手续费',
    `refund_id`           varchar(255) NULL DEFAULT NULL COMMENT '退款ID（表示已退款）',
    `pay_create_time`     varchar(255) NULL DEFAULT NULL,
    `create_time`           datetime     NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by`             varchar(255) NULL DEFAULT NULL COMMENT '创建人',
    `update_time`           datetime     NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by`             varchar(255) NULL DEFAULT NULL COMMENT '更新人'
);

CREATE TABLE `piclumen`.`paypal_sub_revise_log`
(
    `id`                    bigint       NOT NULL COMMENT '主键ID',
    `user_id`               bigint       NOT NULL COMMENT '用户ID',
    `login_name`            varchar(255) NULL DEFAULT NULL COMMENT '登录用户名',
    `status`                varchar(50)  NULL DEFAULT NULL COMMENT '修改状态',
    `subscription_id`       varchar(255) NOT NULL COMMENT '订阅ID',
    `src_plan_id`           varchar(255) NULL DEFAULT NULL COMMENT '原始订阅计划ID',
    `new_plan_id`           varchar(255) NULL DEFAULT NULL COMMENT '新订阅计划ID',
    `next_billing_time_sec` bigint       NULL DEFAULT NULL,
    `type`                  varchar(50)  NULL DEFAULT NULL COMMENT '修改类型（升级/降级）',
    `create_time`           datetime     NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by`             varchar(255) NULL DEFAULT NULL COMMENT '创建人',
    `update_time`           datetime     NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by`             varchar(255) NULL DEFAULT NULL COMMENT '更新人'
);

CREATE TABLE `piclumen`.`paypal_upgrade_log`
(
    `id`                  bigint       NOT NULL COMMENT '主键',
    `type`                varchar(255) NULL DEFAULT NULL COMMENT 'immediate/next_billing_period',
    `user_id`             bigint       NULL DEFAULT NULL,
    `login_name`          varchar(255) NULL DEFAULT NULL,
    `src_subscription_id` varchar(255) NULL DEFAULT NULL COMMENT '源订阅ID',
    `src_sub_status`      varchar(255) NULL DEFAULT NULL,
    `new_subscription_id` varchar(255) NULL DEFAULT NULL COMMENT '新订阅ID',
    `new_sub_status`      varchar(255) NULL DEFAULT NULL,
    `new_sub_active_time` bigint       NULL DEFAULT NULL,
    `src_plan_id`         varchar(255) NULL DEFAULT NULL COMMENT '源计划ID',
    `new_plan_id`         varchar(255) NULL DEFAULT NULL COMMENT '新计划ID',
    `has_refund`          tinyint(1)   NULL DEFAULT NULL COMMENT '是否有退款',
    `vip_revert`          tinyint(1)   NULL DEFAULT NULL,
    `refund_amount`       varchar(255) NULL DEFAULT NULL COMMENT '退款金额',
    `refund_id`           varchar(255) NULL DEFAULT NULL COMMENT '退款ID',
    `create_time`           datetime     NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by`             varchar(255) NULL DEFAULT NULL COMMENT '创建人',
    `update_time`           datetime     NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by`             varchar(255) NULL DEFAULT NULL COMMENT '更新人'
);


ALTER TABLE `piclumen`.`stripe_product` ADD COLUMN `first_buy_sub_discount` int NULL DEFAULT 0 AFTER `initial_lumen`;

ALTER TABLE `piclumen`.`stripe_product` ADD COLUMN `price` varchar(255) NULL DEFAULT NULL COMMENT '价格' AFTER `price_interval`;

ALTER TABLE `piclumen`.`stripe_product` MODIFY COLUMN `initial_lumen` int NULL DEFAULT NULL COMMENT '试用赠送lumen数' AFTER `trial_day`;

ALTER TABLE `piclumen`.`subscription_current` ADD COLUMN `renew_price` varchar(255) NULL DEFAULT NULL COMMENT '价格' AFTER `price_interval`;