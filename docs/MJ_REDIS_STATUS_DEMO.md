# MJ Redis状态管理演示

## 演示场景

假设用户发起一个MJ生图请求，然后前端轮询状态的完整流程。

## 1. 生图请求阶段

### 用户发起请求
```http
POST /api/midjourney/imagine
{
    "prompt": "a beautiful cat",
    "markId": "1703123456789012345"
}
```

### 后端处理
```java
// MidjourneyService.imagine()
String jobId = "mj-job-uuid-12345-67890";
String markId = "1703123456789012345";
String loginName = "user123";

// 调用MJ API成功后，保存状态到Redis
saveMjTaskStatusToRedis(jobId, markId, loginName);
```

### Redis状态初始化
```redis
# 1. jobId -> markId映射
SET "mj-job-uuid-12345-67890" "1703123456789012345" EX 7200

# 2. markId -> loginName映射  
SET "1703123456789012345" "user123" EX 7200

# 3. 用户hash中的任务状态（-1表示新建）
HSET "user123" "1703123456789012345" -1

# 4. 任务时间戳
SET "USER_TASK_TIMESTAMP1703123456789012345" 1703123456789 EX 7200
```

## 2. 状态轮询阶段（后台线程）

### 第一次轮询 - 排队中
```java
// MJ API返回状态：PENDING_QUEUE
updateMjTaskStatusInRedis(jobId, 1);
```

```redis
# 更新用户hash中的状态（1表示排队）
HSET "user123" "1703123456789012345" 1
```

### 第二次轮询 - 执行中
```java
// MJ API返回状态：ON_QUEUE  
updateMjTaskStatusInRedis(jobId, 0);
```

```redis
# 更新用户hash中的状态（0表示执行中）
HSET "user123" "1703123456789012345" 0
```

### 第三次轮询 - 完成
```java
// MJ API返回状态：SUCCESS
handleTaskSuccess(jobId, loginName, taskStatus);
// -> processTaskImages() 保存图片到数据库
// -> cleanupTaskFromRedis() 清理Redis状态
```

```redis
# 清理所有相关Redis数据
DEL "mj-job-uuid-12345-67890"
DEL "1703123456789012345" 
HDEL "user123" "1703123456789012345"
DEL "USER_TASK_TIMESTAMP1703123456789012345"
```

## 3. 前端查询阶段

### 前端轮询请求
```javascript
// 前端可以混合查询不同类型的任务
const taskIds = [
    "1703123456789012345",      // 传统markId
    "mj-job-uuid-12345-67890",  // MJ jobId
    "1703123456789012346"       // 另一个传统markId
];

fetch('/api/task/batch-process-task', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(taskIds)
});
```

### 后端处理逻辑

#### 处理第一个ID（传统markId）
```java
// TaskService.processTask("1703123456789012345")
isMidjourneyTask("1703123456789012345") // 检查Redis，返回false
-> processTraditionalTask("1703123456789012345") // 使用传统逻辑
```

#### 处理第二个ID（MJ jobId）
```java
// TaskService.processTask("mj-job-uuid-12345-67890")
isMidjourneyTask("mj-job-uuid-12345-67890") // 检查Redis，返回true
-> processMidjourneyTask("mj-job-uuid-12345-67890") // 使用MJ逻辑

// 在processMidjourneyTask中：
String markId = redisService.stringGet("mj-job-uuid-12345-67890"); // 获取markId
String loginName = redisService.stringGet(markId); // 获取loginName
Integer index = redisService.getDataFromHash(loginName, markId); // 获取状态

// 如果index为null，说明任务已完成，调用dealSuccessOrFailureTask
// 如果index不为null，根据值返回对应状态
```

### 返回结果示例

#### 任务进行中的响应
```json
{
    "code": 200,
    "data": [
        {
            "markId": "1703123456789012345",
            "status": "running",
            "index": 0,
            "prompt": "a beautiful landscape"
        },
        {
            "markId": "1703123456789012345", 
            "promptId": "mj-job-uuid-12345-67890",
            "status": "pending",
            "index": 1,
            "prompt": "a beautiful cat"
        },
        {
            "markId": "1703123456789012346",
            "status": "success",
            "img_urls": [...]
        }
    ]
}
```

#### 任务完成后的响应
```json
{
    "code": 200,
    "data": [
        {
            "markId": "1703123456789012345",
            "promptId": "mj-job-uuid-12345-67890", 
            "status": "success",
            "prompt": "a beautiful cat",
            "featureName": "midjourneyImagine",
            "img_urls": [
                {
                    "imgUrl": "https://cdn.midjourney.com/image1.jpg",
                    "thumbnailUrl": "https://cdn.midjourney.com/thumb1.jpg",
                    "realWidth": 1024,
                    "realHeight": 1024,
                    "imgName": "midjourney_image_1.jpg"
                }
            ]
        }
    ]
}
```

## 4. 关键优势

### 性能优化
- **避免频繁API调用**：前端查询时不调用MJ API，只读取Redis
- **统一状态管理**：MJ任务和传统任务使用相同的Redis结构
- **后台异步更新**：状态轮询在后台进行，不影响前端响应速度

### 一致性保证
- **状态同步**：后台线程确保Redis状态与MJ API状态同步
- **自动清理**：任务完成后自动清理Redis数据
- **错误处理**：网络异常不影响已缓存的状态信息

### 扩展性
- **统一接口**：前端无需区分任务类型
- **混合查询**：可以在同一请求中查询不同类型的任务
- **易于扩展**：为未来支持更多第三方API奠定基础

## 5. 注意事项

1. **Redis数据一致性**：确保状态轮询线程正常运行
2. **超时处理**：长时间未完成的任务需要超时清理
3. **错误恢复**：Redis故障时的降级策略
4. **内存管理**：合理设置Redis过期时间，避免内存泄漏
