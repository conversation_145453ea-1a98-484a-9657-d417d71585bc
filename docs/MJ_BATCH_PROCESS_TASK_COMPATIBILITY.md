# Midjourney API 兼容性实现 - batchProcessTask (Redis状态管理版本)

## 概述

本文档描述了如何为 `batchProcessTask` 方法添加 Midjourney API 兼容性，使其能够同时处理传统的 `markId` 和 Midjourney 的 `jobId`。新实现采用Redis状态管理，避免频繁调用MJ API。

## 背景

原有系统中：
- 传统生图使用 `markId` 进行任务跟踪，状态存储在Redis中
- 前端通过 `batchProcessTask` 轮询任务状态
- Midjourney API 使用 `jobId` 进行任务跟踪
- 需要统一接口支持两种任务类型，且MJ任务也应使用Redis状态管理

## 实现方案

### 1. Redis状态管理架构

#### MJ任务Redis存储结构：
```
jobId -> markId                    # jobId到markId的映射
markId -> loginName               # markId到用户名的映射
loginName -> {markId: index}      # 用户hash中的任务状态
USER_TASK_TIMESTAMP + markId      # 任务时间戳
```

#### 状态值含义：
- `index = -1`: 新建状态
- `index > 0`: 排队中（数值表示队列位置）
- `index = 0`: 执行中
- `index = null`: 任务已完成（成功或失败）

### 2. 核心修改

#### TaskService.java 主要变更：

1. **processTask 方法重构**
   ```java
   public TaskProcessMessage processTask(String taskId) throws JsonProcessingException {
       // 检查是否为MJ任务
       if (isMidjourneyTask(taskId)) {
           return processMidjourneyTask(taskId);
       }

       // 处理传统markId任务
       return processTraditionalTask(taskId);
   }
   ```

2. **任务类型检测**
   ```java
   private boolean isMidjourneyTask(String taskId) {
       // 通过检查Redis中是否存在jobId -> markId映射来判断
       String markId = redisService.stringGet(taskId);
       return StringUtil.isNotBlank(markId);
   }
   ```

3. **MJ任务处理**
   ```java
   private TaskProcessMessage processMidjourneyTask(String jobId) {
       // 从Redis获取状态，参考processTraditionalTask逻辑
       // 不直接调用MJ API，只在成功时查询数据库组装结果
   }
   ```

#### MidjourneyService.java 主要变更：

1. **生图时状态初始化**
   ```java
   private void saveMjTaskStatusToRedis(String jobId, String markId, String loginName) {
       // 保存jobId -> markId映射
       // 保存markId -> loginName映射
       // 设置初始状态为-1（新建）
   }
   ```

2. **状态轮询时更新Redis**
   ```java
   private void updateMjTaskStatusInRedis(String jobId, Integer index) {
       // 根据MJ API返回的状态更新Redis中的index值
   }
   ```

### 3. 状态映射与流程

#### MJ API状态到Redis状态的映射：

| MJ API状态 | Redis Index | 系统状态 | 描述 |
|------------|-------------|----------|------|
| 初始提交 | -1 | newbuilt | 新建状态 |
| PENDING_QUEUE | 1 | pending | 排队中 |
| ON_QUEUE | 0 | running | 执行中 |
| SUCCESS | null | success | 成功完成（从Redis删除） |
| FAILED | null | failure | 失败（从Redis删除） |

#### 完整数据流程：

```
1. MJ生图请求
   ↓
MidjourneyService.imagine()
   ↓
saveMjTaskStatusToRedis() - 初始化Redis状态
   ↓
startTaskStatusPolling() - 启动状态轮询线程

2. 状态轮询（后台线程）
   ↓
定期调用MJ API获取状态
   ↓
updateMjTaskStatusInRedis() - 更新Redis状态
   ↓
任务完成时：cleanupTaskFromRedis() - 清理Redis

3. 前端查询状态
   ↓
batchProcessTask([markId1, jobId1, markId2])
   ↓
TaskService.processTask() - 对每个ID处理
   ↓
isMidjourneyTask() - 检查Redis中的jobId映射
   ↓
processMidjourneyTask() - 从Redis读取状态
   ↓
返回统一格式的 TaskProcessMessage
```

## 关键特性

### 1. 向后兼容
- 完全保持对传统 `markId` 的支持
- 不影响现有功能

### 2. 统一接口
- 前端无需区分任务类型
- 可以在同一个请求中混合查询不同类型的任务

### 3. 实时状态
- MJ任务通过API实时查询状态
- 传统任务保持原有逻辑

### 4. 错误处理
- 优雅处理API调用失败
- 提供详细的错误信息

## 使用示例

### 前端调用示例
```javascript
// 混合查询传统markId和MJ jobId
const taskIds = [
    "1234567890123456789",  // 传统markId
    "mj-job-uuid-12345",    // MJ jobId
    "9876543210987654321"   // 传统markId
];

fetch('/api/task/batch-process-task', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(taskIds)
})
.then(response => response.json())
.then(data => {
    data.data.forEach(task => {
        console.log(`任务 ${task.markId}: ${task.status}`);
        if (task.status === 'success' && task.img_urls) {
            console.log(`生成了 ${task.img_urls.length} 张图片`);
        }
    });
});
```

### 返回数据格式
```json
{
    "code": 200,
    "data": [
        {
            "markId": "1234567890123456789",
            "promptId": "prompt-id-123",
            "status": "success",
            "prompt": "a beautiful landscape",
            "featureName": "create",
            "img_urls": [
                {
                    "imgUrl": "https://example.com/image1.jpg",
                    "thumbnailUrl": "https://example.com/thumb1.jpg",
                    "realWidth": 1024,
                    "realHeight": 1024
                }
            ]
        },
        {
            "markId": "generated-mark-id",
            "promptId": "mj-job-uuid-12345",
            "status": "running",
            "prompt": "a cute cat",
            "featureName": "midjourneyImagine",
            "index": 0
        }
    ]
}
```

## 测试

### 单元测试
- `TaskServiceMjCompatibilityTest.java` 提供了完整的测试用例
- 测试混合ID处理、单独MJ任务处理、传统任务兼容性

### 集成测试
建议在实际环境中测试：
1. 创建MJ任务并获取jobId
2. 使用batchProcessTask查询状态
3. 验证状态转换和数据完整性

## 注意事项

1. **性能考虑**
   - MJ任务需要调用外部API，可能有延迟
   - 建议合理控制批量查询的数量

2. **错误处理**
   - MJ API调用失败时会返回failure状态
   - 网络问题不会影响传统任务查询

3. **数据一致性**
   - MJ任务的markId通过PromptRecord关联
   - 确保数据库中的记录完整性

## 扩展性

该实现为未来支持更多第三方API提供了良好的架构基础：
- 可以轻松添加新的任务类型检测
- 统一的状态转换机制
- 模块化的处理逻辑

## 总结

通过这次实现，系统现在能够：
- 无缝支持MJ API的jobId查询
- 保持对传统markId的完全兼容
- 提供统一的前端接口
- 实现实时状态同步

这为用户提供了更好的体验，同时为系统的未来扩展奠定了基础。
