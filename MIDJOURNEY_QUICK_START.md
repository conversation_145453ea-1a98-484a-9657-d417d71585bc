# Midjourney API 快速开始指南

## 1. 配置步骤

### 1.1 更新配置文件

在 `src/main/resources/application.properties` 中更新以下配置：

```properties
# 替换为您的实际API密钥
midjourney.api.api-key=YOUR_ACTUAL_TT_API_KEY

# 替换为您的实际回调URL（如果需要回调）
midjourney.api.callback-url=http://your-domain.com/api/midjourney/callback/webhook
```

### 1.2 启动项目

```bash
mvn spring-boot:run
```

## 2. 快速测试

### 2.1 访问Swagger文档

打开浏览器访问：
```
http://localhost:48080/swagger-ui/index.html
```

在Swagger中找到 "Midjourney API" 分组。

### 2.2 获取认证Token

首先需要登录获取认证token，在请求头中添加：
```
Authorization: Bearer YOUR_TOKEN
```

### 2.3 测试图像生成

**接口**: `POST /api/midjourney/imagine`

**请求体**:
```json
{
  "prompt": "a beautiful sunset over the ocean",
  "mode": "fast"
}
```

**响应示例**:
```json
{
  "status": "SUCCESS",
  "message": "操作成功",
  "data": {
    "jobId": "12345678-1234-1234-1234-123456789abc"
  }
}
```

### 2.4 查询任务状态

使用上一步返回的jobId查询状态：

**接口**: `GET /api/midjourney/status/{jobId}`

**响应示例**:
```json
{
  "status": "SUCCESS",
  "message": "操作成功",
  "data": {
    "status": "SUCCESS",
    "jobId": "12345678-1234-1234-1234-123456789abc",
    "data": {
      "actions": "imagine",
      "progress": "100",
      "prompt": "a beautiful sunset over the ocean",
      "images": [
        "https://cdnb.ttapi.io/2024-04-02/image1.png",
        "https://cdnb.ttapi.io/2024-04-02/image2.png",
        "https://cdnb.ttapi.io/2024-04-02/image3.png",
        "https://cdnb.ttapi.io/2024-04-02/image4.png"
      ],
      "components": ["upsample1", "upsample2", "upsample3", "upsample4"],
      "width": 1024,
      "height": 1024
    }
  }
}
```

### 2.5 执行操作（放大图像）

使用jobId和操作类型执行U1操作：

**接口**: `POST /api/midjourney/action`

**请求体**:
```json
{
  "jobId": "12345678-1234-1234-1234-123456789abc",
  "action": "upsample1"
}
```

## 3. 使用cURL测试

### 3.1 生成图像

```bash
curl -X POST "http://localhost:48080/api/midjourney/imagine" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "a cute cat sitting on a chair",
    "mode": "fast"
  }'
```

### 3.2 查询状态

```bash
curl -X GET "http://localhost:48080/api/midjourney/status/YOUR_JOB_ID" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 3.3 执行操作

```bash
curl -X POST "http://localhost:48080/api/midjourney/action" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "jobId": "YOUR_JOB_ID",
    "action": "upsample1"
  }'
```

### 3.4 Prompt效验

```bash
curl -X POST "http://localhost:48080/api/midjourney/prompt-check" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "a beautiful landscape"
  }'
```

## 4. 文件上传测试

### 4.1 上传图片并转换为Base64

```bash
curl -X POST "http://localhost:48080/api/midjourney/upload-image" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "file=@/path/to/your/image.jpg"
```

### 4.2 图像合成（文件上传）

```bash
curl -X POST "http://localhost:48080/api/midjourney/blend-files" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "files=@/path/to/image1.jpg" \
  -F "files=@/path/to/image2.jpg" \
  -F "dimensions=SQUARE" \
  -F "mode=fast"
```

### 4.3 图像描述（文件上传）

```bash
curl -X POST "http://localhost:48080/api/midjourney/describe-file" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "file=@/path/to/your/image.jpg" \
  -F "mode=fast"
```

## 5. 常见问题

### 5.1 API密钥错误

如果收到认证失败的错误，请检查：
- API密钥是否正确配置
- API密钥是否有效
- 是否有足够的配额

### 5.2 任务状态查询

任务状态说明：
- `PENDING_QUEUE`: 排队中
- `ON_QUEUE`: 执行中
- `SUCCESS`: 成功
- `FAILED`: 失败

### 5.3 JSON反序列化错误修复

**问题**: 如果遇到类似 `UnrecognizedPropertyException` 的错误，说明API返回的JSON包含未定义的字段。

**解决方案**: 已在所有响应DTO类中添加了 `@JsonIgnoreProperties(ignoreUnknown = true)` 注解，会自动忽略未知字段。

**验证修复**: 运行测试类验证JSON反序列化：
```bash
mvn test -Dtest=MidjourneyResponseTest
```

### 5.4 回调设置

如果需要接收回调通知：
1. 确保回调URL可以被外网访问
2. 配置正确的回调URL
3. 检查防火墙设置

## 6. 支持的操作类型

### 6.1 基本操作
- `upsample1`, `upsample2`, `upsample3`, `upsample4` - U1到U4按钮
- `variation1`, `variation2`, `variation3`, `variation4` - V1到V4按钮

### 6.2 高级操作
- `high_variation` - Vary (Strong)
- `low_variation` - Vary (Subtle)
- `zoom_out_2` - Zoom Out 2x
- `zoom_out_1_5` - Zoom Out 1.5x
- `reroll` - 重新生成

### 6.3 放大操作
- `upscale_creative` - Upscale (Creative)
- `upscale_subtle` - Upscale (Subtle)

## 7. 速度模式

- `fast` - 快速模式（约60秒）
- `relax` - 闲时模式（约120秒）
- `turbo` - 极速模式（约30秒）

## 8. 注意事项

1. **配额管理**: 每个操作都会消耗配额，请合理使用
2. **图片格式**: 支持JPG、PNG、WebP格式
3. **文件大小**: 上传图片不能超过5MB
4. **并发限制**: 根据您的套餐限制并发请求数量
5. **错误处理**: 请妥善处理API调用失败的情况

## 9. 获取帮助

如果遇到问题，请：
1. 查看应用日志
2. 检查API密钥和配置
3. 参考完整文档：`MIDJOURNEY_API_INTEGRATION.md`
4. 访问TT API官方文档：https://docs-zh.mjapiapp.com/api/midjourney-api
